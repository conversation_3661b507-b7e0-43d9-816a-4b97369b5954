The Brightfield Analysis Project is a **priority initiative** within MelliCell, serving as an excellent starting point for a new data scientist. Its core purpose is to **integrate and consolidate existing brightfield image processing pipelines directly into the MellitOS application**, specifically addressing a data gap for Olympus images and shifting the storage of new analysis results from BigQuery to Google Cloud Storage.

Drawing on your conversation with <PERSON>, here's a detailed context:

### <PERSON>'s Vision and Project Goals

Andreas emphasizes that the Brightfield Analysis Project is **not explorative** but has a clear, defined goal: to **replicate the exact same numerical results** as the current Cytosmart brightfield analysis pipeline, ensuring consistency when processing the same images. The aim is to achieve **numerical equivalency**.

Key motivations for this consolidation include:
*   **Centralization of Codebase:** To move away from the current "dozen different repositories" and **centralize all image processing and analysis code within the `MellitOS` repository**. This resolves difficulties in tracking and managing disparate code.
*   **Filling the Olympus Data Gap:** To enable **brightfield analysis for Olympus images**, which currently lack active cell and cluster analysis, leading to a data gap (e.g., when the Cytosmart imager was down). This ensures data availability regardless of the imaging device.
*   **Improved Data Storage:** To change the storage mode for analysis output data from BigQuery to **Cloud Storage (CSV files)**, which is the preferred method for new analysis. BigQuery can still access these CSV files via external tables.
*   **Leveraging MellitOS Framework:** To utilize and build upon the existing MellitOS framework, which offers **abstractions over storage (Repository Pattern)** and integrates with database models and workflow management.

### Legacy Brightfield Pipelines

Before this project, brightfield image processing at MelliCell was handled by separate, distinct pipelines:

*   **Cytosmart Pipeline (Current Source of Brightfield Analysis)**:
    *   **Data Acquisition:** Images are acquired using the Cytosmart Omni imager, which has **custom software installed by Cytosmart that pushes images directly to the `cytosmart_brightfield_images_mellicell` Google Cloud Storage bucket**. Images follow a naming convention like `<experiment_uuid>/<scan_number>/<well_name>/<well_name>export.jpg`, accompanied by a `scan_metadata.json` file containing experiment ID, scan number, experiment name, number of wells, and pixels per mm. Andreas notes he knows "almost nothing" about this initial transfer, only that "it is working".
    *   **Analysis:** This data is currently processed by a **dedicated segmentation pipeline called "BF Cytosmart analysis pipeline"**. This pipeline is deployed as a Docker container (e.g., `gcr.io/development-311316/cytosmart-segment-service-v2:two-tables`), and a cloud function (`create-omni-worker-function`) linked to the GCS bucket triggers this worker when an image is uploaded.
    *   **Data Storage:** The processed images are written to the `mellitos` database (in the `development-311316` project) and also to **two tables in BigQuery**. This BigQuery data forms the basis for much of the QC analysis seen in the MellitOS application.
    *   **Code Location:** The critical aspect for this project is that the code for this Cytosmart brightfield analysis pipeline **resides in separate repositories outside of the main MellitOS repository**.

*   **Olympus Brightfield Gap (Missing Analysis)**:
    *   **Data Acquisition:** Olympus images are initially transferred from a lab PC to the `ix83-raw-files` bucket in the `production-401712` project via a Windows service (`OlympusUploadService`). A pubsub message (`projects/production-401712/topics/ix83-image-uploaded`) is pushed upon upload. Andreas noted a `sync` repository that handles the `rsync`-like transfer of files from the lab PC to Google Cloud Storage for Olympus images, which has a known issue of repeatedly copying some files.
    *   **Image Type Conversion:** These images (VSI format) are then converted to an open TIFF format by the `ImageConvertWorker`, hosted on a Managed Instance Group (MIG). This worker listens to the `convert-ix83-image-sub` pubsub subscription.
    *   **Segmentation & Measurement:** Subsequent steps include segmentation (`ImageSegmentWorker`) and measurement (`MeasureMaskWorker`), also hosted on MIGs. The measurement workers historically wrote directly to BigQuery but have since been changed to write completed data to **CSV files in the `gs://ix83-brightfield-measurments/MCL_CP_005` bucket**, which can be accessed via BigQuery external tables.
    *   **Missing Brightfield Analysis:** Despite these processing steps, **there is currently no active brightfield analysis for cells and clusters performed on Olympus images within the *existing* pipeline**. This has led to a critical data gap when the Cytosmart imager was unavailable.
    *   **Orchestration:** **Airflow DAGs** (Directed Acyclic Graphs) connect these workers, listening for pubsub messages upon task completion, making database entries, and triggering the next worker pool.

### New Brightfield Pipeline (within MellitOS)

The new pipeline will be built directly within the `MellitOS` application's codebase, focusing on the `src/images/` directory, particularly drawing inspiration from `analysis.py` and `imaging.py`.

**Key parts of the *new* pipeline compared to legacy:**
*   **Unified Codebase:** Instead of disparate repositories, **all brightfield analysis code will reside within the main `MellitOS` repository**.
*   **Input Agnosticism:** The new pipeline will leverage MellitOS's existing **`Image Repositories` (e.g., `OlympusRepo` and `CytosmartRepo`)**. Andreas highlighted that these repositories are designed with a **consistent interface** following the **Repository Pattern**, making it "completely interchangeable" to retrieve images from either source. This is crucial for processing both Cytosmart and Olympus images for brightfield analysis within a single system.
*   **Replicated Analysis:** The goal is to take the actual processing logic from the existing Cytosmart brightfield pipeline (currently outside MellitOS) and integrate it, ensuring the **exact same numerical outputs** for cell and cluster counting if fed the same images.
*   **Output Storage Shift:** New analysis output data (e.g., cell counts, cluster information) will be saved as **CSV files directly to Google Cloud Storage** in designated prefixes (e.g., `gs://pipeline_results/brightfield_versionXXX/`). This is a departure from directly writing to BigQuery, though BigQuery can still access these CSVs via external tables.
*   **Image Processing Concepts:** The analysis will build upon MellitOS's internal image processing capabilities, which include **background correction, normalization, and thresholding**.
*   **Cellpose Integration:** **Cellpose** is a segmentation tool that is "believed to be" used in the current BigQuery-feeding pipeline and will be useful for the new implementation. Cellpose is an anatomical segmentation algorithm that takes images (e.g., TIFFs, PNGs, JPEGs) and outputs masks, flows, and styles. It offers built-in models (like 'cyto3' for cytoplasm and 'nuclei' for nucleus) and allows for training custom models. Its `eval` function can run models on images, returning masks, flows, and styles.
*   **Workflow Integration:** The new pipeline will integrate with MellitOS's existing workflow system, where image sets are matched to pipelines based on channels detected, and analysis is scheduled by "pipeline bots" (e.g., `src/bots/pipeline.py`). The new Olympus image import system (`OlympusListener`) will be able to send Dataflow pipeline requests to convert and process images within MellitOS.

### Development and Setup for the Agent

To get started on this project, Andreas outlined several crucial steps and considerations:

*   **Local MellitOS Setup:** The **initial and most important step is to get the MellitOS application running locally** on your machine. This involves:
    *   Setting up environment variables in a `.env` file.
    *   Running a local PostgreSQL database using Docker Compose (`docker compose up db`).
    *   Activating a Python virtual environment and installing app packages (`pip install -e .`).
    *   Potentially resetting the local database to the production version using `sh/reset`.
*   **Database Connections:** You will need connection details for both the production database (for Olympus image data, in `production-401712`) and the `mellitos-dev` database (in `development-311316`, primarily for mitotracker analysis and treatment data).
*   **Cloud SQL Proxy:** A `cloud-sql-proxy` binary is essential for connecting to the cloud databases.
*   **Google Cloud Access:** Proper **authentication credentials** (`credentials.json`, `token.json`) and **permissions** are required to access Google Cloud Storage buckets and other services. You are expected to log any missing permissions encountered to facilitate future onboarding processes.
*   **Mac-Specific Issues:** Be aware that Apple MacBooks may misinterpret passwords in `.env` files, potentially requiring you to **copy and paste passwords directly into code files** (though this should not be pushed to the git repository).
*   **Java VM Requirement:** MellitOS requires a **Java Virtual Machine (JVM)** to read Olympus VSI image formats.
*   **Code Principles:** Adhere strictly to the **DRY (Don't Repeat Yourself) principle**, favoring reuse of existing MellitOS code abstractions (like image repositories) over copying and pasting. The `imaging.py` script serves as a good template for the new brightfield analysis.
*   **Git Workflow:** All new code should be committed to a **personal branch** (e.g., `[your initials]`) and not pushed directly to `main` until reviewed and ready for integration.
*   **Communication:** Andreas stressed the importance of frequent communication via **Slack for immediate problem-solving**. There will be a **weekly data science meeting on Tuesdays at 2 PM ET (11 AM PT for you)**. You should also attend **All Hands meetings on Fridays**. During Andreas's absence in April, **Madhumita will serve as the main technical and scientific contact**.