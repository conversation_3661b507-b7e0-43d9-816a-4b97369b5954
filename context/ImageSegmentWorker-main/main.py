from google.cloud import storage, pubsub_v1
from google.cloud.pubsub_v1.types import FlowControl
from cellpose.models import CellposeModel
import skimage
import imageio.v2 as imageio
from io import BytesIO
from PIL import Image
import json
import base64
import numpy as np

from concurrent.futures import TimeoutError
from google.cloud import pubsub_v1

import os

# TODO(developer)
project_id = os.environ.get('PROJECT_ID')  #"development-311316"
subscription_id = os.environ.get('SUBSCRIPTION_ID') # "segment-image-sub"
topic_id = os.environ.get('TOPIC_ID') #"new-mask"
# Number of seconds the subscriber should listen for messages
timeout = 5.0

model_name = os.environ.get('MODEL_NAME') # 'MCL_CP_005'
upload_bucket_name = os.environ.get('UPLOAD_BUCKET_NAME') #"mellicell_development_bucket"
scale = 2.5

def separate_uri(object_uri):
    split_uri = object_uri.split('/')
    bucket_name = split_uri[2]
    object_name = "/".join(split_uri[3:])
    return bucket_name, object_name

def load_model(model_path="/app/MCL_CP_005"):
    model = CellposeModel(pretrained_model=model_path)
    return model

def get_blob(bucket_name, object_name):
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(object_name)
    return blob

def load_image(blob):
    image_bytes = blob.download_as_bytes()
    buf = BytesIO(image_bytes)
    # img = imageio.imread(image_bytes, format='tiff')
    # img = skimage.io.imread(buffer)
    img = np.array(Image.open(buf))
    return img

def eval_image(image, model):
    mask, _, _ = model.eval(image, channels=[0,0])
    return mask

def preprocess_image(image, scale=2.5):
    shape = image.shape
    new_shape = (shape[0]//scale, shape[1]//scale)
    image = skimage.transform.resize(image, new_shape)
    return image

def upload_image(image, blob):
    buffer = BytesIO()
    imageio.imwrite(buffer, image, format='tiff')
    image_bytes = buffer.getvalue()
    blob.upload_from_string(image_bytes, content_type='image/tiff')

def decode_message(message_data):
    data = json.loads(message_data.decode('utf-8'))
    return data

def send_message(publisher, topic_id, message):
    topic_path =  publisher.topic_path(project_id, topic_id)
    data = json.dumps(message).encode('utf-8')
    future = publisher.publish(topic_path, data)
    print(future.result())

def construct_label_uri(message, model_name, upload_bucket_name):
    plate_name = message.attributes.get("plate")
    culture_day = message.attributes.get("cultureDay")
    well_name = message.attributes.get("wellName")
    channel_name = message.attributes.get("channelName")
    image_basename = os.path.basename(message.attributes.get("objectUri"))
    return f"gs://{upload_bucket_name}/masks/{model_name}/{plate_name}/{well_name}/day{culture_day}/{image_basename}"


subscriber = pubsub_v1.SubscriberClient()
# The `subscription_path` method creates a fully qualified identifier
# in the form `projects/{project_id}/subscriptions/{subscription_id}`
subscription_path = subscriber.subscription_path(project_id, subscription_id)

def callback(message: pubsub_v1.subscriber.message.Message) -> None:
    print(f"Received {message}.")
    #message_data = decode_message(message.data)
    if message.attributes.get("channelName") != "BF":
        print("not a bright-field image")
        message.ack()
        return
    download_bucket_name, download_object_name = separate_uri(message.attributes.get("objectUri"))
    download_blob = get_blob(download_bucket_name, download_object_name)
    image = load_image(download_blob)
    preprocessed_image = preprocess_image(image, scale=scale)
    model = load_model("/app/" + model_name)
    label = eval_image(preprocessed_image, model)
    upload_uri = construct_label_uri(message, model_name, upload_bucket_name)
    mask_bucket_name, upload_object_name = separate_uri(upload_uri)
    upload_blob = get_blob(mask_bucket_name, upload_object_name)
    upload_image(label, upload_blob)
    publisher = pubsub_v1.PublisherClient()
    new_message_data = {
        "image_id": int(message.attributes.get("imageId")),
        "plate_id":int(message.attributes.get("plateId")),
        "plate": message.attributes.get("plate"),
        "well_id": int(message.attributes.get("wellId")),
        "well": message.attributes.get("wellName"),
        "culture_day":int(message.attributes.get("cultureDay")),
        "mask_uri": upload_uri,
        "image_uri":message.attributes.get("objectUri"),
        "scale":scale,
        "model":model_name,
        "channel_name":message.attributes.get("channelName"),
        "original_um_per_pixel":float(message.attributes.get("pixelCalibration")),
        "size_x":label.shape[0],
        "size_y":label.shape[1],
    }
    send_message(publisher, topic_id, new_message_data)
    message.ack()
fcontrol = FlowControl(max_messages=5)
streaming_pull_future = subscriber.subscribe(subscription_path, flow_control=fcontrol, callback=callback)
print(f"Listening for messages on {subscription_path}..\n")

# Wrap subscriber in a 'with' block to automatically call close() when done.
with subscriber:
    # When `timeout` is not set, result() will block indefinitely,
    # unless an exception is encountered first.
    streaming_pull_future.result()
    
