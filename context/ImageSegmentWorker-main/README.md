# ImageSegmentWorker

The image segment worker is meant to listen for bright-field olympus images that have been converted to tiff over pubsub messaging. It will pull the message and process the image, creating a new segmentation mask. Then, it will save that mask and pass along a new message that will notify of the masks creation.

## Configuration

the program only needs the following environment variable to work.

<ol>
  <li>PROJECT_ID: the project id where the resources it is accessing reside</li>
  <li>SUBSCRIPTION_ID: the pubsub subscription it will listen to for new images</li>
  <li>TOPIC_ID: the pubsub topic it will post new mask notifications to</li>
  <li>MODEL_NAME: the name of the Cellpose model it should use</li>
  <li>UPLOAD_BUCKET_NAME: the name of the bucket it should upload new segmentation masks to</li>
</ol>


## Deployment to the cloud

<ol>
  <li>Create an instance on google compute engine (E2 standard 16gb)</li>
  <li>
    SSH into the instance
    <ol>
      <li>upload the main.py file as well as requirements.txt to the instance</li>
      <li>`mkdir /app`</li>
      <li>`mv main.py /app/main.py`</li>
      <li>`mv requirement.txt /app/requirements.txt`</li>
      <li>`pip install -r /app/requirements.txt`</li>
    </ol>
  </li>
  <li>shut the instance down</li>
  <li>got to "images" under "storage" in compute engine</li>
  <li>click create image</li>
  <li>select "disk" for source, then select the disk of the instance you shut down for the source disk</li>
  <li>you can choose to make the location multi-regional and also add a family and description. hit "create"</li>
  <li>once created, go to "instance templates" and hit "create an instance template"</li>
  <li>give it a name, **select "spot" under provisioning model**,choose "us-east1" region, select "e2-standard-4" instance type</li>
  <li>under advanced options, put in the following start up script
    ```	
    #!/bin/bash
    export PROJECT_ID='production-401712'
    export SUBSCRIPTION_ID='segment-ix83-tiff-sub'
    export TOPIC_ID='ix83-lipid-mask-uploaded'
    export MODEL_NAME='MCL_CP_005'
    export UPLOAD_BUCKET_NAME='ix83-lipid-masks'
    python3 /app/main.py
    ```
  </li>
  <li>select change boot disk, select custom images, and select the image you made earlier, set the size >= 20gb</li>
  <li>allow all access to google apis then create</li>
  <li>go to instance groups and select create instance group</li>
  <li>name the group what you want and select us-east1 region</li>
  <li>select minimum of 0 instances</li>
  <li>for autoscaling signals, select pubsub queue and select the topic of SUBSCRIPTION_ID it is listening for</li>
  <li>you can assign 2 messages per instance and create the group</li>
</ol>
  
