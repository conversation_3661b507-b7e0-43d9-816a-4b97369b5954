from google.cloud import storage, bigquery, pubsub_v1
from google.cloud.pubsub_v1.types import FlowControl
import skimage
import json
from io import BytesIO
import numpy as np
from PIL import Image
import pandas as pd
import os
import writer
import redis
from uuid import uuid4

project_id = os.environ.get('PROJECT_ID') #'development-311316'
topic_id = os.environ.get('TOPIC_ID') #'measurments-added'
dataset_id = os.environ.get('DATASET_ID') #'mellicell_image_data_v4'
table_id = os.environ.get('TABLE_ID') #"test_olympus_lipids"
subscription_id = os.environ.get('SUBSCRIPTION_ID') #'measure-mask-sub'
target_bucket_name = os.environ.get('TARGET_BUCKET_NAME')
REDIS_HOST = os.environ.get('REDIS_HOST', '**************')
REDIS_PORT = os.environ.get('REDIS_PORT', 6379)

def decode_message(message):
    results = {}
    for key in message.attributes:
        if key in ["culture_day", "plate_id", "well_id", "mask_id", "image_id"]:
            results[key] = int(message.attributes.get(key))
        elif key in ["original_um_per_pixel", "scale"]:
            results[key] = float(message.attributes.get(key))
        else:
            results[key] = message.attributes.get(key)
    
    return results

def get_blob(bucket_name, object_name):
    storage_client = storage.Client()
    return storage_client.bucket(bucket_name).blob(object_name)

def separate_uri(object_uri):
    split_uri = object_uri.split('/')
    bucket_name = split_uri[2]
    object_name = "/".join(split_uri[3:])
    return bucket_name, object_name

def load_image(blob):
    image_bytes = blob.download_as_bytes()
    buf = BytesIO(image_bytes)
    # img = imageio.imread(image_bytes, format='tiff')
    # img = skimage.io.imread(buffer)
    img = np.array(Image.open(buf))
    return img

def make_fused_mask(mask):
    binary_mask = mask.astype(bool)
    r0 = skimage.morphology.rectangle(20, 2)
    r90 = skimage.morphology.rectangle(2, 20)
    diamond = skimage.morphology.diamond(5)
    fused_mask = skimage.morphology.binary_erosion(
        skimage.morphology.binary_dilation(
            skimage.morphology.binary_dilation(
                binary_mask,
                r0
            ),
            r90
        ),
        diamond
    )
    return skimage.measure.label(fused_mask)


def measure_mask(mask, fused_mask, metadata):
    results = []
    regionprops = skimage.measure.regionprops(mask)
    for props in regionprops:
        results.append({
            "culture_day":metadata["culture_day"], #1
            "image_id":metadata["image_id"], #2
            "image_uri":metadata["image_uri"], #3
            "well_id":metadata["well_id"], #4
            "well":metadata["well"], #5
            "plate_id":metadata["plate_id"], #6
            "plate":metadata["plate"], #7
            "mask_id":metadata["mask_id"], #8
            "model":metadata["model"], #9
            "label": props.label, #10
            "pixel_area": props.area, #11
            "um_area": props.area * (metadata["original_um_per_pixel"]*metadata["scale"])**2, #12
            "perimeter":props.perimeter, #13
            "centroid_0":props.centroid[0], #14
            "centroid_1":props.centroid[1], #15
            "axis_major":props.axis_major_length, #16
            "axis_minor":props.axis_minor_length, #17
            "cluster_id":int(fused_mask[int(props.centroid[0]), int(props.centroid[1])]) #18
        })
    return results



def message_completion(metadata):
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(project_id, topic_id)
    data = json.dumps(metadata).encode('utf-8')
    future = publisher.publish(topic_path, data)
    print(future.result())


def mask_exists(redis_client: redis.Redis, mask_id: int):
    return redis_client.exists(mask_id)

def get_offset(redis_client: redis.Redis, mask_id: int):
    return redis_client.get(mask_id)

def get_latest_offset(redis_client: redis.Redis):
    return redis_client.get('latest_offset')

def increment_latest_offset(redis_client: redis.Redis, increment: int):
    latest_offset = redis_client.get('latest_offset')
    new_offset = latest_offset + increment
    redis_client.set('latest_offset', new_offset)

def set_mask_offset(redis_client: redis.Redis, mask_id: int, offset: int):
    redis_client.set(mask_id, offset, ex=60*60*24)

def write_stream_open(redis_client: redis.Redis):
    return redis_client.exists('write_stream_name')

def set_write_stream_name(redis_client: redis.Redis, stream_name: str):
    redis_client.set('write_stream_name', stream_name)

def get_write_stream_name(redis_client: redis.Redis):
    return redis_client.get('write_stream_name')

def callback(message: pubsub_v1.subscriber.message.Message) -> None:
    print(f"Received {message}")
    metadata = decode_message(message)
    download_bucket_name, download_object_name = separate_uri(metadata["mask_uri"])
    blob = get_blob(download_bucket_name, download_object_name)
    labels = load_image(blob)
    fused_labels = make_fused_mask(labels)
    measurments = measure_mask(labels, fused_labels, metadata)
    redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT)

    if not write_stream_open(redis_client):
        stream_name = writer.create_write_stream(project_id=project_id, dataset_id=dataset_id, table_id=table_id)
        set_write_stream_name(redis_client=redis_client, stream_name=stream_name)
    else:
        stream_name = get_write_stream_name(redis_client=redis_client)
    
    # offset management
    offset_new = False
    if mask_exists(redis_client, metadata["mask_id"]):
        mask_offset = get_offset(redis_client=redis_client, mask_id=metadata['mask_id'])
    else:
        offset_new = True
        mask_offset = get_latest_offset(redis_client)
        increment_latest_offset(redis_client, len(measurments))
        set_mask_offset(redis_client, metadata["mask_id"], mask_offset)

    
    write_response = writer.append_rows_pending(measurments, mask_offset, stream_name)
    # storage write API fails because of opening too many streams 
    # there are many workers just stuck on this so we write them to a bucket instead
    # will have a function just load each csv into the table instead
    # writer.append_rows_pending(project_id=project_id, dataset_id=dataset_id, table_id=table_id, data_rows=measurments)
    

    redis_client.close()
    csv_buffer = BytesIO()
    pd.DataFrame(measurments).to_csv(csv_buffer, index=False)
    # construct a csv file name for the bucket
    # uuid4 makes it so it is always unique
    upload_blob = get_blob(target_bucket_name, f"{metadata['model']}/plate{metadata['plate']}/day{metadata['culture_day']}/{metadata['well']}/{metadata['channel_name']}_{uuid4().hex[:4]}.csv")
    upload_blob.upload_from_string(csv_buffer.getvalue(), content_type='text/csv')
    print('uploaded')

    # with file written job is done and message can be acknoleged
    message.ack()
    message_completion(metadata)
    return

    
fcontrol = FlowControl(max_messages=5)
subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(project_id, subscription_id)
streaming_pull_future = subscriber.subscribe(subscription_path, flow_control=fcontrol, callback=callback)
print(f"Listening for messages on {subscription_path}..\n")
with subscriber:
    streaming_pull_future.result()
