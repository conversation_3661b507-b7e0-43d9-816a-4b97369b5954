# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lipid_record.proto
# Protobuf Python Version: 4.24.0-main
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12lipid_record.proto\"\xd2\x02\n\x0bLipidRecord\x12\x13\n\x0b\x63ulture_day\x18\x01 \x02(\x03\x12\x10\n\x08image_id\x18\x02 \x02(\x03\x12\x11\n\timage_uri\x18\x03 \x02(\t\x12\x0f\n\x07well_id\x18\x04 \x02(\x03\x12\x0c\n\x04well\x18\x05 \x02(\t\x12\x10\n\x08plate_id\x18\x06 \x02(\x03\x12\r\n\x05plate\x18\x07 \x02(\t\x12\x0f\n\x07mask_id\x18\x08 \x02(\x03\x12\r\n\x05model\x18\t \x02(\t\x12\r\n\x05label\x18\n \x02(\x03\x12\x12\n\npixel_area\x18\x0b \x02(\x02\x12\x0f\n\x07um_area\x18\x0c \x02(\x02\x12\x11\n\tperimeter\x18\r \x02(\x02\x12\x12\n\ncentroid_0\x18\x0e \x02(\x02\x12\x12\n\ncentroid_1\x18\x0f \x02(\x02\x12\x12\n\naxis_major\x18\x10 \x02(\x02\x12\x12\n\naxis_minor\x18\x11 \x02(\x02\x12\x12\n\ncluster_id\x18\x12 \x02(\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lipid_record_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_LIPIDRECORD']._serialized_start=23
  _globals['_LIPIDRECORD']._serialized_end=361
# @@protoc_insertion_point(module_scope)
