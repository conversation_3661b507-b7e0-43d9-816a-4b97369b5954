# MeasureMaskWorker

## to run locally

```
	
#!/bin/bash
export REDIS_HOST=************
export REDIS_PORT=6379
export PROJECT_ID='production-401712'
export TOPIC_ID='ix83-lipid-morphology-written'
export SUBSCRIPTION_ID='measure-ix83-lipid-mask-sub'
export DATASET_ID='lipid_measurments'
export TABLE_ID='ix83-brightfield-cp5-2-5'
export TARGET_BUCKET_NAME='ix83-brightfield-measurments'
python3 /app/main.py
```

## to run in cloud

<ol>
  <li>Create an instance on google compute engine (E2 standard 16gb)</li>
  <li>
    SSH into the instance
    <ol>
      <li>upload the main.py file as well as requirements.txt to the instance</li>
      <li>`mkdir /app`</li>
      <li>`mv main.py /app/main.py`</li>
      <li>`mv requirement.txt /app/requirements.txt`</li>
      <li>`pip install -r /app/requirements.txt`</li>
    </ol>
  </li>
  <li>shut the instance down</li>
  <li>got to "images" under "storage" in compute engine</li>
  <li>click create image</li>
  <li>select "disk" for source, then select the disk of the instance you shut down for the source disk</li>
  <li>you can choose to make the location multi-regional and also add a family and description. hit "create"</li>
  <li>once created, go to "instance templates" and hit "create an instance template"</li>
  <li>give it a name, **select "spot" under provisioning model**,choose "us-east1" region, select "e2-standard-4" instance type</li>
  <li>under advanced options, put in the following start up script
   ```
    #!/bin/bash
    export REDIS_HOST=************
    export REDIS_PORT=6379
    export PROJECT_ID='production-401712'
    export TOPIC_ID='ix83-lipid-morphology-written'
    export SUBSCRIPTION_ID='measure-ix83-lipid-mask-sub'
    export DATASET_ID='lipid_measurments'
    export TABLE_ID='ix83-brightfield-cp5-2-5'
    export TARGET_BUCKET_NAME='ix83-brightfield-measurments'
    python3 /app/main.py
    ```
  </li>
  <li>select change boot disk, select custom images, and select the image you made earlier, set the size >= 20gb</li>
  <li>allow all access to google apis then create</li>
  <li>go to instance groups and select create instance group</li>
  <li>name the group what you want and select us-east1 region</li>
  <li>select minimum of 0 instances</li>
  <li>for autoscaling signals, select pubsub queue and select the topic of SUBSCRIPTION_ID it is listening for</li>
  <li>you can assign 2 messages per instance and create the group</li>
</ol>
