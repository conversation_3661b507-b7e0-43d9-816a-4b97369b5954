from google.cloud import bigquery_storage_v1
from google.cloud.bigquery_storage_v1 import types
from google.cloud.bigquery_storage_v1 import writer
from google.protobuf import descriptor_pb2

import lipid_record_pb2

def create_row_data(
            culture_day, #1
            image_id, #2
            image_uri, #3
            well_id, #4
            well, #5
            plate_id, #6
            plate, #7
            mask_id, #8
            model, #9
            label, #10
            pixel_area, #11
            um_area, #12
            perimeter, #13
            centroid_0, #14
            centroid_1, #15
            axis_major, #16
            axis_minor, #17
            cluster_id #18
            ):
    row = lipid_record_pb2.LipidRecord()
    
    row.culture_day = culture_day #1
    row.image_id = image_id #2
    row.image_uri = image_uri #3
    row.well_id = well_id #4
    row.well = well #5
    row.plate_id = plate_id #6
    row.plate = plate #7
    row.mask_id = mask_id #8
    row.model = model #9
    row.label = label #10
    row.pixel_area = pixel_area #11
    row.um_area = um_area #12
    row.perimeter = perimeter #13
    row.centroid_0 = centroid_0 #14
    row.centroid_1 = centroid_1 #15
    row.axis_major = axis_major #16
    row.axis_minor = axis_minor #17
    row.cluster_id = cluster_id #18
    
    return row.SerializeToString()

def create_write_stream(project_id, dataset_id, table_id):
    write_client = bigquery_storage_v1.BigQueryWriteClient()
    parent = write_client.table_path(project_id, dataset_id, table_id)
    write_stream = types.WriteStream()

    write_stream.type_ = types.WriteStream.Type.PENDING
    write_stream = write_client.create_write_stream(
        parent=parent, write_stream=write_stream
    )
    stream_name = write_stream.name
    return stream_name

def append_rows_pending(data_rows, offset, stream_name):
    """write_client = bigquery_storage_v1.BigQueryWriteClient()
    parent = write_client.table_path(project_id, dataset_id, table_id)
    write_stream = types.WriteStream()

    write_stream.type_ = types.WriteStream.Type.PENDING
    write_stream = write_client.create_write_stream(
        parent=parent, write_stream=write_stream
    )
    stream_name = write_stream.name
    """
    write_client = bigquery_storage_v1.BigQueryWriteClient()
    request_template = types.AppendRowsRequest()

    request_template.write_stream = stream_name
    

    proto_schema = types.ProtoSchema()
    proto_descriptor = descriptor_pb2.DescriptorProto()
    lipid_record_pb2.LipidRecord.DESCRIPTOR.CopyToProto(proto_descriptor)
    proto_schema.proto_descriptor = proto_descriptor
    proto_data = types.AppendRowsRequest.ProtoData()
    proto_data.writer_schema = proto_schema
    request_template.proto_rows = proto_data

    append_rows_stream = writer.AppendRowsStream(write_client, request_template)

    proto_rows = types.ProtoRows()
    for rec in data_rows:
        proto_rows.serialized_rows.append(create_row_data(**rec))

    request = types.AppendRowsRequest()
    request.offset = offset
    proto_data = types.AppendRowsRequest.ProtoData()
    proto_data.rows = proto_rows
    request.proto_rows = proto_data

    response_future_1 = append_rows_stream.send(request)

    append_rows_response = response_future_1.result()

    append_rows_stream.close()

    write_client.finalize_write_stream(name=stream_name)


def close_write_stream(project_id, dataset_id, table_id, stream_name):
    write_client = bigquery_storage_v1.BigQueryWriteClient()
    parent = write_client.table_path(project_id, dataset_id, table_id)
    batch_commit_write_streams_request = types.BatchCommitWriteStreamsRequest()
    batch_commit_write_streams_request.parent = parent
    batch_commit_write_streams_request.write_streams = [stream_name]
    write_client.batch_commit_write_streams(batch_commit_write_streams_request)

    print(f"Writes to stream '{stream_name}' have been committed.")
