package com.mellicell.imaging;

import com.google.api.gax.batching.FlowControlSettings;
import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.MessageReceiver;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.PubsubMessage;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.TimeoutException;
import org.threeten.bp.Duration;
import java.util.Map;

public class PubsubSubscriber {

  static Duration extensionPeriod = Duration.ofHours(1L);
  static Duration maxExtensionDuration = Duration.ofMinutes(9L);
  static Duration minExtensionDuration = Duration.ofMinutes(8L);

  public static void subscribeAsync(String projectId, String resultsBucketName, String subscriptionId, String publishingTopicId) {
    ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);

    // Instantiate an asynchronous message receiver.
    MessageReceiver receiver =
        (PubsubMessage message, AckReplyConsumer consumer) -> {
          // Handle incoming message, then ack the received message.
          System.out.println("Id: " + message.getMessageId());
          // there is not message data. only string attributes
          Map<String, String> attributes = message.getAttributesMap();
          System.out.println(attributes.keySet());
          String messageData = attributes.get("object_uri");
          System.out.println("Recived: " + messageData);
          Pipeline.process(messageData, projectId, publishingTopicId, resultsBucketName);
          consumer.ack();
        };

    Subscriber subscriber = null;
    FlowControlSettings flowControlSettings = FlowControlSettings.newBuilder()
      .setMaxOutstandingElementCount(2L)
      .build();
    subscriber = Subscriber.newBuilder(subscriptionName, receiver)
      .setFlowControlSettings(flowControlSettings)
      .setMaxAckExtensionPeriod(extensionPeriod)
      .setMaxDurationPerAckExtension(maxExtensionDuration)
      .setMinDurationPerAckExtension(minExtensionDuration)
      .build();
    // Start the subscriber.
    subscriber.startAsync().awaitRunning();
    System.out.printf("Listening for messages on %s:\n", subscriptionName.toString());
    subscriber.awaitTerminated();
    
  }
}
