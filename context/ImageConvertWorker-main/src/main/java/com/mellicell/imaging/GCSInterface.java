package com.mellicell.imaging;

import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import com.google.cloud.WriteChannel;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;
import com.google.api.gax.paging.Page;
import com.google.cloud.storage.Blob;

import java.util.ArrayList;


public class GCSInterface {
    Storage storage;

    public GCSInterface(String projectId) {
        this.storage = StorageOptions.newBuilder().setProjectId(projectId).build().getService();
    }
    
    public void uploadBytes(byte[] bytes, String bucketName, String objectName, String format) {
        BlobId blobId = BlobId.of(bucketName, objectName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(format).build();
        WriteChannel writer = this.storage.writer(blobInfo);
        try {
            writer.write(ByteBuffer.wrap(bytes));
            writer.close();
        } catch (IOException e) {
            System.out.println("There was an IO exception while writing to bucket.");
            System.exit(1);
        }
    }

    public ArrayList<String> listObjects(String bucketName, String objectPrefix) {
        ArrayList<String> objectNames = new ArrayList<String>();
        Page<Blob> blobs = this.storage.list(bucketName, Storage.BlobListOption.prefix(objectPrefix));
        for (Blob blob: blobs.iterateAll()) {
            objectNames.add(blob.getName());
        }
        return objectNames;
    }

    public void downloadToFileName(String bucketName, String objectName, String fileName) {
        Blob blob = this.storage.get(BlobId.of(bucketName, objectName));
        int slash = fileName.lastIndexOf("/");
        if (slash > 0) {
            Path path = Paths.get(fileName.substring(0, slash));
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                System.out.println("IO exception trying to make dir for file download.");
                System.exit(1);
            }
        }

        blob.downloadTo(Paths.get(fileName));
    }

    public String downloadMultiFileImage(String bucketName, String vsiObjectName) {
        String name = Utils.genRandomString(7);
        String fname = "/tmp/" + name + ".vsi";
        String dir = "/tmp/" + "_" + name + "_";
        downloadToFileName(bucketName, vsiObjectName, fname);
        for (String etsName: listObjects(bucketName, Utils.getPrefixFromVsi(vsiObjectName))) {
            String relPath = Utils.getEtsRelativePath(etsName);
            String etfDownloadName =  dir + "/" + relPath;
            downloadToFileName(bucketName, etsName, etfDownloadName);
        }
        return fname;
    }

    public  boolean objectExists(String bucketName, String objectName) {
        Blob blob = this.storage.get(BlobId.of(bucketName, objectName));
        if (blob != null && blob.exists()) {
            return true;
        } else {
            return false;
        }
    }

    public static String[] splitUri(String objectUri) {
        String[] pathArray = objectUri.split("/");
        String bucketName = pathArray[2];
        StringBuilder sb = new StringBuilder();
        for (int i=3; i<pathArray.length; i++) {
            sb.append(pathArray[i]);
            if (i < pathArray.length - 1) {
                sb.append("/");
            }
        }
        String objectName = sb.toString();
        String[] results = {bucketName, objectName};
        return results;
    }
    
    
}
