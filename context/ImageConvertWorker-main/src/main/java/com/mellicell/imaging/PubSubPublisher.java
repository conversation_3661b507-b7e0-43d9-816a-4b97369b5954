package com.mellicell.imaging;

import java.util.Hashtable;

import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.api.gax.rpc.ApiException;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class PubSubPublisher {
	String projectId;
	String topicId;
	TopicName topicName;

	public PubSubPublisher(String projectId, String topicId) {
		this.projectId = projectId;
		this.topicId = topicId;
		this.topicName = TopicName.of(this.projectId, this.topicId);
	}

	private Publisher createPublisher() throws IOException{
		Publisher publisher;
		publisher = Publisher.newBuilder(this.topicName).build();
		return publisher;
	}

	public void pubishMessage (String JsonString) {
		try {
			ByteString data = ByteString.copyFromUtf8(JsonString);
			PubsubMessage pubsubMessage = PubsubMessage.newBuilder().setData(data).build();
			Publisher publisher = createPublisher();
			ApiFuture<String> future = publisher.publish(pubsubMessage);
			ApiFutures.addCallback(
				future,
				new ApiFutureCallback<String>() {
					@Override
					public void onFailure(Throwable throwable) {
						if (throwable instanceof ApiException) {
							ApiException apiException = ((ApiException) throwable);
						// details on exception
						System.out.println(apiException.getStatusCode().getCode());
						System.out.println(apiException.isRetryable());
						}
					System.out.println("Error publishing message: " + JsonString);
					}

					@Override
					public void onSuccess(String messageId) {
						System.out.println("Published message ID: " + messageId);
					}
				},
				MoreExecutors.directExecutor());
			closePublisher(publisher);
		} catch (IOException e) {
			System.out.println("Error creating publisher.");
		}
	}

	private void closePublisher(Publisher publisher) {
		publisher.shutdown();
		try {
			publisher.awaitTermination(1, TimeUnit.MINUTES);
		} catch (InterruptedException e) {
			System.out.println("Publisher closing interrupted.");
		}
		
	}

	public static String generateJsonString(Hashtable<String, Object> data) {
		StringBuilder sb = new StringBuilder();
		sb.append("{");
		for (String key : data.keySet()) {
			String attribute;
			if (data.get(key) instanceof String) {
				if (data.get(key) != "null") {
					attribute = "\"" + key + "\": " + "\"" + data.get(key) + "\", ";
				} else {
					attribute = "\"" + key + "\": " + data.get(key) + ", ";
				}
			} else {
				attribute = "\"" + key + "\": " + data.get(key) + ", ";
			}
			sb.append(attribute);
		}
		sb.delete(sb.length() - 2, sb.length());
		sb.append("}");
		return sb.toString();
	}

}