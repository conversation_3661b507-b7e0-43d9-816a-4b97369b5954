package com.mellicell.imaging;

import loci.common.services.DependencyException;
import loci.common.services.ServiceException;
import loci.common.services.ServiceFactory;

import java.io.IOException;

import loci.common.ByteArrayHandle;
import loci.common.DebugTools;
import loci.common.Location;
import loci.formats.FormatException;
import loci.formats.ImageReader;
import loci.formats.ImageWriter;
import loci.formats.IFormatReader;
import loci.formats.IFormatWriter;
import loci.formats.meta.IMetadata;
import loci.formats.services.OMEXMLService;

import java.util.Hashtable;


import java.io.File;
import java.io.FileOutputStream;

public class IX83Reader {
    IFormatReader reader;
    IFormatWriter writer;
    IMetadata metadataStore;
    String inputFileName;
    Hashtable<String, Object> metadata;
    boolean writerIdSet;

    public static void main(String[] args) {
        IX83Reader rdr = new IX83Reader("/home/<USER>/test/L12/input_image.vsi");
        byte[] img_bytes = rdr.getChannelBytes(0);
        try {
            FileOutputStream fos = new FileOutputStream(new File("/home/<USER>/output_image.tif"));
            fos.write(img_bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public IX83Reader(String inputFileName) {
        DebugTools.setRootLevel("OFF");
        this.inputFileName = inputFileName;
        this.reader = new ImageReader();
        try {
            ServiceFactory factory = new ServiceFactory();
            OMEXMLService service = factory.getInstance(OMEXMLService.class);
            this.metadataStore = service.createOMEXMLMetadata();
            this.reader.setMetadataStore(this.metadataStore);
            this.reader.setId(this.inputFileName);
            this.metadata = this.reader.getSeriesMetadata();

            this.writer = new ImageWriter();
            this.writer.setMetadataRetrieve(this.metadataStore);
            this.writerIdSet = false;
        } catch (DependencyException e) {
            System.out.println("Exception in creating serviceFactory.");
            e.printStackTrace();
            System.exit(1);
        } catch (ServiceException e) {
            System.out.println("service failed to create OMEXMLMetadata.");
            e.printStackTrace();
            System.exit(1);
        } catch (FormatException e) {
            System.out.println("Reader raised a format exception on setting id");
            e.printStackTrace();
            System.exit(1);
        } catch (IOException e) {
            System.out.println("Reader raised an IO exception on setting id");
            e.printStackTrace();
            System.exit(1);
        }

    }

    public int getSizeX() {
        return this.reader.getSizeX();
    }

    public int getSizeY() {
        return this.reader.getSizeY();
    }

    public int getChannelCount() {
        return this.reader.getImageCount();
    }

    public String getChannelName(int channel) {
        String channelName;
        if (getChannelCount() == 1) {
            channelName = this.metadata.get("Channel name").toString();
        } else {
            int label = channel + 1;
            channelName = this.metadata.get("Channel name #" + label).toString();
        }
        return channelName;
    }

    public String getChannelWaveLength(int channel) {
        int label = channel + 1;
        String channelWaveLength = "null";
        if (getChannelCount() > 1) {
            channelWaveLength = this.metadata.get("Channel Wavelength Value #" + label).toString();
        }
        return channelWaveLength;
    }

    public String getChannelWaveLengthUnits(int channel){
        int label = channel + 1;
        String channelWaveLength = "null";
        if (getChannelCount() > 1) {
            channelWaveLength = this.metadata.get("Channel Wavelength Units #" + label).toString();
        }
        return channelWaveLength;
    }

    private String[] getCalibrationString() {
        String[] calibrations = this.metadata.get("Calibration").toString().replace("(", "").replace(")", "").split(", ");
        return calibrations;
    }

    public double getCalibrationX() {
        String[] calibrations = getCalibrationString();
        return Double.parseDouble(calibrations[0]);
    }
    
    public double getCalibrationY() {
        String[] calibrations = getCalibrationString();
        return Double.parseDouble(calibrations[1]);
    }

    public String getCalibrationUnits() {
        return this.metadata.get("Calibration units").toString();
    }

    public String getCreationTimeUTC() {
        return this.metadata.get("Creation time (UTC)").toString().replace("T", "|");
    }


    public Hashtable<String, Object> allMetadata() {
        return this.metadata;
    }

    public byte[] getChannelBytes(int channel) {
        this.reader.setSeries(0);
        int stringLength = 7;
        String fname = Utils.genRandomString(stringLength) + ".tif";
        ByteArrayHandle byteHandle = new ByteArrayHandle();
        Location.mapFile(fname, byteHandle);
        try {
            if (this.writerIdSet) {
                this.writer.changeOutputFile(fname);
            } else {
                this.writer.setId(fname);
                this.writerIdSet = true;
            }
            this.writer.setSeries(0);
            int index = channel * 1 + 0;
            this.writer.saveBytes(0, this.reader.openBytes(index));
        } catch (FormatException e) {
            System.out.println("There was a format exception in writing bytes.");
            System.exit(1);
        } catch (IOException e) {
            System.out.println("There was an IO exception in writing bytes.");
            System.exit(1);
        } 
        return byteHandle.getBytes();
    }
}
