package com.mellicell.imaging;

public class App {

    public static void main( String[] args )
    {
        String projectId = System.getenv("PROJECT_ID"); //"development-311316";
        String subscriptionId = System.getenv("SUBSCRIPTION_ID"); //"convert-image-sub"; 
        String publishingTopicId = System.getenv("PUBLISH_TOPIC_ID"); //"new-tiff-image";
        String resultsBucketName = System.getenv("RESULTS_BUCKET"); //"mellicell_development_bucket";
        PubsubSubscriber.subscribeAsync(projectId, resultsBucketName, subscriptionId, publishingTopicId);
    }
    
}
