package com.mellicell.imaging;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

import java.io.File;

public class Utils {
    public static void main(String[] args) {
        System.out.println(getPrefixFromVsi("path/to/my/image_file.vsi"));
        System.out.println(getEtsRelativePath("path/to/my/_image_file_/stack1/frame_t_0.ets"));
    }


    public static String genRandomString(int n) {
        String AlphaNumericString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
         + "0123456789"
         + "abcdefghijklmnopqrstuvxyz";
        // create StringBuffer size of AlphaNumericString
        StringBuilder sb = new StringBuilder(n);
        // ensures it always starts with a character rather than digit
        sb.append("A");
        for (int i = 0; i < n; i++) {
            // generate a random number between
            // 0 to AlphaNumericString variable length
            int index = (int)(AlphaNumericString.length() * Math.random());
 
            // add Character one by one in end of sb
            sb.append(AlphaNumericString.charAt(index));
        }

        return sb.toString();
    }

    public static String getPrefixFromVsi(String vsiObjectName) {
        String[] nameSplit = vsiObjectName.split("/");
        int pathLength = nameSplit.length;
        nameSplit[pathLength - 1] = "_" + nameSplit[pathLength - 1].replace(".vsi", "_");
        return String.join("/", nameSplit);
    }

    public static String getEtsRelativePath(String EtsObjectName) {
        String[] nameSplit = EtsObjectName.split("/");
        int pathLength = nameSplit.length;
        String[] relPathSplit = {nameSplit[pathLength - 2], nameSplit[pathLength - 1]};
        return String.join("/", relPathSplit);
    }

    public static String parsePlateSerial(String objectName) {
        Pattern platePattern = Pattern.compile("_Plate[A-Z]{0,2}[0-9]+_");
        Matcher plateMatcher = platePattern.matcher(objectName);
        if (plateMatcher.find()) {
            String plateDesignation = plateMatcher.group(0).replace("_Plate", "").replace("_", "");
            return plateDesignation;
        }
        return null;
    }

    public static int parseCultureDay(String objectName) {
        Pattern digitsPattern = Pattern.compile("_Day[0-9]+_");
        Matcher dayMatcher = digitsPattern.matcher(objectName);
        if (dayMatcher.find()) {
            int cultureDay = Integer.parseInt(dayMatcher.group(0).replace("_Day", "").replace("_", ""));
            return cultureDay;
        }
        return 0;
    }

    public static String parseWellName(String objectName) {
        Pattern wellPattern = Pattern.compile("_[A-Z][0-9]+_");
        Matcher wellMatcher = wellPattern.matcher(objectName);
        if (wellMatcher.find()) {
            String well = wellMatcher.group(0).replace("_", "").replace("_", "");
            return well;
        }
        return null;
    }

    public static String parseVersion(String objectName) {
        Pattern versionPattern = Pattern.compile("_[0-9]+[._]");
        Matcher versionMatcher = versionPattern.matcher(objectName);
        if (versionMatcher.find()) {
            String version = versionMatcher.group(0).replaceAll("_", "").replaceAll("[.]", "");
            return version;
        }
        return "00";
    }

    public static void deleteMultiFileImage(String vsiName) {
        File vsi = new File(vsiName);
        vsi.delete();
        deleteFiles(new File("_"+ vsiName.replace(".vsi", "_")));
    }

    public static boolean deleteFiles(File directory) {
        File[] allContents = directory.listFiles();
        if (allContents != null) {
            for (File file: allContents) {
                deleteFiles(file);
            }
        }
        return directory.delete();
    }

}
