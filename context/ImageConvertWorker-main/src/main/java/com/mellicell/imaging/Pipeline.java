package com.mellicell.imaging;

import java.util.Hashtable;

/**
 * Hello world!
 *
 */
public class Pipeline {

    public static void main(String[] args) {
        Pipeline.process(
            "gs://mellicell_raw_images/filelistener/plate149/flourescence/day57/P22/P_Plate0149_Day57_P22_01.vsi",
            "development-311316",
            "emptyTopic",
            "mellicell_development_bucket"
         );
    }
  
    public static void process(String fileUri, String projectId, String publishingTopicId, String uploadBucketName) {
        // I don't think the GCSInterface above is thread safe
        // just make a new one for each parallel process
        GCSInterface storageInterface = new GCSInterface(projectId);
        String[] bucketObjectNames = GCSInterface.splitUri(fileUri);
        String fname = storageInterface.downloadMultiFileImage(bucketObjectNames[0], bucketObjectNames[1]);
        String plateSerial = Utils.parsePlateSerial(bucketObjectNames[1]);
        int cultureDay = Utils.parseCultureDay(bucketObjectNames[1]);
        String wellName = Utils.parseWellName(bucketObjectNames[1]);
        String version = Utils.parseVersion(bucketObjectNames[1]);
        IX83Reader imageReader = new IX83Reader(fname);
        int numChannels = imageReader.getChannelCount();
        for (int ch = 0; ch < numChannels; ch++) {
            String uploadObjectName = "java/convert/" + plateSerial + "/" + wellName + "/day" + cultureDay + "/"
                    + imageReader.getChannelName(ch) + "_" + version + ".tif";
            byte[] channelBytes = imageReader.getChannelBytes(ch);
            storageInterface.uploadBytes(channelBytes, uploadBucketName, uploadObjectName, "image/tiff");
            Hashtable<String, Object> imageMeta = new Hashtable<String, Object>();
            imageMeta.put("originalUri", fileUri);
            imageMeta.put("objectUri", "gs://" + uploadBucketName + "/" + uploadObjectName);
            imageMeta.put("plate", plateSerial);
            imageMeta.put("cultureDay", cultureDay);
            imageMeta.put("wellName", wellName);
            imageMeta.put("channelName", imageReader.getChannelName(ch));
            imageMeta.put("channelWaveLength", imageReader.getChannelWaveLength(ch));
            imageMeta.put("waveLengthUnits", imageReader.getChannelWaveLengthUnits(ch));
            imageMeta.put("pixelCalibration", imageReader.getCalibrationX());
            imageMeta.put("calibrationUnits", imageReader.getCalibrationUnits());
            imageMeta.put("dimensionX", imageReader.getSizeX());
            imageMeta.put("dimensionY", imageReader.getSizeY());
            imageMeta.put("creationTime", imageReader.getCreationTimeUTC());
            String message = PubSubPublisher.generateJsonString(imageMeta);
            PubSubPublisher publisher = new PubSubPublisher(projectId, publishingTopicId);
            publisher.pubishMessage(message);
            Utils.deleteMultiFileImage(fname);
        }

    }
}
