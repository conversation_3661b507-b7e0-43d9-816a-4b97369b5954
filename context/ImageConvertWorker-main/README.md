# ImageConvertWorker

A service that listens for messages and converts Olympus Cellsens images into Tiff images. each channel of an image is saved as its own grays scale image.

## Configuration 

to run this code you need the following environment variables in your system

<ol>
  <li>PROJECT_ID: the project that program will be operating in</li>
  <li>SUBSCRIPTION_ID: the name of the subscription it will be listening for</li>
  <li>PUBLISH_TOPIC_ID: the name of the topic it will push completion messages to</li>
  <li>RESULTS_BUCKET: the bucket you wish the program to save converted images to</li>
</ol>


## Run

to build this program, you will need to have Apache Maven installed
```
#!/bin/bash
export PROJECT_ID="production-401712"
export SUBSCRIPTION_ID="convert-ix83-image-sub"
export PUBLISH_TOPIC_ID="ix83-tiff-image-uploaded"
export RESULTS_BUCKET="ix83-tiff-files"
mvn clean package
java -jar target/ix83-reader-1.0.0.jar
```

## Deployment to the cloud

<ol>
  <li>Create an instance on google compute engine (E2 standard 16gb)</li>
  <li>
    SSH into the instance
    <ol>
      <li>install java (open jdk 11 will work)</li>
      <li>upload the compiled jar file to the instance</li>
      <li>mkdir /app</li>
      <li>mv <jar-file> /app/ix83-reader.jar</li>
    </ol>
  </li>
  <li>shut the instance down</li>
  <li>got to "images" under "storage" in compute engine</li>
  <li>click create image</li>
  <li>select "disk" for source, then select the disk of the instance you shut down for the source disk</li>
  <li>you can choose to make the location multi-regional and also add a family and description. hit "create"</li>
  <li>once created, go to "instance templates" and hit "create an instance template"</li>
  <li>give it a name, **select "spot" under provisioning model**,choose "us-east1" region, select "e2-standard-4" instance type</li>
  <li>under advanced options, put in the following start up script
    ```
    #!/bin/bash
    export PROJECT_ID="production-401712"
    export SUBSCRIPTION_ID="convert-ix83-image-sub"
    export PUBLISH_TOPIC_ID="ix83-tiff-image-uploaded"
    export RESULTS_BUCKET="ix83-tiff-files"
    java -jar /app/ix83-reader.jar
    ```
  </li>
  <li>select change boot disk, select custom images, and select the image you made earlier, set the size >= 20gb</li>
  <li>allow all access to google apis then create</li>
  <li>go to instance groups and select create instance group</li>
  <li>name the group what you want and select us-east1 region</li>
  <li>select minimum of 0 instances</li>
  <li>for autoscaling signals, select pubsub queue and select the topic of SUBSCRIPTION_ID it is listening for</li>
  <li>you can assign 2 messages per instance and create the group</li>
</ol>
  
