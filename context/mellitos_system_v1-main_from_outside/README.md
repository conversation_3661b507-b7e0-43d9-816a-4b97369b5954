# mellitos_system_v1

this is the system that is currently in production.  
it is composed of multiple components:  

- assay calc service: logic for calculate secretion rates for glucose uptake assays  
- image resize service: simple flask app hosted in cloud run. requests for viewing olympus images are sent to it and it sends a reformatted version so app engine isn't over loaded  
- mask request processor: cloud function that starts a vm to segment image for mask viewing. (needs to run only once)  
- mellitos: main application for viewing images and downloading lipid data.  
- register cytosmart images: cloud function that writes image details to sql for mellitos (omni imager).  
- register olympus images: cloud function that writes image details to sql (ix83 microscope)  
- v1: flask app that calcualtes z prime value based on inputs
    - z prime generator/data analysis app v2: is in production


