from google.cloud import storage, secretmanager
import json
import re
import sqlalchemy
import os

storage_client = storage.Client()
secret_client = secretmanager.SecretManagerServiceClient()

image_table = sqlalchemy.table(
    "image",
    sqlalchemy.column("id"),
    sqlalchemy.column("source"),
    sqlalchemy.column("image_uri"),
    sqlalchemy.column("mask_uri"),
    sqlalchemy.column("well"),
    sqlalchemy.column("day"),
    sqlalchemy.column("image_set_id"),
    sqlalchemy.column("plate_id")
)

image_set_table = sqlalchemy.table(
    "image_set",
    sqlalchemy.column("id"),
    sqlalchemy.column("experiment_id"),
    sqlalchemy.column("scan_number"),
    sqlalchemy.column("experiment_name"),
    sqlalchemy.column("day"),
    sqlalchemy.column("number_of_wells"),
    sqlalchemy.column("pixels_per_mm"),
    sqlalchemy.column("plate_id")
)

plate_table = sqlalchemy.table(
  "plate",
  sqlalchemy.column("id"),
  sqlalchemy.column("serial")
)

def connect_unix_socket() -> sqlalchemy.engine.base.Engine:
    connection_secrets = json.loads(
        secret_client.access_secret_version(
            request={
                "name":"projects/1049074702976/secrets/mellitos-dev-db-secret/versions/3"
            }
        ).payload.data.decode("UTF-8")
    )
    pool = sqlalchemy.create_engine(
        sqlalchemy.engine.url.URL.create(
            drivername="postgresql+pg8000",
            username=connection_secrets["DB_USER"],
            password=connection_secrets["DB_PASS"],
            database=connection_secrets["DB_NAME"],
            query={
                "unix_sock": "{}/.s.PGSQL.5432".format(
                    '/home/<USER>/cloudsql/mellitos'
                )
            }
        )
    )
    return pool


def run(event, context):
    bucket = event["bucket"]
    name = event["name"]

    if name.endswith(".tif"):
        blob = storage_client.bucket(bucket).blob(name)

        well = re.findall(re.compile('[A-Q][0-9]{1,2}'), os.path.basename(blob.name))[0]

        
        plate = re.findall(re.compile('plate[0-9]{4}'), os.path.basename(blob.name.lower()))[0].replace('plate', '')
        day = int(re.findall(
            re.compile('day[0-9]{1,3}'),
            os.path.basename(blob.name.lower())
            )[0].replace('day', '')
        )

        plate_select_stmt = (
            sqlalchemy.select(plate_table).
            where(
                plate_table.c.serial==plate
            )
        )

        with connect_unix_socket().connect() as conn:
            plate_results =  list(conn.execute(plate_select_stmt))

        if not plate_results:

            plate_insert_stmt = (
                sqlalchemy.insert(plate_table).
                values(
                    serial=plate
                )
            )
            with connect_unix_socket().connect() as conn:
                conn.execute(plate_insert_stmt)
                plate_results = list(conn.execute(plate_select_stmt))
        
        plate_id = plate_results[0][0]

        insert_image_stmt = (
            sqlalchemy.insert(image_table).
            values(
                source="olympus",
                image_uri=f"gs://{blob.bucket.name}/{blob.name}",
                well=well,
                day=day,
                plate_id=plate_id
            )
        )
        
        compiled = insert_image_stmt.compile()
        print(compiled)
        print(compiled.params)

        with connect_unix_socket().connect() as conn:
            conn.execute(insert_image_stmt)
            conn.commit()

    return
