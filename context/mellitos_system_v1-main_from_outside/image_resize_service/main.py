from flask import Flask, request, jsonify
from google.cloud import storage
import imageio
from PIL import Image
from io import BytesIO
import base64
import json
import os

app = Flask(__name__)

storage_client = storage.Client()

@app.route('/', methods=['POST'])
def index():
    data = request.get_json()

    bucket_name = data['image_uri'].split('/')[2]
    object_name = "/".join( data['image_uri'].split('/')[3:])
    image_blob = storage_client.bucket(bucket_name).blob(object_name)
    im16 = imageio.imread(
        BytesIO(image_blob.download_as_bytes())
    )[0, 0, 0, :, :]
    
    im8 = (im16 / 256).astype('uint8')
    del(im16)
    big_image = Image.fromarray(im8)
    del(im8)
    small_img = big_image.resize((4000, 4000))
    del(big_image)
    bytes_arr = BytesIO()
    small_img.save(bytes_arr, format="JPEG")
    content = base64.b64encode(bytes_arr.getvalue()).decode("utf-8")

    return jsonify({
        'image': content
    })

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=int(os.environ.get('PORT', 8080)))