# Use official python image
FROM python:3.10

# Let statements and log messages immediately appear in cloud run logs
ENV PYTHONBUFFERED True

# Copy dependancies to container image
COPY requirements.txt ./

# Install production dependencies
RUN apt-get update
RUN pip install -r requirements.txt
# RUN apt-get install ffmpeg libsm6 libxext6  -y

# Install production dependencies
RUN pip install -r requirements.txt

# copy local code to container image
ENV APP_HOME /app
WORKDIR $APP_HOME
COPY . ./

# Run the service on startup
# use gunicorn webserver with once worker and 8 threads
# if you have more CPU cores, set number of workers to number of cores
# timout is set to 0 to disable timeouts of workers to allow cloud run to handle scaling.
CMD exec gunicorn --bind :$PORT --workers 1 --threads 8 --timeout 0 main:app