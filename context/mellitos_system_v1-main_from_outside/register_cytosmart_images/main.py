from google.cloud import storage, secretmanager
import json
import re
import sqlalchemy

storage_client = storage.Client()
secret_client = secretmanager.SecretManagerServiceClient()


image_table = sqlalchemy.table(
    "image",
    sqlalchemy.column("id"),
    sqlalchemy.column("source"),
    sqlalchemy.column("image_uri"),
    sqlalchemy.column("mask_uri"),
    sqlalchemy.column("well"),
    sqlalchemy.column("day"),
    sqlalchemy.column("image_set_id"),
    sqlalchemy.column("plate_id")
)


image_set_table = sqlalchemy.table(
    "image_set",
    sqlalchemy.column("id"),
    sqlalchemy.column("experiment_id"),
    sqlalchemy.column("scan_number"),
    sqlalchemy.column("experiment_name"),
    sqlalchemy.column("day"),
    sqlalchemy.column("number_of_wells"),
    sqlalchemy.column("pixels_per_mm"),
    sqlalchemy.column("plate_id")
)

plate_table = sqlalchemy.table(
  "plate",
  sqlalchemy.column("id"),
  sqlalchemy.column("serial")
)

def connect_unix_socket() -> sqlalchemy.engine.base.Engine:
    connection_secrets = json.loads(
        secret_client.access_secret_version(
            request={
                "name":"projects/1049074702976/secrets/mellitos-db-secret/versions/3"
            }
        ).payload.data.decode("UTF-8")
    )
    pool = sqlalchemy.create_engine(
        sqlalchemy.engine.url.URL.create(
            drivername="postgresql+pg8000",
            username=connection_secrets["DB_USER"],
            password=connection_secrets["DB_PASS"],
            database=connection_secrets["DB_NAME"],
            query={
                "unix_sock": "{}/.s.PGSQL.5432".format(
                    '/cloudsql/development-311316:us-east1:mellitos'
                )
            }
        )
    )
    return pool


def run(event, context):
    bucket = event["bucket"]
    name = event["name"]

    if name.endswith(".jpg"):
        blob = storage_client.bucket(bucket).blob(name)

        well = re.findall(re.compile('[A-Q]\d{1,2}'), blob.name)[0]

        image_set_stmt = sqlalchemy.select(image_set_table).where(
            image_set_table.c.experiment_id==blob.name.split('/')[0] and
            image_set_table.c.scan_number==int(blob.name.split('/')[1])
        )
        
        with connect_unix_socket().connect() as conn:
            image_set_results = list(conn.execute(image_set_stmt))

        if not image_set_results:
            raise Exception("no image set registered yet...")
        
        image_set_id = image_set_results[0][0]
        
        experiment_name = image_set_results[0][3]
        plate = re.findall(re.compile('\d{4}'), experiment_name)[0]
        day = int(re.findall(
            re.compile('Day\d{1,3}'),
            experiment_name
            )[0].lower().replace('day', '')
        )

        plate_select_stmt = (
            sqlalchemy.select(plate_table).
            where(
                plate_table.c.serial==plate
            )
        )

        with connect_unix_socket().connect() as conn:
            plate_results =  list(conn.execute(plate_select_stmt))

        if not plate_results:

            plate_insert_stmt = (
                sqlalchemy.insert(plate_table).
                values(
                    serial=plate
                )
            )
            with connect_unix_socket().connect() as conn:
                conn.execute(plate_insert_stmt)
                plate_results = list(conn.execute(plate_select_stmt))
        
        plate_id = plate_results[0][0]

        insert_image_stmt = (
            sqlalchemy.insert(image_table).
            values(
                source="cytosmart",
                image_uri=f"gs://{blob.bucket.name}/{blob.name}",
                well=well,
                day=day,
                image_set_id=image_set_id,
                plate_id=plate_id
            )
        )

        with connect_unix_socket().connect() as conn:
            conn.execute(insert_image_stmt)

    return

