from cellpose import models
from cellpose.io import imread
from google.cloud import storage, bigquery, compute_v1
import google.cloud.logging
import skimage
import random
import string
import os
import time
import numpy as np
import pandas as pd
import json
import sys
import time

model_bucket_name = "mellicell_ml_models"
model_name = "MCL_CP_005"
mask_bucket_name = "cytosmart_masks_mellicell"
dataset = "mellicell_image_data_v2"
project_id = "development-311316"
bq_agg_table = "cytosmart_measurements"
bq_cp_table = "cytosmart_lipid_measurments"
logging_client = google.cloud.logging.Client()
logger = logging_client.logger("cytosmart_analysis")


storage_client = storage.Client()
bq_client = bigquery.Client()

def q25(x):
    return x.quantile(0.25)

def q50(x):
    return x.quantile(0.5)

def q75(x):
    return x.quantile(0.75)

def ratio_big_to_all(x):
    x = x.values
    if pd.isna(x.max()) or pd.isna(x.sum()):
        return None
    else:
        return float(x.max())/float(x.sum())

def ratio_two_biggest(x):
    x = sorted(x.values, reverse=True)
    if len(x) > 1:
        return float(x[0])/float(x[1])
    else:
        return None

def make_foreground(mask):
    mask = mask.astype(bool)
    r0 = skimage.morphology.rectangle(20, 2)
    r90 = skimage.morphology.rectangle(2, 20)
    diamond = skimage.morphology.diamond(5)
    mask = skimage.morphology.binary_erosion(
        skimage.morphology.binary_dilation(
            skimage.morphology.binary_dilation(
                mask,
                r0
            ),
            r90
        ),
        diamond
    )
    mask = skimage.measure.label(mask)
    return mask

def segment(fname):
    image = imread(fname)
    model = models.CellposeModel(gpu=False, pretrained_model=model_name)
    mask, _, _ = model.eval(image)
    return mask

def measure(mask, metadata, objectURI):

    pixel_size = 1000/metadata['pixelsPerMm'] # 1000um/Npix (gives um per pix)
    fg_mask = make_foreground(mask)
    # make measurment of cellpose masks
    cp_df = pd.DataFrame(
        skimage.measure.regionprops_table(
            mask,
            properties=(
                'label',
                'area',
                'perimeter',
                'centroid'
            )
        )
    )

    cp_df.columns = [c.replace('-', '_') for c in cp_df.columns]
    cp_df['circularity'] = (4*np.pi*cp_df['area'])/(cp_df['perimeter']**2)
    # get label from foreground mask for cluster analysis
    clusters = cp_df[['centroid_0', 'centroid_1']].apply(
        lambda x: int(fg_mask[int(x[0]), int(x[1])]),
        axis=1
    )
    if len(clusters) > 0:
        cp_df['cluster'] = clusters
    else:
        cp_df['cluster'] = None

    cp_df['image'] = objectURI

    agg_df = cp_df.groupby(['cluster', 'image']).agg(
        {'area': ['count', 'mean', 'std',
                  'min', q25, q50, q75,
                  'max', ratio_two_biggest, ratio_big_to_all],
         'circularity': ['mean', 'std', 'min', 'max']
        }
    )

    agg_df_area = agg_df['area']
    agg_df_circ = agg_df['circularity']
    agg_df_area.columns = [f"{cname}_area" for cname in agg_df_area.columns]
    agg_df_circ.columns = [f"{cname}_circ" for cname in agg_df_circ.columns]
    agg_df = pd.merge(
        agg_df_area,
        agg_df_circ,
        left_index=True,
        right_index=True
    )
    agg_df = agg_df.reset_index()
    agg_df['image'] = objectURI
    del (agg_df_area)
    del (agg_df_circ)

    cp_df['area'] = cp_df['area']*(pixel_size**2)
    cp_df['perimeter'] = cp_df['perimeter']*pixel_size

    agg_df["mean_area"] = agg_df["mean_area"]*(pixel_size**2)
    agg_df["min_area"] = agg_df["min_area"]*(pixel_size**2)
    agg_df["q25_area"] = agg_df["q25_area"]*(pixel_size**2)
    agg_df["q50_area"] = agg_df["q50_area"]*(pixel_size**2)
    agg_df["q75_area"] = agg_df["q75_area"]*(pixel_size**2)
    agg_df["max_area"] = agg_df["max_area"]*(pixel_size**2)

    agg_df['experiment_id'] = metadata['experimentId']
    agg_df['scan_number'] = metadata['scanNumber']
    agg_df['experiment_name'] = metadata['experimentName']
    agg_df['plate_num_wells'] = metadata['numberOfWells']
    agg_df['pixels_per_mm'] = metadata['pixelsPerMm']

    agg_df = agg_df.astype(
        {
            "cluster":int,
            "mean_circ":float,
            "std_circ":float,
            "min_circ":float,
            "max_circ":float,
            "count_area":int,
            "mean_area":float,
            "std_area":float,
            "min_area":float,
            "q25_area":float,
            "q50_area":float,
            "q75_area":float,
            "max_area":float,
            "ratio_two_biggest_area":float,
            "ratio_big_to_all_area":float,
            "image":str,
            'experiment_id': str,
            'scan_number': int,
            'experiment_name': str,
            'plate_num_wells': int,
            'pixels_per_mm': int
        }
    )

    cp_df = cp_df.astype(
        {
            "label":int,
            "area":float,
            "perimeter":float,
            "centroid_0":float,
            "centroid_1":float,
            "cluster":int,
            "image":str
        }
    )

    return agg_df, cp_df
    
def save_to_bq(measurments):
    agg_df = measurments[0]
    cp_df = measurments[1]
    agg_records = json.loads(agg_df.to_json(orient = 'records'))
    cp_records = json.loads(cp_df.to_json(orient = 'records'))
    errors1 = bq_client.insert_rows_json(
        f"{project_id}.{dataset}.{bq_agg_table}",
        agg_records
    )
    errors2 = bq_client.insert_rows_json(
        f"{project_id}.{dataset}.{bq_cp_table}",
        cp_records
    )
    if errors1 == [] and errors2 == []:
        return None
    else:
        raise Exception("there was an error inserting into the bigquery tables")


def process(bucket_name, object_name):
    bucket = storage_client.bucket(bucket_name)
    object = bucket.blob(object_name)
    uuid = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
    fname = uuid + '_' + os.path.basename(object.name)
    mask_bucket = storage_client.bucket(mask_bucket_name)
    mask_object = mask_bucket.blob(object.name)

    image_details_blob_name = '/'.join(object.name.split('/')[:-2]) + "/scan_metadata.json"
    image_details_blob = bucket.blob(image_details_blob_name)
    
    for i in range(30):
        if not image_details_blob.exists():
            time.sleep(10)
        else:
            break
    else:
        raise Exception("scan_metadata.json file not found in bucket")

    image_details = json.loads(image_details_blob.download_as_string().decode('utf-8'))

    if mask_object.exists():
        raise Exception("mask already exists")
    
    object.download_to_filename(fname)
    time.sleep(1)

    if not os.path.exists(model_name):
        model_bucket = storage_client.bucket(model_bucket_name)
        model_object = model_bucket.blob(model_name)
        model_object.download_to_filename(model_name)

    mask = segment(fname)
    image = imread(fname)
    overlay = skimage.img_as_ubyte(skimage.color.label2rgb(mask, image))
    skimage.io.imsave("overlay.jpg", overlay)
    mask_object.upload_from_filename("overlay.jpg")
    # np.save(fname.replace(".jpg", ".npy"), mask)
    # mask_object.upload_from_filename(fname.replace(".jpg", ".npy"))
    measurments = measure(mask, image_details, 'gs://' + bucket.name + '/' + object.name)
    # csv_file_name = os.path.basename(object.name.replace('.jpg', '.csv'))
    # measurments.to_csv(csv_file_name, index=False)
    
    # csv_bucket = storage_client.bucket("mellicell_lab_bucket")
    # csv_blob = csv_bucket.blob(object.name.replace('.jpg', '.csv'))
    # print(f"uploading {csv_blob.name} to {csv_bucket.name}")
    # csv_blob.upload_from_filename(csv_file_name)
    os.remove(fname)
    # os.remove(csv_file_name)
    save_to_bq(measurments)

def delete_instance(project_id: str, zone: str, machine_name: str) -> None:
    """
    Send an instance deletion request to the Compute Engine API and wait for it to complete.

    Args:
        project_id: project ID or project number of the Cloud project you want to use.
        zone: name of the zone you want to use. For example: “us-west3-b”
        machine_name: name of the machine you want to delete.
    """
    instance_client = compute_v1.InstancesClient()

    print(f"Deleting {machine_name} from {zone}...")
    operation = instance_client.delete(
        project=project_id, zone=zone, instance=machine_name
    )
    print(f"Instance {machine_name} deleted.")
    return


if __name__ == '__main__':
    machine_name = sys.argv[1]
    zone = sys.argv[2]
    bucket_name = sys.argv[3]
    object_name = sys.argv[4]
    project_id = 'development-311316'
    try:
        logger.log_text(f"starting process")
        logger.log_struct(
            {
                "name": machine_name,
                "bucket": bucket_name,
                "object": object_name
            }
        )
        process(bucket_name, object_name)
    except Exception as e:
        print("there was an error")
        print(str(e))
        logger.log_text("there was an error")
        logger.log_struct(
            {
                "name": machine_name,
                "bucket": bucket_name,
                "object": object_name
            }
        )
    finally:
        if machine_name != 'na' and zone != 'na':
            delete_instance(project_id=project_id, zone=zone, machine_name=machine_name)


