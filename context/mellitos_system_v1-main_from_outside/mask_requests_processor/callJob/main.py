from google.api_core.extended_operation import ExtendedOperation
from google.cloud import compute_v1
from datetime import datetime
import sys
import os
from typing import Any

image_client = compute_v1.ImagesClient()

def wait_for_extended_operation(
    operation: ExtendedOperation, verbose_name: str = "operation", timeout: int = 300
) -> Any:
    """
    This method will wait for the extended (long-running) operation to
    complete. If the operation is successful, it will return its result.
    If the operation ends with an error, an exception will be raised.
    If there were any warnings during the execution of the operation
    they will be printed to sys.stderr.

    Args:
        operation: a long-running operation you want to wait on.
        verbose_name: (optional) a more verbose name of the operation,
            used only during error and warning reporting.
        timeout: how long (in seconds) to wait for operation to finish.
            If None, wait indefinitely.

    Returns:
        Whatever the operation.result() returns.

    Raises:
        This method will raise the exception received from `operation.exception()`
        or RuntimeError if there is no exception set, but there is an `error_code`
        set for the `operation`.

        In case of an operation taking longer than `timeout` seconds to complete,
        a `concurrent.futures.TimeoutError` will be raised.
    """
    result = operation.result(timeout=timeout)

    if operation.error_code:
        print(
            f"Error during {verbose_name}: [Code: {operation.error_code}]: {operation.error_message}",
            file=sys.stderr,
            flush=True,
        )
        print(f"Operation ID: {operation.name}", file=sys.stderr, flush=True)
        raise operation.exception() or RuntimeError(operation.error_message)

    if operation.warnings:
        print(f"Warnings during {verbose_name}:\n", file=sys.stderr, flush=True)
        for warning in operation.warnings:
            print(f" - {warning.code}: {warning.message}", file=sys.stderr, flush=True)

    return result

def main(event, context):
    bucket_name = event['bucket']
    object_name = event['name']

    startup_script = startup_script = open(
        os.path.join(
            os.path.dirname(__file__), 'startup-script.sh'), 'r').read()


    boot_disk = compute_v1.AttachedDisk()
    # source_image_name = image_client.get_from_family(project="cos-cloud", family="cos-101-lts").name
    initialize_params = compute_v1.AttachedDiskInitializeParams()
    initialize_params.source_image = 'projects/development-311316/global/machineImages/cytosmart-analysis-vm-image'
    initialize_params.disk_size_gb = 25
    initialize_params.disk_type = 'zones/us-east1-b/diskTypes/pd-balanced'
    boot_disk.initialize_params = initialize_params
    boot_disk.auto_delete = True
    boot_disk.boot = True

    instance_client = compute_v1.InstancesClient()
    network_interface = compute_v1.NetworkInterface()
    network_interface.name = 'global/networks/default'

    access = compute_v1.AccessConfig()
    access.type_ = compute_v1.AccessConfig.Type.ONE_TO_ONE_NAT.name
    access.name = "External NAT"
    access.network_tier = access.NetworkTier.PREMIUM.name
    network_interface.access_configs = [access]

    instance = compute_v1.Instance()
    instance.network_interfaces = [network_interface]
    instance.name = "cytosmart-image-analysis-instance" + str(datetime.now().timestamp()).replace('.', '-')
    instance.disks = [boot_disk]
    instance.machine_type = f"projects/development-311316/zones/us-east1-b/machineTypes/e2-standard-4"
    instance.service_accounts = [
        compute_v1.ServiceAccount(
            email="<EMAIL>",
            scopes=[
                "https://www.googleapis.com/auth/cloud-platform"
            ]
        )
    ]
    instance.metadata = compute_v1.Metadata(
        items=[
            compute_v1.Items(
                key='startup-script',
                value=startup_script
            ),
            compute_v1.Items(
                key='bucket',
                value=bucket_name
            ),
            compute_v1.Items(
                key='image_name',
                value=object_name
            )
        ]
    )

    request = compute_v1.InsertInstanceRequest()
    request.zone = 'us-east1-b'
    request.project = 'development-311316'
    request.instance_resource = instance

    print(f"creating the {instance.name} in zone {request.zone}")

    operation = instance_client.insert(request=request)

    wait_for_extended_operation(operation, "instance creation")

    print(f"Instance {instance.name} created")

    return
