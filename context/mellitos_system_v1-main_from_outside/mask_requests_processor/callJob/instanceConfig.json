{"canIpForward": false, "confidentialInstanceConfig": {"enableConfidentialCompute": false}, "deletionProtection": false, "description": "", "disks": [{"autoDelete": true, "boot": true, "deviceName": "my-instance-name", "diskEncryptionKey": {}, "initializeParams": {"diskSizeGb": "25", "diskType": "projects/development-311316/zones/us-central1-a/diskTypes/pd-balanced", "labels": {}, "sourceImage": "projects/cos-cloud/global/images/cos-101-17162-40-42"}, "mode": "READ_WRITE", "type": "PERSISTENT"}], "displayDevice": {"enableDisplay": false}, "guestAccelerators": [], "keyRevocationActionType": "NONE", "labels": {}, "machineType": "projects/development-311316/zones/us-central1-a/machineTypes/e2-standard-4", "metadata": {"items": [{"key": "startup-script", "value": "gcp_zone=$(curl -H Metadata-Flavor:Google http://metadata.google.internal/computeMetadata/v1/instance/zone -s | cut -d/ -f4)\ndocker-credential-gcr configure-docker\ndocker run --rm gcr.io/development-311316/cytosmart-segment-service-v2:self-delete $(hostname) ${gcp_zone} cytosmart_brightfield_images_mellicell 0436c64c-cc47-428e-8873-b37a3197510c/**********/A12/A12_export.jpg"}]}, "name": "my-instance-name", "networkInterfaces": [{"accessConfigs": [{"name": "External NAT", "networkTier": "PREMIUM"}], "stackType": "IPV4_ONLY", "subnetwork": "projects/development-311316/regions/us-central1/subnetworks/default"}], "params": {"resourceManagerTags": {}}, "reservationAffinity": {"consumeReservationType": "ANY_RESERVATION"}, "scheduling": {"automaticRestart": true, "onHostMaintenance": "MIGRATE", "provisioningModel": "STANDARD"}, "serviceAccounts": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shieldedInstanceConfig": {"enableIntegrityMonitoring": true, "enableSecureBoot": false, "enableVtpm": true}, "tags": {"items": ["http-server"]}, "zone": "projects/development-311316/zones/us-central1-a"}