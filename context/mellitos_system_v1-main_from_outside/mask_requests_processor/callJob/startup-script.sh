#! /bin/bash
sudo -u worker bash -c \
'gcp_zone=$(curl -H Metadata-Flavor:Google http://metadata.google.internal/computeMetadata/v1/instance/zone -s | cut -d/ -f4);
bucket=$(curl http://metadata/computeMetadata/v1/instance/attributes/bucket -H Metadata-Flavor:Google);
image_name=$(curl http://metadata/computeMetadata/v1/instance/attributes/image_name -H Metadata-Flavor:Google);
docker-credential-gcr configure-docker;
docker run --rm gcr.io/development-311316/cytosmart-segment-service-v2:two-tables $(hostname) $gcp_zone $bucket $image_name;'
