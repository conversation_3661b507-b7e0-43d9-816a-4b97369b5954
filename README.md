# MellitOS - The MelliCell Operating System

## Web App Development
To run locally, use the following steps

### set environment variables
These should be the same as the ones in `docker-compose.yaml`. Put this in file `.env`:
```
DBHOST=0.0.0.0
DBPORT=5432
DBUSER=app
DBPASS=pass
DBNAME=app
DRIVERNAME=postgresql+pg8000
PORT=5001
```
then
```
source .env
```

### Run the local database (in one window)
```
docker compose up db
```
This will get the database running locally. Data will be maintained from session to session, 
see below on resetting the database after data gets corrupted or the schema changes.

### Activate Python environment and install the app packages (in another window)
```
python3 -m venv venv
. venv/bin/activate
pip install -r requirements.txt
pip install -e .
```

##Run the web app
```
uvicorn app.main:app --host=0.0.0.0 --port=5003 --reload
```
Open browser at 
```
http://0.0.0.0:5003/
```
The `--reload` flag in conjunction with the `install -e` makes it possible to simply save the source and reload the browser page to see changes, immediately. That can speed up development substantially.

A script `sh/run` is provided that executes all steps needed to run the local server except for the one-time `pip` installs.

## Reset database to empty

Go back to the other window:
```
^C
rm -rf pgdata
docker compose up db
```
This is really only suitable in early stage development. The preferred method is resetting the database to the state of the production database, see below.

## Reset the database to the production version

As we experiment with the application locally, it will become messy and we want to reset it to reflect the data in production. For this, we have the `sh/reset` script, which can be used as follows:
```
source sh/reset
```
Beware that the contents of the local database will be lost in this process!

Before this works, we need to 
* install the cloud-sql-proxy (see below)
* set up credentials for database passwords: put passwords in a `.secrets/secrets.json` (see below) file.
* Have the local database running (`docker compose up db`).
* Make sure there is no client connected to the local database. Sometimes, it is necessary to restart it.


## Alembic Migrations

When it's time to make a schema change, we use Alembic to generate a migration script. The migration script will be generated in the `migrations/versions` directory. It will be a Python script that contains the changes to the database schema. It will be named something like `f3b3e4e5e6f7_add_column_to_table.py`. The script will contain a `upgrade` function that contains the changes to the database schema, and a `downgrade` function that contains the changes to revert the database schema. The `upgrade` function will be run when the migration is applied, and the `downgrade` function will be run when the migration is reverted.

### Create a new migration
```
alembic upgrade head   # Bring database up to date
alembic revision --autogenerate -m "Some comment here"
```
Then, we need to check it in:
```git add migrations/versions/a5c...```
Then we edit it until it's right. We can also run it to see if it works:
```
alembic upgrade head
```

## Secrets Configuration

The application requires a `secrets.json` file in the `.secrets` directory. 

The secrets.json structure looks like this:
```json
{
  "database": {
    "local": {
      "user": "app",
      "password": "pass",
      "name": "app"
    },
    "development": {
      "user": "<your-dev-db-user>",
      "password": "<your-dev-db-password>",
      "name": "<your-dev-db-name>"
    },
    "production": {
      "user": "<your-prod-db-user>",
      "password": "<your-prod-db-password>",
      "name": "<your-prod-db-name>"
    }
  },
  "google": {
    "development": {
      // Copy contents from your development.json
    },
    "production": {
      // Copy contents from your production credentials
    }
  },
  "auth": {
    "secret_key": "<your-secret-key>"
  }
}
```

## Run locally with Cloud SQL Proxy

When things are working well in the local database, we can also run the app on the development database. We need to run a proxy to connect to the Cloud SQL database. The proxy is a separate process that runs in the background and listens on a local port. The app connects to the local port, and the proxy forwards the connection to the Cloud SQL database.
```
./cloud-sql-proxy development-311316:us-east1:mellitos-dev
```
Since we can just reset the local database to be identical with the cloud database using `sh/reset`, this will not be needed often, but it can come in handy when we want to make changes to the development database using new code that is not yet released. With great care, obviously. 

The cloud-sql-proxy is a binary that can be downloaded from the Google Cloud website. It is not included in this repository. The `sh/reset` script also uses this proxy. 

The installation varies by OS, according to instructions found here: 
https://cloud.google.com/sql/docs/mysql/connect-instance-auth-proxy

For Mac:
```
curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.14.0/cloud-sql-proxy.darwin.amd64
chmod +x cloud-sql-proxy
```

The cloud proxy will require authentication credentials. Those can be set up with gcloud:
```
gcloud auth application-default login
```

## Run in Docker container

Before deployment it is best to see if the app runs well inside a container. Take the following steps:
```
source sh\build
docker compose up db app
```
This will build the app container and start it on the local database.

## Deploy container to Cloud Run

Set up authentication, just once:
```
gcloud auth configure-docker us-east1-docker.pkg.dev
```
Build and upload the container:
```
source sh/upload
```
Then, some work on the Cloud Run console. 
- Configure the Cloud SQL service with a private IP and a VPC network
- Create a service, like `mellitos-dev`
- Edit & Deploy New Versions -> Variables and secrets
  - add the environment variables from the `.env` or compose yaml file
  - add the secrets from the `.secrets` directory (details tbd)
  - Under Network, select the VPC network for outbound connections, same network as the Cloud SQL service
  - Make sure the environment variable DBHOST is set to the private IP of the Cloud SQL service

This needs to be set up just once. After that, the sequence is 
- Edit & Deploy New Versions -> Deploy
- Container Image URL -> SELECT -> select the image from the repository
- The image we just uploaded (seee above) is under .../dev-repo -> mellitos-dev

For deployment, secrets are mounted as RAM volumes in the container, and the `.secrets` file is not included in the repository. Secrets cannot be autogenerated in the deployed container, so they must be provided and updated manually through the service configuration. The compose `docker-compose.yaml` file does this when running local containers, but it needs to be configured manually in the Cloud Run console.

## Upgrade server and Cloud SQL database
When changes have been made to the database, the server needs to be upgraded to reflect the changes. To do this, we start the cloud proxy:
```
./cloud-sql-proxy development-311316:us-east1:mellitos-dev
```
Then we run the alembic upgrade command:
```
alembic upgrade head
```
The we can deploy the new version of the server to Cloud Run, see above.

## Install Java VM
Mellitos needs a Java VM to run bioformats to read the Olympus VSI image format. On a Mac, the Java VM can be installed with the following commands:
```
brew install openjdk
sudo ln -sfn /opt/homebrew/opt/openjdk/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk.jdk
java_home -v
```

## Remote editing
For some purposes, it is better to run code on a remote machine, but edit it locally. This can be done with the following steps:
  - Set up a VM on Google Cloud Engine, with git and the same configurations as described above, using `sh/vm-init`
  - Set up an SSH key for the remote machine, so that `ssh user@remote` works without a password
  - Install the `remote-ssh` extension in VS Code ([link](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh))
  - Connect to the remote machine with the extension as described in the [documentation](https://code.visualstudio.com/docs/remote/ssh)

You can then check out code with git on the remote machine, edit it in VS Code, and run it remotely. This can be useful for debugging, profiling, or running code that requires a lot of resources and benefits from faster access to the cloud.
