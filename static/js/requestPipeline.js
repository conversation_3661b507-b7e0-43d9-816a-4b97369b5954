const plate_select = document.getElementById("plate")
const deviceSelect = document.getElementById("device")
const imageSet = document.getElementById("imageset")
const pipelineSelect = document.getElementById("pipeline")
const versionSelect = document.getElementById("version")

deviceSelect.addEventListener("change", async e => {
    imageSet.innerHTML = '';
    if (plate_select.value.length < 4) {
        return
    }
    const resp = await fetch('/imaging/api/imageset/search', {
        method: "POST",
        header: {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        body: `{\"plateName\": \"${plate_select.value}\",\"device\":\"${e.target.value}\"}`
     })
     const jsonResp = await resp.json();
     jsonResp["results"].forEach(el => {
         const opt = document.createElement("option")
         opt.text = el["bt_key"]
         opt.value = el["id"]
         imageSet.appendChild(opt)
     })
 })
    
pipelineSelect.addEventListener("change", async e => {
    versionSelect.innerHTML = '';
    const resp = await fetch("/imaging/api/pipelines/versions", {
        method: "POST",
        header: {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        body: `{\"pipelineId\": \"${e.target.value}\"}`
    });
    const versions = await resp.json().then(data => data["data"]["versions"])
    versions.forEach(el => {
        const opt = document.createElement("option")
        opt.setAttribute("value", el["id"])
        opt.text = el["version"]
        versionSelect.appendChild(opt);
    })
})



plate_select.addEventListener("input", async e => {
    const parentEl = e.target.parentElement;
    const name = e.target.value;
    if (name.length < 3) {
        return;
    }
    let response = await fetch("/treatments/plates", {
        method: "POST",
        header: {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        body: `{\"name\": \"${name}\"}`
    });
    let jsonResponse = await response.json();
    let results = jsonResponse["results"];
    e.target.setAttribute("autoComplete", "on");
    e.target.setAttribute("list", "plate-datalist");

    let dlEl = document.getElementById("plate-datalist");
    if (dlEl) {
        dlEl.remove();
    }

    const dl = document.createElement("datalist");
    dl.setAttribute("id", "plate-datalist");
    results.forEach(el => {
        const opt = document.createElement("option");
        opt.text = el['name'];
        dl.appendChild(opt);
    })
    parentEl.appendChild(dl);

});


plate_select.addEventListener("change", async e => {
    imageSet.innerHTML = ''
    if (e.target.value.length < 4) {
        return
    }
    const resp = await fetch('/imaging/api/imageset/search', {
        method: "POST",
        header: {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        body: `{\"plateName\": \"${e.target.value}\",\"device\":\"${deviceSelect.value}\"}`
     })
     const jsonResp = await resp.json();
     jsonResp["results"].forEach(el => {
         const opt = document.createElement("option")
         opt.text = el["bt_key"]
         opt.value = el["id"]
         imageSet.appendChild(opt)
     })
 })
