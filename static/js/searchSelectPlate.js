const plate_select = document.getElementById("plate")
plate_select.addEventListener("input", async e => {
    const parentEl = e.target.parentElement;
    const name = e.target.value;
    if (name.length < 3) {
        return;
    }
    let response = await fetch("/treatments/plates", {
        method: "POST",
        header: {
            "Accept": "application/json",
            "Content-Type": "application/json"
        },
        body: `{\"name\": \"${name}\"}`
    });
    let jsonResponse = await response.json();
    let results = jsonResponse["results"];
    e.target.setAttribute("autoComplete", "on");
    e.target.setAttribute("list", "plate-datalist");

    let dlEl = document.getElementById("plate-datalist");
    if (dlEl) {
        dlEl.remove();
    }

    const dl = document.createElement("datalist");
    dl.setAttribute("id", "plate-datalist");
    results.forEach(el => {
        const opt = document.createElement("option");
        opt.text = el['name'];
        dl.appendChild(opt);
    })
    parentEl.appendChild(dl);

});
