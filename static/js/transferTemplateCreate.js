const mainDiv = document.getElementById("main");

const form = document.createElement("form");
form.setAttribute("method", "post");

const create_well_forms = function(format) {
    let max_rows;
    let max_cols;
    form.innerHTML = "";
    
    const selectDiv = document.createElement("div")
    const select = document.createElement("select");
    select.setAttribute("name", "plate-format");
    select.setAttribute("id", "plate-format");
    const format384 = document.createElement("option");
    format384.setAttribute("value", "384-well");
    format384.innerText = "384-well";
    const format96 = document.createElement("option");
    format96.setAttribute("value", "96-well");
    format96.innerText = "96-well";
    select.appendChild(format384);
    select.appendChild(format96);
    selectDiv.appendChild(select);
    
    if (format == "384-well") {
        max_rows = 16;
        max_cols = 24;
        select.value = "384-well"
    } else if (format == "96-well") {
        max_rows = 8;
        max_cols = 12;
        select.value = "96-well"
    }
    
    select.addEventListener("change", (event) => {
        create_well_forms(event.target.value);
    });
    form.append(selectDiv);
    for (let row = 0; row < max_rows; row++) {
        const code = 65 + row;
        const row_name = String.fromCharCode(code);
        for (let col = 1; col < max_cols + 1; col++) {
            const wellName = row_name + col;
            // const inputDiv = document.createElement("div");
            // const wellInputLabel = document.createElement("label");
            // wellInputLabel.setAttribute("for", wellName);
            // wellInputLabel.innerText = wellName;
            const wellInput = document.createElement("input");
            wellInput.setAttribute("id", wellName);
            wellInput.setAttribute("type", "hidden");
            wellInput.setAttribute("name", "wellName");
            // inputDiv.appendChild(wellInputLabel);
            // inputDiv.appendChild(wellInput);
            form.appendChild(wellInput);// inputDiv);
        }
    }
    const submit = document.createElement("input");
    submit.setAttribute("type", "submit");
    submit.setAttribute("value", "Submit");
    form.appendChild(submit);
    
}
create_well_forms("384-well");
mainDiv.appendChild(form);


