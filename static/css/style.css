body {
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
}

div.main {
    margin-left: 20%;
    padding: 1px 16px;
}

div.navbar {
    margin: 0;
    padding: 0;
    width: 20%;
    background-color: #f1f1f1;
    position: fixed;
    height: 100%;
    overflow: auto;
}

div.navbar ul {
    list-style-type: none;
}

div.navbar summary {
    display: block;
    color: #000;
    padding: 8px 8px;
    text-decoration: none;
    font-weight: bold;
}

div.navbar summary:hover {
    background-color: #555;
    color: white;
}

div.navbar li a {
    display: block;
    color: #000;
    padding: 4px 16px;
    text-decoration: none;
}

div.navbar li a.active {
    background-color: #04AA6D;
    color: white;
}

div.navbar li a:hover:not(.active) {
    background-color: #555;
    color: white;
}

form table td.sub {
    text-align: center;
    padding: 2px 4px;
}

form table {
    background-color: #f1f1f1;
    padding: 4px;
}

form table td.label0 {
    text-align: right;
}

table tr[status="pending"] {
    color: lightgray;
}

table tr[status="overdue"] {
    color: darkred;
    font-weight: bold;
}



.plate-collection {
    display: grid;
    grid-template-columns: repeat(1, minmax(600px, 1fr));
    grid-auto-rows: minmax(10px, auto);
    gap: 50px;
    width: 60%;
    padding-left: 10%;
}

.plate-container {
    /* display: flex; */
    width: 1fr;
    /* aspect-ratio: 3 / 2; */
}

.plate {
    background-color: black;
}


.well-row-384-well {
    width: 100%;
    height: 6.25%;
    font: 0/0 a;
}

.well-row-384-well > .well {
    display: inline-block;
    vertical-align: middle;
    margin: 0.1%;
    width: 3.95%;
    padding-bottom: 4%;
}

.well-row-96-well {
    width: 100%;
    height: 12.5%;
    font: 0/0 a;
}

.well-row-96-well > .well {
    display: inline-block;
    vertical-align: middle;
    margin: 0.2%;
    width: 7.9%;
    padding-bottom: 8%;
}



.plate2 {
    background-color: white;
    display: grid; 
    gap: 2px;
    padding: 2px;
    grid-auto-rows: minmax(1.2em, auto);
    width: 100%;
    max-width: 40em;
}

.wells6 {
    grid-template-columns: repeat(3, 5em);
}

.wells12 {
    grid-template-columns: repeat(4, 5em);
}

.wells96 {
    grid-template-columns: repeat(12, 3em);
}

.wells384 {
    grid-template-columns: repeat(24, 1fr);
}

.wells-6 {
    grid-template-columns: repeat(4, 5em);
}

.wells-12 {
    grid-template-columns: repeat(5, 5em);
}

.wells-96 {
    grid-template-columns: repeat(13, 3em);
}

.wells-384 {
    grid-template-columns: repeat(25, 1fr);
}

.well {
    width: 1fr;
    height: 1fr;
    display: grid;
    background-color: blue;
    text-align: center;
    align-items: center;
    border-radius: 50%;
    font-size: 0.5vw;
}

.well2 {
    display: grid;
    justify-content: center;
    align-items: center;
    background-color: lightgray;
    /* font-size: 1.5vw; */
    aspect-ratio: 1;
}

.well2.label {
    background-color: white;
}

.well2[title$="Unmapped"] {
    background-color: hsl(0, 0%, 95%);
}

.well2[qc="good"] {
    background-color: hsl(121, 100%, 89%);
}

.well2[qc="bad"] {
    background-color: hsl(0, 100%, 84%);
}

.well2 p {
    margin: 0;
    font-size: 1vw;
}

.concentration-text {
    visibility: hidden;
    padding: 0.25em 0.5em;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 0.25em;
    white-space; nowrap;
    
    position: absolute;
    z-index: 1;
    top: 100%;
    left: 100%;
    transition-property: visibility;
    transition-delay: 0s;
}



.group0 {
    background-color: ghostwhite;
}

.group1 {
    background-color: rgb(255, 0, 0);
}

.group2 {
    background-color: rgb(250, 0, 254);
}

.group3 {
    background-color: rgb(251, 125, 0);
}

.group4 {
    background-color: rgb(176, 10, 227);
}

.group5 {
    background-color: rgb(238, 255, 0);
}

.group6 {
    background-color: rgb(8, 76, 235);
}

.group7 {
    background-color: rgb(142, 251, 0);
}

.group8 {
    background-color: rgb(8, 86, 196);
}

.group9 {
    background-color: rgb(255, 0, 0);
}

.group10 {
    background-color: rgb(250, 0, 254);
}

.group11 {
    background-color: rgb(251, 125, 0);
}

.group12 {
    background-color: rgb(176, 10, 227);
}

.group13 {
    background-color: rgb(238, 255, 0);
}

.group14 {
    background-color: rgb(8, 76, 235);
}

.group15 {
    background-color: rgb(142, 251, 0);
}

.group16 {
    background-color: rgb(8, 86, 196);
}

.group17 {
    background-color: rgb(255, 0, 0);
}

.group18 {
    background-color: rgb(250, 0, 254);
}

.group19 {
    background-color: rgb(251, 125, 0);
}

.group20 {
    background-color: rgb(176, 10, 227);
}

.group21 {
    background-color: rgb(238, 255, 0);
}

.group22 {
    background-color: rgb(8, 76, 235);
}

.group23 {
    background-color: rgb(142, 251, 0);
}

.group24 {
    background-color: rgb(8, 86, 196);
}

.clicked {
    background-color: crimson;
}


.well:hover .concentration-text {
    visibility: visible;
    transition-delay: 0.3s;
}

@media screen and (max-width: 500px) {
    .concentration-text {
        display: none;
    }

    .plate-collection {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        grid-auto-rows: minmax(10px, auto);
        gap: 50px;
        width: 100%;
    }
}
