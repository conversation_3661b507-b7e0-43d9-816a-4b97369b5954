--  Fix failed plates for feeding - Jul 16 2024
select * from result where id = 248;
update result set result='pass' where id = 248;

select * from request where id = 728;

select * from plate_map;
select * from map_group;
update map_group set group_type='standard' where plate_map_id in (6, 7, 8) and group_type = 'positive';

select * from upload_out;
select * from image_set order by id;
select image_set, count(*) from image group by image_set;
select * from image where image_set = 210;

SELECT request.id, request.project, request.step, request.created, request.schedule, request.deadline, request.comment, request.action, result.id AS id_1, result.ended, result.result, result.comment AS comment_1 
FROM request, result 
WHERE request.step = 'upload' AND request.action = result.id AND result.result = 'pass' AND 
NOT (EXISTS (SELECT 1 
FROM upload_in 
WHERE request.id = upload_in.image_req));

SELECT request.id, request.project, request.step, request.created, request.schedule, request.deadline, request.comment, request.action, result.id AS id_1, result.ended, result.result, result.comment AS comment_1 
FROM request, result 
WHERE request.step = 'upload' AND request.action = result.id AND result.result = 'pass';
