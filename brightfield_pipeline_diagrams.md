# Brightfield Pipeline Architecture Diagrams

## 1. Legacy Olympus Pipeline

```mermaid
flowchart TD
    A[Lab PC<br/>Olympus IX83] -->|Windows Service<br/>OlympusUploadService| B[ix83-raw-files<br/>GCS Bucket]
    B -->|PubSub Message<br/>ix83-image-uploaded| C[Airflow DAG<br/>IX83 Conversion]
    C -->|PubSub Message<br/>convert-ix83-image| D[ImageConvertWorker<br/>MIG]
    D -->|VSI → TIFF<br/>Conversion| E[ix83-tiff-files<br/>GCS Bucket]
    E -->|PubSub Message<br/>ix83-tiff-uploaded| F[Airflow DAG<br/>Segmentation]
    F -->|Database Insert<br/>+ PubSub Message| G[ImageSegmentWorker<br/>MIG]
    G -->|Cellpose<br/>Segmentation| H[Mask Files<br/>GCS Storage]
    H -->|PubSub Message<br/>mask-uploaded| I[Airflow DAG<br/>Measure Masks]
    I -->|Database Insert<br/>+ PubSub Message| J[MeasureMaskWorker<br/>MIG]
    J -->|Measurements| K[CSV Files<br/>ix83-brightfield-measurements]
    K -->|External Tables| L[BigQuery<br/>Analysis]
    
    style A fill:#ffcccc
    style D fill:#cce5ff
    style G fill:#cce5ff
    style J fill:#cce5ff
    style L fill:#ccffcc
```

**Key Issues:**
- ❌ **No active brightfield analysis** for cells/clusters
- ❌ **Multiple separate repositories** - hard to maintain
- ❌ **Complex orchestration** with Airflow DAGs
- ❌ **Data gap** when Cytosmart imager unavailable

## 2. Legacy Cytosmart Pipeline

```mermaid
flowchart TD
    A[Cytosmart Omni<br/>Imager] -->|Custom Software<br/>Export Button| B[cytosmart_brightfield_images_mellicell<br/>GCS Bucket]
    B -->|Cloud Function<br/>create-omni-worker-function| C[BF Cytosmart Analysis<br/>Docker Container]
    C -->|Cellpose Model<br/>MCL_CP_005| D[Cell Segmentation<br/>& Measurement]
    D -->|Direct Write| E[BigQuery Tables<br/>cytosmart_lipid_measurements<br/>cytosmart_measurements]
    D -->|Also writes to| F[mellitos Database<br/>development-311316]
    
    style A fill:#ffcccc
    style C fill:#ffffcc
    style E fill:#ccffcc
    style F fill:#ccffcc
```

**Key Features:**
- ✅ **Active brightfield analysis** with cell/cluster counting
- ✅ **Automated processing** via cloud functions
- ❌ **Separate codebase** outside MellitOS
- ❌ **BigQuery storage** (legacy approach)

## 3. Consolidated Brightfield Pipeline (New)

```mermaid
flowchart TD
    A[Image Sources] --> B[Repository Pattern<br/>Unified Interface]
    A1[Cytosmart Omni<br/>cytosmart_brightfield_images] --> B
    A2[Olympus IX83<br/>mellitos-lab-data] --> B
    A3[Olympus TIFF<br/>ix83-tiff-files] --> B
    
    B --> C[MellitOS Framework<br/>src/images/]
    C --> D[BrightfieldAnalysis<br/>Class]
    D --> E[Image Loading<br/>Repository Pattern]
    E --> F[Cellpose Segmentation<br/>MCL_CP_005 Model]
    F --> G[Measurement Pipeline<br/>Individual + Aggregated]
    G --> H[CSV Output<br/>Cloud Storage]
    H --> I[BigQuery External Tables<br/>Analysis Ready]
    
    J[Pipeline Bot<br/>Automatic Matching] --> C
    K[Workflow Integration<br/>Database Models] --> C
    
    style B fill:#e6f3ff
    style C fill:#e6ffe6
    style D fill:#fff2e6
    style H fill:#ccffcc
    style I fill:#ccffcc
```

**Key Benefits:**
- ✅ **Unified codebase** within MellitOS
- ✅ **Repository pattern** - works with any image source
- ✅ **Numerical equivalency** with existing Cytosmart pipeline
- ✅ **Modern storage** - CSV files in Cloud Storage
- ✅ **Framework integration** - database models, workflows
- ✅ **Fills Olympus gap** - brightfield analysis for all sources

## 4. Architecture Comparison

```mermaid
graph LR
    subgraph "Legacy Approach"
        A1[Olympus Pipeline<br/>❌ No BF Analysis]
        A2[Cytosmart Pipeline<br/>✅ BF Analysis<br/>❌ Separate Repo]
        A3[Multiple Codebases<br/>❌ Hard to Maintain]
    end
    
    subgraph "Consolidated Approach"
        B1[Single Pipeline<br/>✅ All Image Sources]
        B2[MellitOS Integration<br/>✅ Unified Framework]
        B3[Repository Pattern<br/>✅ Source Agnostic]
        B4[Modern Storage<br/>✅ CSV + External Tables]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    
    style A1 fill:#ffcccc
    style A2 fill:#ffffcc
    style A3 fill:#ffcccc
    style B1 fill:#ccffcc
    style B2 fill:#ccffcc
    style B3 fill:#ccffcc
    style B4 fill:#ccffcc
```

## Key Presentation Points

### Problem Statement
- **Data Gap**: Olympus images lack brightfield analysis
- **Code Fragmentation**: Multiple repositories hard to maintain
- **Storage Inconsistency**: Mix of BigQuery direct writes and CSV files

### Solution Benefits
- **Centralization**: All brightfield code in MellitOS repository
- **Consistency**: Same analysis for all image sources
- **Maintainability**: Single codebase, unified patterns
- **Scalability**: Framework-based approach supports future expansion

### Technical Achievements
- **Numerical Equivalency**: Exact same results as legacy Cytosmart pipeline
- **Repository Pattern**: Clean abstraction over different image sources
- **Modern Architecture**: CSV storage with BigQuery external tables
- **Framework Integration**: Leverages existing MellitOS infrastructure
