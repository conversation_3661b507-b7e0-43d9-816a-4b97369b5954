from base.reps import rep
from plates.model import Plate, Donor, Medium, PlateFormat, Vendor
from images.model import Device
from workflow.orm.steps import SeedIn, FeedIn, ImageIn, ExtractIn, DiluteIn, AssayIn
from workflow.assay import ExtractResult, ExtractStep, DiluteStep, AssayStep
from base.util import make_logger
log = make_logger(__name__)

def reps1():
    rp = rep(DiluteIn)
    rs = rp.fields.values()
    for r in rs:
        log(r)
    log(rp)

r = ExtractResult(action_id=0, comment='comment', result='pass', barcode=0, plate_vendor=Vendor(name='name'))
log(rep(ExtractResult))
log(r)
