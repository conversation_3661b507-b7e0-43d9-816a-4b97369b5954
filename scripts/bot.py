import logging
import asyncio

from base.database import get_db
from bots.upload import UploadBot
from images.parse import java_vm
from images.repo import ImageInfo

log = logging.getLogger(__name__)
#
# Backfill data from the image repository
#

# Obtain a database connection
db = get_db()

bot = UploadBot(db)

with java_vm():
    # run the bot until all work is done
    asyncio.run(bot.run(once=False)) 
    # run the bot on just one new task
    # asyncio.run(bot.run(once=True, complete=False))
    # run a specific task (by action_id)
    # asyncio.run(bot.sim_one(812))
