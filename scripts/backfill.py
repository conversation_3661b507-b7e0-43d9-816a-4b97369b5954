from datetime import datetime, <PERSON>elta
import statistics
from typing import List, NamedTuple
import pytz
from dataclasses import dataclass

from images.parse import java_vm
from plates.model import Plate, PlateFormat, PlateType, Vendor, Donor, PlateMap
from images.model import ImageSet, Image, Device
from workflow.orm.rar import Action, Request, Result
from workflow.orm.steps import ImageIn, SeedIn, UploadIn, UploadOut
from base.database import Base, get_db, transact
from images.repo import PlateImageSets
from images.match import ImageMatcher
from base.util import make_logger, sform, tform
from base.sheets import get_sheet_data, match_heads
from qc.data import parse_plate_no, plate_days
from images.repo import Repo
from bots.upload import UploadBot
from workflow.flow import BusyActions
from workflow.culture import SeedStep, ImageStep, UploadStep

log = make_logger(__name__)

#
# Backfill data from the image repository
#
# Obtain a database connection
db = get_db() 
    
def fill_sizes(repo_name: str = "olympus", doit: bool = False):
    """Find and fix image size database errors"""
    repo = Repo(repo_name)
    image_mismatch = 0
    iset_mismatch = 0
    no_plate = 0
    no_rims = 0
    for pims in repo.image_sets.all_plates(): # [repo.plate("Plate0176")]: # 
        log("")
        [pn] = pims.plate_names
        log(f"PLATE: {pn}")
        plate = db.query(Plate).filter(Plate.name == pn).first()
        if plate:
            m = ImageMatcher(db, plate, [repo])
            # get unlinked image sets
            for ims in m.imss:
                rims = m.repo_map.get(ims.bt_key)
                if not rims:
                    log(f"  No repo images for {ims}")
                    no_rims += 1
                    continue
                rims_x = 0
                rims_y = 0
                for im in ims.images:
                    rwell = rims.wells.get(im.wellPos.well_name)
                    rim = rwell.default_image()
                    if im.size_x != rim.info.width or im.size_y != rim.info.height:
                        log(f"  IMAGE: {im.wellPos.well_name} {im.size_x}x{im.size_y} != {rim.info.width}x{rim.info.height}")
                        image_mismatch += 1
                    if rim.info.width > rims_x: rims_x = rim.info.width
                    if rim.info.height > rims_y: rims_y = rim.info.height
                if ims.size_x != rims_x or ims.size_y != rims_y:
                    log(f"   ISET: {ims} {ims.size_x}x{ims.size_y} != {rims_x}x{rims_y}")
                    iset_mismatch += 1
                    if doit:
                        with transact(db):
                            ims.size_x = rims_x
                            ims.size_y = rims_y
        else:
            no_plate += 1
            log(f"  Plate {pn} not found.")
    log(f"Found{' and fixed' if doit else ''} {image_mismatch} mismatching images.")
    log(f"Found{' and fixed' if doit else ''} {iset_mismatch} mismatching sets.")
    log(f"Found {no_plate} plates not in database.")
    log(f"Found {no_rims} image sets without repository images.")
    log("################################################################")
    
def fill_images(repo_name: str = "olympus", doit: bool = False):
    """Find image sets with missing images and add them"""
    repo = Repo(repo_name)
    missing_wells = 0
    not_in_db = 0
    for pims in repo.image_sets.all_plates(): # [repo.plate("Plate0212")]: #
        log("")
        [pn] = pims.plate_names
        log(f"PLATE: {pn}")
        plate = db.query(Plate).filter(Plate.name == pn).first()
        if plate:
            m = ImageMatcher(db, plate, [repo])
            # get the images sets from the repository
            for bt_key, rims in m.repo_map.items():
                # find the db image set
                db_ims = m.db_map.get(bt_key)
                if db_ims:
                    wm1 = set([im.wellPos.well_name for im in db_ims.images])
                    wm2 = set(rims.wells.keys())
                    if wm1 != wm2:
                        only1 = wm1 - wm2
                        only2 = wm2 - wm1
                        both = wm1 & wm2
                        log(f"  Mismatch: ({len(only1)}({len(both)}){len(only2)})")
                        if len(only1) > 0:
                            raise ValueError(f"Only in db: {only1}")
                        if doit:
                            m.insert_image_set(rims)
                        missing_wells += 1
                else:
                    log(f"  Not in db: {rims}")
                    if doit:
                        m.insert_image_set(rims)
                    not_in_db += 1
    log(f"Found{' and fixed' if doit else ''} {missing_wells} image sets with  missing images.")
    log(f"Found{' and inserted' if doit else ''} {not_in_db} image sets not in database.")
    
def untracked_images(plate: str, repo_name: str = "olympus", doit = False):
    """Find and insert image sets not linked to a workflow step"""
    repo = Repo(repo_name)
    if plate=="all":
        plates = repo.image_sets.all_plates()
    else:
        plates = [repo.plate(plate)]
    count = 0
    for pims in plates:
        log("")
        [pn] = pims.plate_names
        log(f"PLATE: {pn}")
        plate = db.query(Plate).filter(Plate.name == pn).first()
        if plate:
            m = ImageMatcher(db, plate, [repo])
            # get unlinked image sets
            for ims in pims.image_sets:
                if ims.bt_key not in m.db_map:
                    log(f"  {ims}")
                    if doit:
                        m.insert_image_set(ims)
                    count += 1
    log(f"Found{' and fixed' if doit else ''} {count} untracked image sets.")
    

def bad_links(plate: str, repo_name: str = "olympus", remove: bool = False, add: bool = False):
    """Find and fix bad links between image sets and uploads."""
    repo = Repo(repo_name)
    if plate=="all":
        plates = repo.image_sets.all_plates()
    else:
        plates = [repo.plate(plate)]
    count = 0
    added = 0
    for pims in plates:
        log("")
        [pn] = pims.plate_names
        log(f"PLATE: {pn}")
        plate = db.query(Plate).filter(Plate.name == pn).first()
        if plate:
            m = ImageMatcher(db, plate, [repo])
            # Get the imaging actions
            for i, (iin, ireq, iact, ires) in enumerate(m.iacts):
                dev = "cytosmart"
                if iin.device == "olympus" or iact.station == "olympus":
                    dev = "olympus"
                ureq = db.query(UploadIn, Request).\
                    filter(UploadIn.image_req == ireq.id).\
                    filter(Request.id == UploadIn.request).\
                    one_or_none()
                img_day = None
                day = plate_days(ires.ended, m.seed_time)
                dstr = "     "
                if ureq:
                    uout = db.query(UploadOut, ImageSet).\
                        filter(UploadOut.request == ureq[1].id).\
                        filter(ImageSet.id == UploadOut.image_set).\
                    one_or_none()
                    if uout: 
                        img_day = uout[1].day
                        img_light = uout[1].lighting
                        dstr = f"{img_day:2d} {img_light}"
                    elif dev==repo.name:
                        ims = None
                        try:
                            ims = m.find_match(dev, iact, ires)
                        except ValueError as e:
                            log("  " + str(e))
                        if ims:
                            if add:
                                db_ims, reason, msg = m.insert_image_set(ims)
                                uout = UploadOut(request=ureq[1].id, image_set=db_ims.id)
                                with transact(db):
                                    db.add(uout)
                            added += 1        
                        
                log(f"  {i:2d}  {iin.device[:3]} {iact.station[:3]}  {day} {dstr}  {sform(ires.comment)}")
                if img_day and abs(day - img_day) > 2:
                    log(f"  Mismatch: {day} != {img_day}{', unlink...' if remove else ''}")
                    if remove:
                        with transact(db):
                            db.delete(uout[0])
                    count += 1
    log(f"Found {'and removed' if remove else ''} {count} bad links.")
    log(f"Found {'and added' if add else ''} {added} missing links.")
               
    
def seed_dates(plate: str, force: bool = False, doit: bool = False):
    repo = Repo("cytosmart")
    if plate=="all":
        if force:
            log("ALL PLATES, force disabled.")
            force = False
        plates = repo.image_sets.all_plates()
    else:
        plates = [repo.plate(plate)]
    count = 0
    for pims in plates:
        log("")
        [pn] = pims.plate_names
        log(f"PLATE: {pn}")
        plate = db.query(Plate).filter(Plate.name == pn).first()
        if plate:
            m = ImageMatcher(db, plate, [repo])
            days_min = []
            days_max = []
            for ims in pims.image_sets:
                days_min.append(((ims.created_min - m.seed_time).total_seconds() / 86400) - ims.day)
                days_max.append(((ims.created_max - m.seed_time).total_seconds() / 86400) - ims.day)
                log(f"    {ims.day:2d} : {ims.lighting} {len(ims.wells):3d} {ims.bt_key} {tform(ims.created_min)} {days_min[-1]:4.2f} {days_max[-1]:4.2f}")
            # Compute the median day offset
            days = [0.5*(days_min[i] + days_max[i]) for i in range(len(days_min))]
            #med = sorted(days)[len(days) // 2]
            med = statistics.median(days)
            # check for tight clustering, disregarding some outliers
            n = 0
            for i in range(len(days)):
                if abs(days[i] - med) < 0.3:
                    n += 1
            if (n > 2 and n > 0.8 * len(days)) or force:
                if abs(med) > 0.5:
                    new_time = m.seed_time + timedelta(days=med)
                    log(f"  FIX: ({n}/{len(days)}) by {med:4.2f}, {m.seed_time} -> {new_time}")
                    count += 1
                    if doit:
                        if hasattr(m, 'seed_result'):
                            act = db.get(Action, m.seed_result.id)
                            with transact(db):
                                m.seed_result.ended = new_time
                                act.started = act.started + timedelta(days=med)
                        elif hasattr(m, 'seed_request'):
                            with transact(db):
                                m.seed_request.created = new_time
                    else:
                        log("  No seed action or request found.")
                else:
                    log(f"   OK: ({n}/{len(days)}) by {med:4.2f}")
            else:
                log(f"  Inconclusive: ({n}/{len(days)}) by {med:4.2f}")
    log(f"Found {'and fixed' if doit else ''} {count} bad seed dates.")

def queue_uploads():
    """Queue uploads for all imaging operations in the database."""

    ireqs = db.query(Request, Result).\
        filter(Request.action == Result.id).\
        filter(Result.result == 'pass').\
        filter(Request.step == 'image').\
        all()
    log(f"  Found {len(ireqs)} completed imaging requests")
    
    ureqs = db.query(UploadIn, Request, UploadOut).\
        filter(UploadIn.request == Request.id).\
        filter(Request.id == UploadOut.request).\
        all()
    
    ireq_of_interest = 1587
    for uli, req, ulo in ureqs:
        if uli.image_req == 1587:
            log(f"For {ireq_of_interest}:")
            log(f"  {uli}")
            log(f"  {req}, action: {req.action}, image_set: {ulo.image_set}")
    todo = set([req.id for req, _ in ireqs])
    done = set([uli.image_req for uli, _, _ in ureqs])
    left = todo - done
    log(f"  Found {len(todo)} - {len(done)} = {len(left)} imaging requests to queue for upload.")

#
#   Fill some create dates
#
def fill_null_fields(model: Base, field: str, value):
    _done = None
    def log_progress():
        nonlocal _done
        xs0 = db.query(model).all()
        xs = db.query(model).\
            filter(getattr(model, field) != None).\
            all()
        if _done is None:
            log.info(f"{model.__name__}.{field}: {len(xs)}/{len(xs0)}")
            _done = len(xs)
        else:
            log.info(f"{model.__name__}.{field}: {_done} -> {len(xs)}/{len(xs0)} ({len(xs) - _done} completed just now.)")
        return len(xs) == len(xs0)
    
    done = log_progress()
    if value is not None and not done:
        with transact(db):
            for x in db.query(model).all():
                if getattr(x, field) is None:
                    setattr(x, field, value)
        done = log_progress()
    log(f"{model.__name__}.{field} nulls done: {done}")
    return done

#
# Backfill plate map info
#

def fill_platemap_info():
    _done = None
    def log_progress():
        nonlocal _done
        maps0 = db.query(PlateMap).all()
        maps = db.query(PlateMap).\
            filter(PlateMap.name != None).\
            filter(PlateMap.description != None).\
            filter(PlateMap.type != None).\
            filter(PlateMap.created != None).\
            all()
        if _done is None:
            log.info(f"{len(maps)}/{len(maps0)}")
            _done = len(maps)
        else:
            log.info(f"{_done} -> {len(maps)}/{len(maps0)} ({len(maps) - _done} completed just now.)")
        return len(maps) == len(maps0)

    done = log_progress()
    if not done:
        maps = db.query(PlateMap).all()
        for m in maps:
            if m.name is not None:
                continue
            groups = m.map_groups
            gc = {g.group_type: 0 for g in groups}
            wc = {g.group_type: 0 for g in groups}
            log(f"  gc: {gc}")
            log(f"  wc: {wc}")
            wc_exp = None
            for g in groups:
                gc[g.group_type] += 1
                wc[g.group_type] += len(g.doses)
                if g.group_type == 'experimental':
                    if wc_exp is None:
                        wc_exp = f"{len(g.doses)}"
                    else:
                        if f"{len(g.doses)}" != wc_exp:
                            wc_exp = "various"
            name = f"{gc['experimental']:02d} treatments (id:{m.id})"
            desc = f"{gc['experimental']}x{wc_exp} experiments, {wc['positive']} positives, {wc['negative']} negatives."
            log(f"  Update  {m} {m.created}:")
            log(f"    {name}: {desc}")
            with transact(db):
                if m.name==None: m.name = name
                if m.description==None: m.description = desc
                if m.type==None: m.type = 'dosing'
                if m.created==None: m.created = datetime.min.replace(tzinfo=pytz.UTC)
            log(f"  Updated {m} {m.created}")
        done = log_progress()
    log(f"PlateMap info done: {done}")
    return done

#
# Fill in missing image set data, all except sizes which are filled with the images 
#
def fill_iset_info():
    # Get the plates eligible to be backfilled
    plates = db.query(Plate).filter(Plate.type == 'culture').all()
    log.info(f"Found {len(plates)} plates to backfill")

    # Look for any images that don't have a plate, there should be none.
    imss0 = db.query(ImageSet).all()
    imss = db.query(ImageSet).filter(ImageSet.plate != None).all()
    log.info(f"   All: {len(set([i.id for i in imss0]))}/{len(imss0)}")
    log.info(f"Plated: {len(set([i.id for i in imss]))}/{len(imss)}")

    # script to report progress
    _done = None
    def log_progress():
        nonlocal _done
        imss0 = db.query(ImageSet).all()
        imss = db.query(ImageSet).\
            filter(ImageSet.day != None).\
            filter(ImageSet.lighting != None).\
            all()
        if _done is None:
            log.info(f"{len(imss)}/{len(imss0)}")
            _done = len(imss)
        else:
            log.info(f"{_done} -> {len(imss)}/{len(imss0)} ({len(imss) - _done} completed just now.)")
        return len(imss) == len(imss0)
    
    done = log_progress()
    if not done:
        wrong_key = []
        for i, plate in enumerate(plates[:]):
            log.info(f"Backfilling {i+1}/{len(plates)}: {plate}")
            m = ImageMatcher(db, plate)
            # Do the updating
            log.info(f"Update image sets for plate {plate.name}")
            for ims in m.imss:
                # DAY
                day = plate_days(ims.created, m.seed_time)
                if ims.day is None:
                    with transact(db):
                        ims.day = day
                    log.info(f"  Update: {day} -> {ims}")
                else:
                    log.info(f"  Already done day: {ims}")
                    if ims.day != day:
                        raise Exception(f"Day mismatch: {ims.day} != {day}")
                    
                # LIGHTING
                log(f"  {ims} -> {ims.bt_key}")
                if ims.bt_key not in m.repo_map:
                    log.error(f"Wrong key: {ims.bt_key}")
                    wrong_key.append(ims)
                    for k in m.repo_map.keys():
                        ks = ims.bt_key.split("/")
                        if ks[1].endswith("_FL"):
                            k2 = ks[0] + "/" + "F_" + ks[1][:-3]
                        if ks[1].endswith("_BF"):
                            k2 = ks[0] + "/" + "P_" + ks[1][:-3]
                        if k != k2:
                            k2 = k2[:-3] + "_01" + k2[-3:]
                        log(f"  {k} =? {k2}")
                        if k == k2:
                            log.error(f"  Found match, fixing: {k}")
                            with transact(db):
                                ims.bt_key = k
                            break
                    continue
                lighting = m.repo_map[ims.bt_key].lighting
                if ims.lighting is None:
                    with transact(db):
                        ims.lighting = lighting
                    log.info(f"  Update: {lighting} -> {ims}")
                else:
                    log.info(f"  Already done lighting: {ims}")
                    if ims.lighting != lighting:
                        raise Exception(f"Lighting mismatch: {ims.lighting} != {lighting}")
        if wrong_key:
            log.error(f"Wrong keys: {len(wrong_key)}")
        done = log_progress()
    log(f"Iset info done: {done}")
    return done

def fill_olympus_bf():
    # Get the plates eligible to be backfilled
    plates = db.query(Plate).filter(Plate.type == 'culture').all()
    log.info(f"Found {len(plates)} plates to backfill")

    # Look for any images that don't have a plate, there should be none.
    imss0 = db.query(ImageSet).all()
    imss = db.query(ImageSet).filter(ImageSet.plate != None).all()
    log.info(f"   All: {len(set([i.id for i in imss0]))}/{len(imss0)}")
    log.info(f"Plated: {len(set([i.id for i in imss]))}/{len(imss)}")

    # script to report progress
    _done = None
    def print_progress():
        nonlocal _done

        # Get the image sets already in the database
        imss_db = db.query(ImageSet).all()
        done = set([ims.bt_key for ims in imss_db])
        if len(done) != len(imss_db):
            raise Exception(f"Duplicate bt_keys: {len(done)} != {len(imss_db)}")
        
        # Get the image sets in the repository
        repo = Repo("olympus-tiff")
        imss0 = repo.image_sets.image_sets.values()
        imss = [ims for ims in imss0 if ims.bt_key in done]
        
        if _done is None:
            log.info(f"{len(imss)}/{len(imss0)}")
            _done = len(imss)
        else:
            log.info(f"{_done} -> {len(imss)}/{len(imss0)} ({len(imss) - _done} completed just now.)")

    print_progress()
    for i, plate in enumerate(plates[-10:]):
        log.info(f"Backfilling {i+1}/{len(plates)}: {plate}")
        m = ImageMatcher(db, plate)
        # Do the updating
        log.info(f"Update olympus bright field image sets for plate {plate.name}")
        pims = m.repo_pims["olympus"]
        for ims in pims.image_sets:
            log.info(f"    {ims.day:2d}{'*' if ims.bt_key in m.db_map else ' '} : {ims.lighting} {ims}")
    print_progress()


# def fill_images(plate_name: str, day: int, well: str):
#     csr = Repo("cytosmart")
#     cs_ims = csr.image_set(plate_name, day)
#     log.info(f"Cytosmart Image set: {cs_ims}")
    
#     db_ims = db.query(ImageSet).filter(ImageSet.bt_key == cs_ims.bt_key).one()
#     log.info(f"DB Image set: {db_ims}")
#     if not db_ims.day:
#         with transact(db):
#             db_ims.day = day
    
#
#  Older stuff:
#

# Base.metadata.create_all(bind=engine)

@dataclass
class DonorSheet:
    sheet_id: str = "1Bg3ezlSAiyc-SBg9W9IRxyU2aiYys5dXCBa12FfmEJY"
    range: str = "Donor Lots!A:L"

def load_donor_sheet():
    input = DonorSheet()
    data = get_sheet_data(input.sheet_id, input.range)
    log.info(f"Data: {data}")
    
 
def by_name(m: Base, name: str):
    """Return the object with the given name"""
    x = db.query(m).filter(m.name == name).first()
    if not x:
        raise Exception(f"{m.__name__} {name} not found")
    return x
    
def fill_plate(pis: PlateImageSets, pname: str):
    """Add a plate to database if not there already. Return the plate object."""
    ims = pis.image_sets[0]
    #pname = f"Plate{pno:04d}"
    plate = db.query(Plate).filter(Plate.name == pname).first()
    if not plate:
        #raise Exception(f"Plate {pname} not found.")
        log.info(f"Adding plate {pname}")
        # Find the format
        format = by_name(PlateFormat, f"{ims.numberOfWells}-well")
        ptype = by_name(PlateType, "culture")
        vendor = by_name(Vendor, "4DCell")
        
        db.rollback()
        with db.begin():
            # Create new plate
            plate = Plate(
                name=pname,
                format=format.name,
                type = ptype.name,
                vendor = vendor.name
            )
            db.add(plate)
        log.info(f"Added plate {plate}")
    return plate

def fill_image_sets(pis: PlateImageSets, plate_name: str, donor_code: str = "S4"):
    """Fill the database with image sets for a plate"""
    if not isinstance(plate_name, str):
        raise ValueError(f"Plate name must be a string, not {plate_name}")
    
    # Fill seed request for seed date
    plate  = fill_plate(pis, plate_name)
    seedIn = db.query(SeedIn).filter(SeedIn.plate == plate.id).first()
    if seedIn:
        request = db.query(Request).filter(Request.id == seedIn.request).first()
    else:
        donor = db.query(Donor).filter(Donor.code == donor_code).first()
        db.rollback()
        with db.begin():
            # Create a fake request for the seed date
            request = Request(
                step = "seed",
                project = "backfill",
                created = pis.seed_time,
                schedule = datetime.min.replace(tzinfo=pytz.UTC),
                deadline = datetime.max.replace(tzinfo=pytz.UTC),
                comment = "Fake request to set seed time for backfilling"
            )
            db.add(request)
            db.flush()
            db.refresh(request)
            seedIn = SeedIn(
                request = request.id,
                plate = plate.id,
                donor = donor.id
            )
            db.add(seedIn)
        log.info(f"Added seed request for {plate_name}.")

    # Fill all missing image sets, by day
    present = dict()
    for ims in db.query(ImageSet).filter(ImageSet.plate == plate.id).all():
        day = plate_days(ims.created, request.created)
        present[day] = ims
    log.info(f"Present: {len(present)}/{len(pis.image_sets)}.")
    for ims in pis.image_sets:
        if ims.plate_name != plate_name: continue
        image_set = present.get(ims.day)
        if not image_set:
            db.rollback()
            with db.begin():
                # Create new image set
                image_set = ImageSet(
                    plate = plate.id,
                    created = ims.created,
                    device = "cytosmart",
                    um_per_pixel = 742,
                    size_x = 0,    # Placeholder, not in image set
                    size_y = 0,
                    bt_key = f"{ims.experiment_id}/{ims.id}" 
                )
                db.add(image_set)
            log.info(f"Added image set {ims.plate_name}-{ims.day}.")
        log.info(f"Image set {ims.plate_name}-{ims.day} {ims.well_stats()}.")




def fill_constraints_1():
    done = \
        fill_iset_info() and \
        fill_platemap_info() and \
        fill_null_fields(Plate, "created", datetime.min.replace(tzinfo=pytz.UTC)) and \
        fill_null_fields(Donor, "created", datetime.min.replace(tzinfo=pytz.UTC)) and \
        fill_null_fields(Vendor, "type", None)
    if done:
        log.info("All constraints filled.")

def fill_info():
    fill_constraints_1()



class MissingPlate(NamedTuple):
    name: str
    pimss: List[PlateImageSets]
    first_updated: datetime
    last_updated: datetime
    
    @classmethod
    def head(cls):
        return "name        isets      images first updated last updated"
        
    def row(self):
        imsc = " ".join([f"{len(pims.image_sets):2d}" for pims in self.pimss])
        imc = " ".join([f"{sum([len(ims.wells) for ims in pims.image_sets]):4d}" for pims in self.pimss])
        return f"{self.name:10s}  {imsc}   {imc}  {tform(self.first_updated)}   {tform(self.last_updated)}"

def plate_names(doit: bool = False):
    repos = [Repo(n) for n in ["cytosmart", "olympus"]]
    names_db = set([p.name for p in db.query(Plate).all()])
    names_repo = [
        set([s.plate_name for s in r.image_sets.image_sets.values() if s.plate_name is not None])
        for r in repos
    ]
    only_db = names_db - set.union(*names_repo)
    only_repo = [names_repo[i] - names_db for i in range(len(repos))]
    not_db = set.union(*only_repo)
    log(f"#### Plate names not in db: ({len(not_db)})")
    nond = [n for n in only_db if "_S" not in n]
    log(f"Only in db, non-supernatant {len(nond)}/{len(only_db)}:")
    for n in nond:
        p = db.query(Plate).filter(Plate.name == n).one()
        log(f"  {p}")
    log(f"Not in db: {len(not_db)}")
    mps = []
    for pn in not_db:
        pimss = [PlateImageSets(r.image_sets, [pn]) for r in repos]
        last_updated = max(*[pims.last_updated for pims in pimss])
        first_updated = min(*[pims.first_updated for pims in pimss])
        mps.append(MissingPlate(pn, pimss, first_updated, last_updated))
    mps.sort(key=lambda mp: mp.last_updated, reverse=True)
    log(f"  {MissingPlate.head()}")
    for mp in mps:
        log(f"  {mp.row()}")
    
    # Find plates that have the same plate number but are not linked
    plates_by_no = {parse_plate_no(p.name): p for p in db.query(Plate).all() if "_S" not in p.name}
    log("#### Plates with the same number but not linked:")
    log(f"  {MissingPlate.head()}   db_name")
    rename = {}
    for mp in mps:
        if not mp.name.startswith("PD") and parse_plate_no(mp.name) in plates_by_no:  
            db_name = plates_by_no[parse_plate_no(mp.name)].name 
            log(f"  {mp.row()}   {db_name}")
            rename[db_name] = mp.name
    if doit:
        with transact(db):
            for db_name, repo_name in rename.items():
                p = db.query(Plate).filter(Plate.name == db_name).one()
                p.name = repo_name
        log(f"Renamed {len(rename)} plates.")
    log(f"Found{' and fixed' if doit else ''} {len(rename)} misnamed plates.")
    
   
with java_vm():
    doit = False
    # done plate_names(doit = doit)
    # done seed_dates("Plate0209", force=True, doit=doit)
    # done bad_links("Plate0209", "cytosmart")
    # done bad_links("Plate0209", "olympus")
    # done untracked_images("Plate0209", "cytosmart", doit=doit)
    # done untracked_images("Plate0209", "olympus", doit=doit)

exit(0)

#fill_sizes("cytosmart", doit=True)

# with java_vm():
#     fill_images("cytosmart", doit=False)
#     fill_images("olympus", doit=False)
# with java_vm():
#     bad_links("all", "cytosmart")
#     bad_links("all", "olympus")
    
# seed_dates()


plate = db.query(Plate).filter(Plate.name == "Plate0210").one()
matcher = ImageMatcher(db, plate, [Repo("olympus")])
log(f"Image sets to insert: {len(matcher.repo_missing)}")
for ims in matcher.repo_missing:
    log(f"  {ims}")
# with java_vm():
#     matcher.insert_image_set(matcher.repo_missing[0])
   
log.info("Filling constraints 1.")  
fill_constraints_1()
#images = CytosmartRepo().image_sets

# to_fill = [174, 175, 176, 177, 178, 179]
# #to_fill = [166, 167, 168]

# for plate_no in to_fill:
#     pis = PlateImageSets(images, f"Plate{plate_no:04d}")
#     fill_image_sets(pis)
#to_fill = [109, 119, 120]
# to_fill = [180, 181, 182, 183, 184, 185]
# pis = PlateImageSets(images, to_fill)
# for plate_no in to_fill:
#     fill_image_sets(pis, f"Plate{plate_no:04d}")

# seeding = SeedStep(project="backfill")
# imaging = ImageStep(project="backfill")
# #seeding.create_plates(db, "384-well", "4DCell", [180, 181, 182, 183, 184, 185])

# pnos = [180, 181, 182, 183, 184, 185]
# pis = PlateImageSets(inv.get_image_sets(), pnos)

# seeding.create_plates(db, "384-well", "4DCell", pnos)
# seeding.queue_plates(db, "S4", pnos)

# for pno in pnos:
#     fill_plate(pis, pno)

# uploading  = UploadStep(project="backfill")

# busy = BusyActions(uploading, db)
# log.info(f"Busy: {busy.n}.")
# for nr, _, action in busy.items:
#     log.info(f"{nr}: {action}")
