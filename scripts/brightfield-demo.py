from base.database import get_db
from plates.model import Plate
from images.pipeline import Pipeline, negatives
from images.model import ImageSet as DbImageSet
from images.match import ImageMatcher
from images.repo import all_repos, ImageRepo
from base.util import make_logger
from google.cloud import storage, bigquery
from base.io import Store, cache_store, FileStore
import pandas as pd
import json
import os
import traceback
import numpy as np

from images.brightfield import BrightfieldAnalysis
from images.repo import Repo
log = make_logger(__name__)

# GCS and BigQuery configuration
project_id = "development-311316"
bucket_name = "cytosmart_brightfield_images_mellicell"
mask_bucket_name = "cytosmart_masks_mellicell"
bq_dataset = "mellicell_image_data_v2"
bq_lipid_table = "cytosmart_lipid_measurments"  # Individual cell measurements
bq_agg_table = "cytosmart_measurements"  # Aggregated measurements

experiment_id = "824d21e2-8395-4a8b-a333-32b26eab32ef"
scan_number = "1697644026"
well_name = "D2"
bt_key = f"{experiment_id}_{scan_number}"

storage_client = storage.Client(project=project_id)
bq_client = bigquery.Client(project=project_id)


def get_image_metadata(bucket_name, experiment_id, scan_number):
    """
    Get the scan_metadata.json for a specific experiment/scan
    Returns:
        dict: Metadata from scan_metadata.json or None if not found
    """
    bucket = storage_client.bucket(bucket_name)
    metadata_blob_name = f"{experiment_id}/{scan_number}/scan_metadata.json"
    metadata_blob = bucket.blob(metadata_blob_name)
    
    if not metadata_blob.exists():
        log.info(f"Metadata file not found: {metadata_blob_name}")
        return None
    
    # Download and parse the metadat
    metadata_content = metadata_blob.download_as_string()
    return json.loads(metadata_content)

def get_bigquery_measurements(table, bt_key, well=None):
    """
    Get measurements from BigQuery for a specific image set and well
    Returns:
        DataFrame: BigQuery results
    """
    experiment_id, scan_number = bt_key.split('/')
    
    # For the individual lipid measurements table
    if table == bq_lipid_table:
        if well:
            query = f"""
            SELECT * FROM `{project_id}.{bq_dataset}.{table}`
            WHERE image LIKE 'gs://cytosmart_brightfield_images_mellicell/{experiment_id}/{scan_number}/{well}/%'
            """
        else:
            query = f"""
            SELECT * FROM `{project_id}.{bq_dataset}.{table}`
            WHERE image LIKE 'gs://cytosmart_brightfield_images_mellicell/{experiment_id}/{scan_number}/%'
            """
    # For the aggregate measurements table
    else:
        if well:
            query = f"""
            SELECT * FROM `{project_id}.{bq_dataset}.{table}`
            WHERE experiment_id = '{experiment_id}'
            AND scan_number = {scan_number}
            AND image LIKE 'gs://cytosmart_brightfield_images_mellicell/{experiment_id}/{scan_number}/{well}/%'
            """
        else:
            query = f"""
            SELECT * FROM `{project_id}.{bq_dataset}.{table}`
            WHERE experiment_id = '{experiment_id}'
            AND scan_number = {scan_number}
            """
    
    log.info(f"Executing query: {query}")
    query_job = bq_client.query(query)
    return query_job.result().to_dataframe()

def compare_results(new_df, bq_df, metrics=None):
    """
    Compare our processing results with BigQuery data
    
    Args:
        new_df: DataFrame with new measurements
        bq_df: DataFrame with BigQuery measurements
        metrics: List of metrics to compare (default: area, perimeter, circularity)
    
    Returns:
        dict: Comparison results
    """
    if metrics is None:
        metrics = ['area', 'perimeter', 'circularity']
    
    if bq_df.empty:
        log.info("No BigQuery data found for comparison")
        return None
    
    if new_df is None or new_df.empty:
        log.info("No new measurements generated")
        return None
    
    # Basic statistics comparison
    log.info("=== Comparison of Results ===")
    log.info(f"BigQuery records: {len(bq_df)}")
    log.info(f"New measurements: {len(new_df)}")
    
    results = {
        'record_count_bq': len(bq_df),
        'record_count_new': len(new_df),
        'metrics': {}
    }
    
    for metric in metrics:
        if metric in bq_df.columns and metric in new_df.columns:
            bq_mean = bq_df[metric].mean()
            new_mean = new_df[metric].mean()
            diff_pct = ((new_mean - bq_mean) / bq_mean) * 100 if bq_mean != 0 else float('inf')
            
            log.info(f"{metric.capitalize()}:")
            log.info(f"  BigQuery mean: {bq_mean:.2f}")
            log.info(f"  New mean: {new_mean:.2f}")
            log.info(f"  Difference: {diff_pct:.2f}%")
            
            results['metrics'][metric] = {
                'bq_mean': bq_mean,
                'new_mean': new_mean,
                'diff_pct': diff_pct
            }
    
    return results


def find_repo_image_set(repo, bt_key):
    """
    Find an ImageSet in the repository by its bigtable key
    
    Args:
        repo: Repository instance
        bt_key: bigtable key
    
    Returns:
        RepoImageSet: The image set or None if not found
    """
    
    # Check if we can find it in the bt_key_map
    if bt_key in repo.image_sets.bt_key_map:
        return repo.image_sets.bt_key_map[bt_key]
    
    # If not found, try to find it by experiment_id and id (which corresponds to scan_number)
    experiment_id, scan_number = bt_key.split('_')
    for image_set in repo.image_sets.image_sets.values():
        if (image_set.experiment_id == experiment_id and 
            image_set.id == scan_number):
            return image_set
    
    # If still not found, log the available keys for debugging
    log.debug(f"Could not find image set with bt_key {bt_key}")
    log.debug(f"Available bt_keys: {list(repo.image_sets.bt_key_map.keys())[:5]}...")
    
    return None


def process_image_with_repo(db, repo, bt_key, well_name):
    """ Process an image using the repository pattern and CytosmartAnalysis """
    repo_image_set = find_repo_image_set(repo, bt_key)
    if repo_image_set is None:
        log.error(f"Image set with bt_key {bt_key} not found in repository")
        return None, None
    
    log.info(f"Found image set: {repo_image_set}")

    # Ensure required attributes exist
    if not hasattr(repo_image_set, 'experimentName'):
        # Add missing attributes with default values - To be changed
        repo_image_set.experimentName = repo_image_set.id if hasattr(repo_image_set, 'id') else bt_key
        repo_image_set.experimentId = repo_image_set.experiment_id if hasattr(repo_image_set, 'experiment_id') else bt_key.split('/')[0]
        repo_image_set.scanNumber = repo_image_set.id if hasattr(repo_image_set, 'id') else bt_key.split('/')[-1]
        repo_image_set.pixelsPerMm = 742  # Default value
        repo_image_set.numberOfWells = 384  # Default value

    plate_name = getattr(repo_image_set, 'plate_name', 'unknown')
    plate = db.query(Plate).filter(Plate.name == plate_name).first()
    if not plate:
        raise Exception(f"Plate {plate_name} not found in the database.")

    matcher = ImageMatcher(db, plate, [repo])

    # Try to find existing database image set
    db_image_set = None
    for ims in matcher.imss:
        if ims.bt_key == repo_image_set.bt_key:
            db_image_set = ims
            log.info(f"Found existing database image set: {db_image_set}")
            break

    if db_image_set is None:
        log.info("Inserting repository image set into database")
        db_image_set, status, msg = matcher.insert_image_set(repo_image_set)
        log.info(f"Insert result: {status} - {msg}")
    
    if db_image_set is None:
        log.error("Failed to create database image set")
        return None, None
    
    # Create a store for caching results
    store_path = f"brightfield_analysis/{bt_key}/{well_name}"
    store = cache_store() / store_path

    cts = negatives(db)
    log.info(f"Negatives: {[d.well_pos.well_name for d in cts.map_groups[0].doses]}")
    
    pipeline = Pipeline(BrightfieldAnalysis, db_image_set, cts, store)
    
    # Create the analysis instance
    analyzer = None
    for well_analyzer in pipeline.wells:
        if well_analyzer.well == well_name:
            analyzer = well_analyzer
            break
    if analyzer is None:
        # If not found in pipeline.wells, create one directly
        analyzer = BrightfieldAnalysis(pipeline, db_image_set, well_name)
        analyzer.storage_client = storage_client

    object_name = f"{bt_key}/{well_name}/{well_name}_export.jpg"
    
    log.info(f"Processing image: gs://{bucket_name}/{object_name}")

    try:
        cp_df, agg_df = analyzer.process(bucket_name, object_name)
        log.info("Image processing completed successfully")
        return cp_df, agg_df
    except Exception as e:
        log.error(f"Error processing image: {e}")
        traceback.print_exc()
        return None, None

def image_match(db, repo: Repo, limit=1):
    """
    Find images that exist in both the database and repository.
    Args:
        repo_name: Repository name to check
        limit: Maximum number of matches to return 
    Returns:
        List of tuples (plate, bt_key, day) for images in both systems
    """
    
    if not repo:
        log.error(f"Repository {repo} not found")
        return []
    
    # Get all plates from the database
    plates = db.query(Plate).all()
    
    matches = []
    for plate in plates:
        log.info(f"Checking plate {plate.name}")
        
        try:
            matcher = ImageMatcher(db, plate, [repo])
            
            # Find image sets that exist in both systems
            overlaps = []
            for bt_key, repo_ims in matcher.repo_map.items():
                if bt_key in matcher.db_map:
                    db_ims = matcher.db_map[bt_key]
                    # Check if repo_ims has required attributes before using
                    if not hasattr(repo_ims, 'experimentName'):
                        # Add missing attributes with default values
                        repo_ims.experimentName = repo_ims.id if hasattr(repo_ims, 'id') else bt_key
                        repo_ims.experimentId = repo_ims.experiment_id if hasattr(repo_ims, 'experiment_id') else bt_key.split('/')[0]
                        repo_ims.scanNumber = repo_ims.id if hasattr(repo_ims, 'id') else bt_key.split('/')[-1]
                        repo_ims.pixelsPerMm = 742  # Default value
                        repo_ims.numberOfWells = 384  # Default value
                    
                    overlaps.append((plate, bt_key, db_ims.day))
                    if len(overlaps) >= limit:
                        break
            
            matches.extend(overlaps)
            
            if len(matches) >= limit:
                log.info(f"Found {len(matches)} matches, stopping search")
                break
                
        except Exception as e:
            log.error(f"Error processing plate {plate.name}: {e}")
            traceback.print_exc()
    
    return matches[:limit]


def display_comparison(cp_df, bq_cp_df, agg_df, bq_agg_df, well_name):
    """
    Display a simple side-by-side comparison of all measurements
    """
    print(f"\n{'='*80}")
    print(f"COMPARISON FOR WELL: {well_name}")
    print(f"{'='*80}")
    
    # 1. Individual cell measurements
    print("\nINDIVIDUAL CELL MEASUREMENTS:")
    print(f"{'='*80}")
    print(f"{'Metric':<20} {'New Processing':<20} {'BigQuery':<20} {'Diff %':<10}")
    print(f"{'-'*80}")
    
    if cp_df is not None and not bq_cp_df.empty:
        # Get all numeric columns from both dataframes
        new_cols = [c for c in cp_df.columns if cp_df[c].dtype.kind in 'fc']
        bq_cols = [c for c in bq_cp_df.columns if bq_cp_df[c].dtype.kind in 'fc']
        
        # Find common columns
        common_cols = sorted(list(set(new_cols) & set(bq_cols)))
        
        # Calculate and display means for each metric
        for col in common_cols:
            new_mean = cp_df[col].mean()
            bq_mean = bq_cp_df[col].mean()
            diff_pct = ((new_mean - bq_mean) / bq_mean * 100) if bq_mean != 0 else float('inf')
            
            print(f"{col:<20} {new_mean:<20.4f} {bq_mean:<20.4f} {diff_pct:<10.2f}")
    else:
        print("No individual cell data available for comparison")
    
    # 2. Aggregated measurements
    print("\nAGGREGATED MEASUREMENTS:")
    print(f"{'='*80}")
    print(f"{'Metric':<20} {'New Processing':<20} {'BigQuery':<20} {'Diff %':<10}")
    print(f"{'-'*80}")
    
    if agg_df is not None and not bq_agg_df.empty:
        # Sort both dataframes by cluster
        agg_df_sorted = agg_df.sort_values('cluster').reset_index(drop=True)
        bq_agg_df_sorted = bq_agg_df.sort_values('cluster').reset_index(drop=True)
        new_cols = [c for c in agg_df.columns if agg_df[c].dtype.kind in 'fc']
        bq_cols = [c for c in bq_agg_df.columns if bq_agg_df[c].dtype.kind in 'fc']
        common_cols = sorted(list(set(new_cols) & set(bq_cols)))
        
        for cluster in sorted(set(agg_df['cluster'].unique()) & set(bq_agg_df['cluster'].unique())):
            print(f"\nCluster {cluster}:")
            new_row = agg_df[agg_df['cluster'] == cluster]
            bq_row = bq_agg_df[bq_agg_df['cluster'] == cluster]
            
            if len(new_row) == 0 or len(bq_row) == 0:
                print(f"  Cluster {cluster} missing in one of the datasets")
                continue
                
            for col in common_cols:
                if col in new_row.columns and col in bq_row.columns:
                    new_val = new_row[col].iloc[0]
                    bq_val = bq_row[col].iloc[0]
                    diff_pct = ((new_val - bq_val) / bq_val * 100) if bq_val != 0 else float('inf')
                    
                    print(f"  {col:<18} {new_val:<20.4f} {bq_val:<20.4f} {diff_pct:<10.2f}")
    else:
        print("No aggregated data available for comparison")
    
    # Summary statistics
    print("\nSUMMARY:")
    print(f"{'='*80}")
    print(f"Individual cells - New: {len(cp_df) if cp_df is not None else 0}, BigQuery: {len(bq_cp_df)}")
    print(f"Aggregated records - New: {len(agg_df) if agg_df is not None else 0}, BigQuery: {len(bq_agg_df)}")

if __name__ == "__main__":

    repo = Repo("cytosmart")
    db = get_db()

    matches = image_match(db, repo, limit=1)
    for plate, bt_key, day in matches:
        log.info(f"Testing image: {plate.name}, {bt_key}, {day}")
        experiment_id, scan_number = bt_key.split('/')
        metadata = get_image_metadata(bucket_name, experiment_id, scan_number)
        if metadata:
            log.info(f"Found metadata: {metadata}")
            experiment_name = metadata.get('experimentName', '')
            log.info(f"Experiment name: {experiment_name}")
        else:
            log.info("Metadata not found, continuing without it")
    
    # Get existing measurements from BigQuery
        bq_cp_df = get_bigquery_measurements(bq_lipid_table, bt_key, well_name)
        log.info(f"Found {len(bq_cp_df)} cell records in BigQuery")
        
        bq_agg_df = get_bigquery_measurements(bq_agg_table, bt_key, well_name)
        log.info(f"Found {len(bq_agg_df)} aggregated records in BigQuery")
        
        cp_df, agg_df = process_image_with_repo(db, repo, bt_key, well_name)
    
    # Compare results
        cell_comparison, agg_comparison = None, None

        if cp_df is not None:
            cell_comparison = compare_results(cp_df, bq_cp_df)
            cp_df.to_csv(f"new_cell_measurements_{well_name}.csv", index=False)
            log.info(f"Saved new cell measurements to new_cell_measurements_{well_name}.csv")

        if agg_df is not None:
            agg_comparison = compare_results(agg_df, bq_agg_df)
            agg_df.to_csv(f"new_agg_measurements_{well_name}.csv", index=False)
            log.info(f"Saved new aggregated measurements to new_agg_measurements_{well_name}.csv")

        if cell_comparison:
            log.info("Cell Measurement Comparison:")
            log.info(json.dumps(cell_comparison, indent=2))
        
        if agg_comparison:
            log.info("Aggregated Measurement Comparison:")
            log.info(json.dumps(agg_comparison, indent=2))

        display_comparison(cp_df, bq_cp_df, agg_df, bq_agg_df, well_name)

        if cp_df is not None and not bq_cp_df.empty:
            # raw cell measurements before any processing
            print("\nRaw cell measurements (first 3 rows):")
            print("New implementation:")
            print(cp_df[['label', 'area', 'circularity', 'cluster']].head(3))
            print("BigQuery:")
            print(bq_cp_df[['label', 'area', 'circularity', 'cluster']].head(3))
            
            # # cluster distribution
            # print("\nCluster distribution in new implementation:")
            # print(cp_df['cluster'].value_counts())
    
            # print("\nCluster distribution in BigQuery data:")
            # print(bq_cp_df['cluster'].value_counts())
            
            # Check aggregation results before scaling
            print("\nChecking aggregation before scaling:")

            test_agg = cp_df.groupby(['cluster', 'image']).agg({
                'area': ['mean', 'min', 'max'],
                'circularity': ['mean', 'min', 'max']
            })
            
            print("First few rows of test aggregation:")
            print(test_agg.head())
            print(f"\nPixel size used: {1000/metadata['pixelsPerMm']}")
            
            # Check final aggregated data
            print("\nFinal aggregated data (new implementation):")
            print(agg_df[['cluster', 'mean_area', 'max_area', 'mean_circ']].head())
            
            print("\nFinal aggregated data (BigQuery):")
            print(bq_agg_df[['cluster', 'mean_area', 'max_area', 'mean_circ']].head())
