from images.repo import <PERSON>o
from base.util import make_logger
import traceback

log = make_logger(__name__)

def demo_olympus_repository_access(repo, bt_key, well_name):
    """Demonstrate accessing Olympus images through repository pattern"""

    log.info(f"=== Repository Access Demo ===")
    log.info(f"Repository: {repo}")
    log.info(f"BT Key: {bt_key}")
    log.info(f"Well: {well_name}")

    try:
        # Get the image set
        ims = repo.image_sets.bt_key_map[bt_key]
        log.info(f"Image set: {ims}")

        # Get the well
        rwell = repo.get_well(bt_key, well_name)
        if not rwell:
            log.error(f"Well {well_name} not found")
            return None

        log.info(f"Well: {rwell}")
        log.info(f"Available channels: {rwell.channels()}")

        # Get image info
        default_image = rwell.default_image()
        log.info(f"Default image: {default_image}")
        log.info(f"Image info: {default_image.info}")

        # Try to load the image data (this demonstrates the repository pattern works)
        log.info("Loading image data...")
        image_data = default_image.data()
        log.info(f"Image loaded successfully: {image_data.shape} {image_data.dtype}")

        # Show some basic statistics
        log.info(f"Image statistics:")
        log.info(f"  Min: {image_data.min()}")
        log.info(f"  Max: {image_data.max()}")
        log.info(f"  Mean: {image_data.mean():.2f}")
        log.info(f"  Shape: {image_data.shape}")

        # Demonstrate that we can access pixel calibration info
        if hasattr(default_image.info, 'resx') and default_image.info.resx:
            pixel_size_um = default_image.info.resx
            pixels_per_mm = 1000.0 / pixel_size_um
            log.info(f"Pixel calibration:")
            log.info(f"  Pixel size: {pixel_size_um:.3f} μm")
            log.info(f"  Pixels per mm: {pixels_per_mm:.1f}")

        return image_data

    except Exception as e:
        log.error(f"Error accessing repository: {e}")
        traceback.print_exc()
        return None

def find_olympus_image_set(repo, plate_name, culture_day):
    """
    Find an Olympus image set in the repository
    """
    log.info(f"Looking for Olympus image set: {plate_name}, day {culture_day}")
    
    # Try to find the image set
    ims = repo.image_set(plate_name, culture_day)
    if ims:
        log.info(f"Found image set: {ims}")
        return ims
    
    # If not found, list available image sets for debugging
    log.info("Available image sets:")
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if hasattr(image_set, 'plate_name') and plate_name in str(image_set.plate_name):
            log.info(f"  {bt_key}: {image_set.plate_name} day {getattr(image_set, 'day', 'unknown')}")
    
    return None

def process_olympus_image_with_repo(db, repo, plate_name, culture_day, well_name):
    """Process an Olympus image using the repository pattern and BrightfieldAnalysis"""
    
    repo_image_set = find_olympus_image_set(repo, plate_name, culture_day)
    if repo_image_set is None:
        log.error(f"Image set not found: {plate_name}, day {culture_day}")
        return None, None
    
    log.info(f"Found image set: {repo_image_set}")
    
    # Get the plate from database
    plate = db.query(Plate).filter(Plate.name == plate_name).first()
    if not plate:
        log.error(f"Plate {plate_name} not found in database")
        return None, None
    
    matcher = ImageMatcher(db, plate, [repo])
    
    # Try to find existing database image set
    db_image_set = None
    for ims in matcher.imss:
        if ims.bt_key == repo_image_set.bt_key:
            db_image_set = ims
            log.info(f"Found existing database image set: {db_image_set}")
            break
    
    if db_image_set is None:
        log.info("Inserting repository image set into database")
        db_image_set, status, msg = matcher.insert_image_set(repo_image_set)
        log.info(f"Insert result: {status} - {msg}")
    
    if db_image_set is None:
        log.error("Failed to create database image set")
        return None, None
    
    # Create a store for caching results
    store_path = f"olympus_brightfield_analysis/{plate_name}/day{culture_day}/{well_name}"
    store = cache_store() / store_path
    
    cts = negatives(db)
    pipeline = Pipeline(BrightfieldAnalysis, db_image_set, cts, store)
    
    # Create the analysis instance
    analyzer = BrightfieldAnalysis(pipeline, db_image_set, well_name)
    
    log.info(f"Processing Olympus image for well {well_name}")
    
    try:
        # Load the image using the repository pattern
        image = analyzer.load_image
        log.info(f"Loaded image: {image.shape} {image.dtype}")
        
        # Create mock metadata (since Olympus doesn't have scan_metadata.json like Cytosmart)
        # We need to get pixel calibration from the image info
        rwell = repo.get_well(repo_image_set.bt_key, well_name)
        if not rwell:
            raise ValueError(f"Well {well_name} not found")
        
        # Get pixel calibration from image info
        default_image = rwell.default_image()
        pixel_calibration = default_image.info.resx  # micrometers per pixel
        pixels_per_mm = 1000.0 / pixel_calibration if pixel_calibration > 0 else 742  # fallback
        
        metadata = {
            'pixelsPerMm': pixels_per_mm,
            'experimentId': repo_image_set.experiment_id,
            'scanNumber': repo_image_set.id,
            'experimentName': getattr(repo_image_set, 'experimentName', f"{plate_name}_Day{culture_day}"),
            'numberOfWells': getattr(repo_image_set, 'numberOfWells', 384)
        }
        
        # Segment the image
        mask = analyzer.segment(image)
        log.info(f"Segmentation complete: {mask.shape}, {len(np.unique(mask))} regions")
        
        # Create object URI for measurements using repository info
        objectURI = f"gs://{repo.bucket}/{repo_image_set.bt_key}/{well_name}/BF.tiff"
        
        # Measure the mask
        cp_df, agg_df = analyzer.measure(mask, metadata, objectURI)
        log.info("Measurement complete")
        
        return cp_df, agg_df
        
    except Exception as e:
        log.error(f"Error processing image: {e}")
        traceback.print_exc()
        return None, None

def compare_results(new_df, legacy_df, metrics=None):
    """
    Compare our processing results with legacy CSV data
    """
    if metrics is None:
        # Map between our column names and legacy column names
        metrics = {
            'um_area': 'area',  # Legacy uses um_area, we use area (after scaling)
            'pixel_area': 'area',  # For pixel-level comparison
            'perimeter': 'perimeter',
            'cluster_id': 'cluster'
        }
    
    if legacy_df.empty:
        log.info("No legacy data found for comparison")
        return None
    
    if new_df is None or new_df.empty:
        log.info("No new measurements generated")
        return None
    
    log.info("=== Comparison of Results ===")
    log.info(f"Legacy CSV records: {len(legacy_df)}")
    log.info(f"New measurements: {len(new_df)}")
    
    results = {
        'record_count_legacy': len(legacy_df),
        'record_count_new': len(new_df),
        'metrics': {}
    }
    
    for new_col, legacy_col in metrics.items():
        if legacy_col in legacy_df.columns and new_col in new_df.columns:
            legacy_mean = legacy_df[legacy_col].mean()
            new_mean = new_df[new_col].mean()
            diff_pct = ((new_mean - legacy_mean) / legacy_mean) * 100 if legacy_mean != 0 else float('inf')
            
            log.info(f"{new_col} vs {legacy_col}:")
            log.info(f"  Legacy mean: {legacy_mean:.2f}")
            log.info(f"  New mean: {new_mean:.2f}")
            log.info(f"  Difference: {diff_pct:.2f}%")
            
            results['metrics'][new_col] = {
                'legacy_mean': legacy_mean,
                'new_mean': new_mean,
                'diff_pct': diff_pct
            }
    
    return results

def explore_available_data(repo):
    """Explore what data is available in the repository"""
    log.info(f"Exploring repository: {repo}")
    log.info(f"Repository details: {repo.name}, {repo.project}, {repo.bucket}")

    # Get some image sets to see what's available
    image_sets = repo.image_sets
    log.info(f"Total image sets: {len(image_sets.image_sets)}")

    # Show first few image sets
    count = 0
    for bt_key, ims in image_sets.bt_key_map.items():
        if count >= 5:  # Just show first 5
            break
        log.info(f"  {bt_key}: {getattr(ims, 'plate_name', 'unknown')} day {getattr(ims, 'day', 'unknown')}")
        if hasattr(ims, 'wells'):
            log.info(f"    Wells: {list(ims.wells.keys())[:5]}...")  # Show first 5 wells
        count += 1

    return list(image_sets.bt_key_map.keys())[:3]  # Return first 3 for testing

def main():
    """Main function to test Olympus pipeline"""

    log.info("Testing Olympus brightfield pipeline")

    # Get database connection
    db = get_db()

    # Try different Olympus repositories to see what's available
    repos_to_try = ["olympus", "olympus-tiff", "olympus-raw"]
    repo = None

    for repo_name in repos_to_try:
        try:
            repo = Repo(repo_name)
            log.info(f"Successfully connected to repository: {repo}")
            break
        except Exception as e:
            log.warning(f"Could not access {repo_name} repo: {e}")

    if repo is None:
        log.error("Could not access any Olympus repository")
        return

    # Explore available data
    available_bt_keys = explore_available_data(repo)

    if not available_bt_keys:
        log.warning("No image sets found in repository")
        return

    # Try to process the first available image set
    bt_key = available_bt_keys[0]
    log.info(f"Testing with bt_key: {bt_key}")

    # Get the image set
    ims = repo.image_sets.bt_key_map[bt_key]
    plate_name = getattr(ims, 'plate_name', 'unknown')
    culture_day = getattr(ims, 'day', 0)

    # Get first available well
    if hasattr(ims, 'wells') and ims.wells:
        well_name = list(ims.wells.keys())[0]
        log.info(f"Testing with well: {well_name}")

        # Process image with new pipeline
        cp_df, agg_df = process_olympus_image_with_repo(db, repo, plate_name, culture_day, well_name)

        if cp_df is not None:
            log.info("=== Processing Results ===")
            log.info(f"Individual cell measurements: {len(cp_df)} records")
            log.info(f"Aggregated measurements: {len(agg_df) if agg_df is not None else 0} records")

            # Show sample of results
            log.info("Sample individual measurements:")
            log.info(cp_df[['label', 'area', 'perimeter', 'circularity', 'cluster']].head())

            if agg_df is not None:
                log.info("Sample aggregated measurements:")
                log.info(agg_df[['cluster', 'count_area', 'mean_area', 'mean_circ']].head())

            # Save results
            output_prefix = f"olympus_demo_{plate_name}_day{culture_day}_{well_name}"
            cp_df.to_csv(f"{output_prefix}_individual.csv", index=False)
            if agg_df is not None:
                agg_df.to_csv(f"{output_prefix}_aggregated.csv", index=False)

            log.info(f"Results saved with prefix: {output_prefix}")
            log.info("Demo completed successfully!")
        else:
            log.warning("Processing failed")
    else:
        log.warning("No wells found in image set")

if __name__ == "__main__":
    main()
