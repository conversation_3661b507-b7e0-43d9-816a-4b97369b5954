from base.database import get_db
from plates.model import Plate
from images.pipeline import Pipeline, negatives
from images.model import ImageSet as DbImageSet
from images.match import ImageMatcher
from images.repo import all_repos, ImageRepo, Repo
from base.util import make_logger
from google.cloud import storage, bigquery
from base.io import Store, cache_store, FileStore
import pandas as pd
import json
import os
import traceback
import numpy as np

from images.brightfield import BrightfieldAnalysis
log = make_logger(__name__)

# Configuration for Olympus pipeline
project_id = "development-311316"  # Use development project for testing
olympus_bucket = "mellitos-lab-data"  # Use development bucket
olympus_csv_bucket = "ix83-brightfield-measurments"  # Legacy CSV bucket (production)
model_name = "MCL_CP_005"

storage_client = storage.Client(project=project_id)
bq_client = bigquery.Client(project=project_id)

def get_olympus_csv_data(plate_name, culture_day, well_name=None):
    """
    Get measurements from the legacy Olympus CSV files in GCS
    For now, return empty DataFrame since we don't have access to production bucket
    """
    log.info(f"Legacy CSV data access not available for {plate_name}, day {culture_day}, well {well_name}")
    log.info("This would normally read from gs://ix83-brightfield-measurments/MCL_CP_005/")

    # Return empty DataFrame - in real scenario this would read from production bucket
    return pd.DataFrame()

def find_olympus_image_set(repo, plate_name, culture_day):
    """
    Find an Olympus image set in the repository
    """
    log.info(f"Looking for Olympus image set: {plate_name}, day {culture_day}")
    
    # Try to find the image set
    ims = repo.image_set(plate_name, culture_day)
    if ims:
        log.info(f"Found image set: {ims}")
        return ims
    
    # If not found, list available image sets for debugging
    log.info("Available image sets:")
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if hasattr(image_set, 'plate_name') and plate_name in str(image_set.plate_name):
            log.info(f"  {bt_key}: {image_set.plate_name} day {getattr(image_set, 'day', 'unknown')}")
    
    return None

def process_olympus_image_with_repo(db, repo, plate_name, culture_day, well_name):
    """Process an Olympus image using the repository pattern and BrightfieldAnalysis"""
    
    repo_image_set = find_olympus_image_set(repo, plate_name, culture_day)
    if repo_image_set is None:
        log.error(f"Image set not found: {plate_name}, day {culture_day}")
        return None, None
    
    log.info(f"Found image set: {repo_image_set}")
    
    # Get the plate from database
    plate = db.query(Plate).filter(Plate.name == plate_name).first()
    if not plate:
        log.error(f"Plate {plate_name} not found in database")
        return None, None
    
    matcher = ImageMatcher(db, plate, [repo])
    
    # Try to find existing database image set
    db_image_set = None
    for ims in matcher.imss:
        if ims.bt_key == repo_image_set.bt_key:
            db_image_set = ims
            log.info(f"Found existing database image set: {db_image_set}")
            break
    
    if db_image_set is None:
        log.info("Inserting repository image set into database")
        db_image_set, status, msg = matcher.insert_image_set(repo_image_set)
        log.info(f"Insert result: {status} - {msg}")
    
    if db_image_set is None:
        log.error("Failed to create database image set")
        return None, None
    
    # Create a store for caching results
    store_path = f"olympus_brightfield_analysis/{plate_name}/day{culture_day}/{well_name}"
    store = cache_store() / store_path
    
    cts = negatives(db)
    pipeline = Pipeline(BrightfieldAnalysis, db_image_set, cts, store)
    
    # Create the analysis instance
    analyzer = BrightfieldAnalysis(pipeline, db_image_set, well_name)
    
    log.info(f"Processing Olympus image for well {well_name}")
    
    try:
        # Load the image using the repository pattern
        image = analyzer.load_image
        log.info(f"Loaded image: {image.shape} {image.dtype}")
        
        # Create mock metadata (since Olympus doesn't have scan_metadata.json like Cytosmart)
        # We need to get pixel calibration from the image info
        rwell = repo.get_well(repo_image_set.bt_key, well_name)
        if not rwell:
            raise ValueError(f"Well {well_name} not found")
        
        # Get pixel calibration from image info
        default_image = rwell.default_image()
        pixel_calibration = default_image.info.resx  # micrometers per pixel
        pixels_per_mm = 1000.0 / pixel_calibration if pixel_calibration > 0 else 742  # fallback
        
        metadata = {
            'pixelsPerMm': pixels_per_mm,
            'experimentId': repo_image_set.experiment_id,
            'scanNumber': repo_image_set.id,
            'experimentName': getattr(repo_image_set, 'experimentName', f"{plate_name}_Day{culture_day}"),
            'numberOfWells': getattr(repo_image_set, 'numberOfWells', 384)
        }
        
        # Segment the image
        mask = analyzer.segment(image)
        log.info(f"Segmentation complete: {mask.shape}, {len(np.unique(mask))} regions")
        
        # Create object URI for measurements
        objectURI = f"gs://{olympus_bucket}/{repo_image_set.bt_key}/{well_name}/BF.tiff"
        
        # Measure the mask
        cp_df, agg_df = analyzer.measure(mask, metadata, objectURI)
        log.info("Measurement complete")
        
        return cp_df, agg_df
        
    except Exception as e:
        log.error(f"Error processing image: {e}")
        traceback.print_exc()
        return None, None

def compare_results(new_df, legacy_df, metrics=None):
    """
    Compare our processing results with legacy CSV data
    """
    if metrics is None:
        # Map between our column names and legacy column names
        metrics = {
            'um_area': 'area',  # Legacy uses um_area, we use area (after scaling)
            'pixel_area': 'area',  # For pixel-level comparison
            'perimeter': 'perimeter',
            'cluster_id': 'cluster'
        }
    
    if legacy_df.empty:
        log.info("No legacy data found for comparison")
        return None
    
    if new_df is None or new_df.empty:
        log.info("No new measurements generated")
        return None
    
    log.info("=== Comparison of Results ===")
    log.info(f"Legacy CSV records: {len(legacy_df)}")
    log.info(f"New measurements: {len(new_df)}")
    
    results = {
        'record_count_legacy': len(legacy_df),
        'record_count_new': len(new_df),
        'metrics': {}
    }
    
    for new_col, legacy_col in metrics.items():
        if legacy_col in legacy_df.columns and new_col in new_df.columns:
            legacy_mean = legacy_df[legacy_col].mean()
            new_mean = new_df[new_col].mean()
            diff_pct = ((new_mean - legacy_mean) / legacy_mean) * 100 if legacy_mean != 0 else float('inf')
            
            log.info(f"{new_col} vs {legacy_col}:")
            log.info(f"  Legacy mean: {legacy_mean:.2f}")
            log.info(f"  New mean: {new_mean:.2f}")
            log.info(f"  Difference: {diff_pct:.2f}%")
            
            results['metrics'][new_col] = {
                'legacy_mean': legacy_mean,
                'new_mean': new_mean,
                'diff_pct': diff_pct
            }
    
    return results

def main():
    """Main function to test Olympus pipeline numerical equivalency"""
    
    # Test parameters - adjust these based on available data
    plate_name = "Plate0174"  # Example from context
    culture_day = 96  # Example from context
    well_name = "A1"  # Test well
    
    log.info(f"Testing Olympus pipeline for {plate_name}, day {culture_day}, well {well_name}")
    
    # Get database connection
    db = get_db()
    
    # Get Olympus repository - try different repos to see what's available
    try:
        repo = Repo("olympus")  # Try main olympus repo first
        log.info(f"Using repository: {repo}")
    except Exception as e:
        log.warning(f"Could not access olympus repo: {e}")
        try:
            repo = Repo("olympus-tiff")  # Fallback to TIFF repo
            log.info(f"Using repository: {repo}")
        except Exception as e2:
            log.error(f"Could not access any Olympus repository: {e2}")
            return
    
    # Get legacy CSV data
    legacy_df = get_olympus_csv_data(plate_name.replace("Plate", ""), culture_day, well_name)
    
    # Process image with new pipeline
    cp_df, agg_df = process_olympus_image_with_repo(db, repo, plate_name, culture_day, well_name)
    
    # Compare results
    if cp_df is not None and not legacy_df.empty:
        comparison = compare_results(cp_df, legacy_df)
        
        if comparison:
            log.info("Comparison Results:")
            log.info(json.dumps(comparison, indent=2))
            
            # Save results for further analysis
            cp_df.to_csv(f"olympus_new_measurements_{plate_name}_day{culture_day}_{well_name}.csv", index=False)
            legacy_df.to_csv(f"olympus_legacy_measurements_{plate_name}_day{culture_day}_{well_name}.csv", index=False)
            
            log.info("Results saved to CSV files for detailed comparison")
        else:
            log.warning("Could not perform comparison")
    else:
        log.warning("Insufficient data for comparison")

if __name__ == "__main__":
    main()
