from typing import List

from sqlalchemy.orm import aliased
from sqlalchemy.orm.session import Session
from fastapi import Depends
from fastapi.responses import HTMLResponse

from base.util import word_s, word_list, lazy
from app.html import node, when
from base.reps import Rep, rep
from app.auth import context
from app.layouts import Context
from app.table import reptable

from workflow.orm.rar import Request, Action, Result
from workflow.flow import FlowStep, app
from workflow.workflow import Flow, Workflow
from workflow.culture import CultureFlow, ImageIn
from workflow.assay import AssayFlow
from plates.model import Plate

from base.util import make_logger
log = make_logger(__name__)

#
# Workflow status reporting
#        
class StepPlateReport:
    def __init__(self, db: Session, step: FlowStep, plate: Plate):
        self.db = db
        self.step = step
        self.plate = plate
    
    @lazy
    def query(self):
        """Query the requests"""
        reqt = aliased(Request) # need alias here because inputs could also contain a Request
        if hasattr(self.step.out, "plate"):
            return self.db.query(reqt, self.step.out, Action, Result).\
                join(Action, Action.id == reqt.action, isouter= True).\
                join(Result, Result.id == Action.id, isouter= True).\
                filter(reqt.id == self.step.out.request).\
                filter(self.step.out.plate == self.plate.id)
        elif hasattr(self.step.out, "image_req"):
            return self.db.query(reqt, self.step.out, Action, Result, ImageIn).\
                join(Action, Action.id == reqt.action, isouter= True).\
                join(Result, Result.id == Action.id, isouter= True).\
                filter(reqt.id == self.step.out.request).\
                filter(self.step.out.image_req == ImageIn.request).\
                filter(ImageIn.plate == self.plate.id)

    @lazy
    def requests(self):
        """Query the requests"""
        log(f"First: {self.query.first()}")
        return [(r,act,res) for r, _, act, res, *_ in self.query.all()]
    
class PlateReport:
    def __init__(self, db: Session, workflows: List[Workflow], plate: Plate):
        self.workflows = workflows
        self.reports = [StepPlateReport(db, s, plate) for wf in workflows for s in wf.steps]
        self.requests = [r for rep in self.reports for r in rep.requests]
        self.plate = plate
        self.columns = {
            "id": "req.id", 
            "step": "req.step",
            "created":"req.created", 
            "started": "Action.started",
            "finished": "Result.ended",
        }
        self.rep = Rep.join(("req", Request), Action, Result)(self.columns)
        
    def html(self, ht):
        """Render the step record as HTML"""
        n = len(self.requests)
        return node("")(
            ht.h2(
                f"{n} work {word_s('request', n)} "+\
                f"in {word_list([wf.name for wf in self.workflows])} "+\
                f"for plate {self.plate.name}:"),
            when(n > 0)(
                reptable(self.rep, self.requests)(id="request"),
            ),
        )
    
class ReportsMain:
    def __init__(self):
        self.path = "workflow/reports"
        self.route()

    async def list_plates(self, ht: Context = Depends(context)):
        plates = ht.db.query(Plate).all()
        prep = rep(Plate)
        return ht.page(f"All plates",
            ht.h2(f"All {word_s('plate')}:"),
            ht.h3("Double click one for details."),
            reptable(prep, plates)(id="plates", href=ht.url(f"{self.path}/plate")),
        ).pretty()
    
    async def plate_report(self, plate_id: int, ht: Context = Depends(context)):
        plate = ht.db.query(Plate).get(plate_id)
        if not plate:
            return ht.h2(f"Plate {plate_id} not found.")
        return ht.page("Plate report, id={plate_id}",
            PlateReport(ht.db, [Flow(CultureFlow), Flow(AssayFlow)], plate).html(ht)
        ).pretty()
        
    def route(self):
        app.get(f"/reports/plates/", response_class=HTMLResponse)(self.list_plates)
        app.get(f"/reports/plate/"+"{plate_id}/", response_class=HTMLResponse)(self.plate_report)

Reports = ReportsMain()
Reports.route()
