
from typing import List, Named<PERSON><PERSON>le
from enum import Enum
from datetime import datetime, <PERSON><PERSON><PERSON>
from dataclasses import dataclass
from sqlalchemy import func, select
from sqlalchemy.orm import Session

from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from fastapi import Depends, Request as HttpRequest

from base.database import flush
from base.io import file_store, File
from base.reps import Rep, rep, ObjRep
from app.table import reptable
from app.auth import context
from app.layouts import Context
from app.html import node
from base.database import Base
from plates.render import plate_map

from plates.model import Plate, PlateFormat, Vendor, Supernatant, Dilution, PlateMap, MapGroup, Dose, WellPos
from workflow.orm.rar import Result
from workflow.orm.steps import ExtractIn, ExtractOut, DiluteIn, DiluteOut, AssayIn, Quadrant
from workflow.flow import FlowStep, ResultSubmit, Workflow, app, UserError, ActionState

from base.util import make_logger
log = make_logger(__name__)
#
#  This defines the concrete steps in the Assay workflow.
#    See https://docs.google.com/document/d/1eUL2ULnZemlwgRd9O0s9cQUBRywTYW2xamRcBnT8r8I/edit
#


# Extension of results to capture newly created plates
@dataclass
class NewPlateResult(ResultSubmit):
    plate_vendor: Vendor
    barcode: int

#
#  Extraction of supernatant
#
class ExtractStep(FlowStep):
    def __init__(self):
        self.description = "Extract supernatant from culture plate"
        self.filters = { "plate": Plate.type == "culture" }
        self.ResultSubmit = NewPlateResult
        super().__init__("extract", ExtractIn)

        
    async def next(self, db: Session, res: NewPlateResult, inp: ExtractIn):
        # Do not queue unless successful
        log(f"ExtractStep: out={res}")
        
        # Load the source plate
        plate = db.query(Plate).get(inp.plate)
        
        # count existing supernatant plates
        n = db.query(Supernatant).filter(Supernatant.source==inp.plate).count()
        log(f"  Found {n} existing supernatant plates")
        
        # Create the supernatant plate:
        snp = Plate(
            name = f"{plate.name}_S{n+1:d}",
            format = plate.format,
            type = "supernatant",
            vendor = res.plate_vendor.name,
            barcode = res.barcode
        )
        db.add(snp)
        flush(db, snp)
        db.add(Supernatant(id=snp.id, source=plate.id))
        db.add(ExtractOut(request=inp.request, plate=snp.id))
        
#       
#  Dilution of supernatant
#
class DiluteStep(FlowStep):
    def __init__(self):
        self.description = "Dilute supernatant"
        self.filters = { "plate": Plate.type == "supernatant" }
        self.ResultSubmit = NewPlateResult
        super().__init__("dilute", DiluteIn)

    async def next(self, db: Session, res: NewPlateResult, inp: DiluteIn):
        # Do not queue unless successful
        log(f"DiluteStep: out={res}")
        
        # Load the source plate
        plate = db.query(Plate).get(inp.plate)
        
        # count existing dilution plates
        n = db.query(Dilution).filter(Dilution.source==inp.plate).count()
        log(f"  Found {n} existing dilution plates")
        
        # Create the supernatant plate:
        dp = Plate(
            name = f"{plate.name}_D{n+1:d}",
            format = plate.format,
            type = "supernatant",
            vendor = res.plate_vendor.name,
            barcode = res.barcode
        )
        db.add(dp)
        flush(db, dp)
        db.add(Dilution(id=dp.id, source=plate.id, factor=inp.dilution))
        db.add(DiluteOut(request=inp.request, plate=dp.id))
            

class AssayMapInput(NamedTuple):
    """Parameters needed to create a new assay control plate map."""
    name: str
    description: str
    format: PlateFormat
    blanks: str
    standards: str
    
# Extension of results to capture data file
@dataclass
class AssayResult(ResultSubmit):
    assay_data: File
    
class AssayStep(FlowStep):
    def __init__(self):
        self.description = "Assay supernatant"
        self.filters = { 
            "plate": Plate.type == "supernatant",
            "platemap": PlateMap.type == "assay"
        }
        self.ResultSubmit = AssayResult
        self.single = True   # only allow one request per action
        self.data_store = file_store("assay")
        #self.data_store = file_store("assay", remote="assay-dev")
        super().__init__("assay", AssayIn)
        self.req_rep.default("assay", None)
    
    def data_file_name(self, db, inp: AssayIn):
        plate = db.query(Plate).get(inp.plate)
        log(f"Plate: {plate}")
        return f"{inp.assay}/{inp.request}_{plate.name}_{inp.quadrant}.xlsx"

    def act_view_extra(self, ht: Context, state: ActionState):
        if state.requests.n != 1: 
            raise ValueError(f"Assay action must have exactly one request: {state.requests.n}")
        inp = state.requests.items[0][1]
        plate = state.requests.items[0][2]
        fn = self.data_file_name(ht.db, inp)
        downl = ht.p("No assay data uploaded, yet.")
        if self.data_store.exists(fn):
            downl = \
                ht.p(f"Download assay data for plate {plate.name}, quadrant {inp.quadrant}: ") +\
                ht.link(f"{self.path}/download/{inp.request}")(fn)
        return node("div")(ht.h3("Assay Data"), downl)
        
        
    #  A10,A11,A12
    #  A1=80,A2=40,A3=20,A4=10,A5=5,A6=2.5,A7=1.25,A8=0.625,A9=0
    #  H1=80,H2=40,H3=20,H4=10,H5=5,H6=2.5,H7=1.25,H8=0.625,H9=0
    #
    def new_controls(self, db: Session, input: AssayMapInput):
        """Create a new assay control plate map.
             Currently there is only one default map with 8 positive controls and 1 negative control.
        """
        if input.format.name != "96-well": 
            raise ValueError("Assay plates must be 96-well, not "+input.format.name)
        pm = PlateMap(
            name = input.name,
            description = input.description,
            type = "assay",
            format_name = input.format.name
        )
        blank = MapGroup(group_type="negative")
        standards = MapGroup(group_type="standard")
        pm.map_groups = [blank, standards]
        wells = {wp.well_name: wp for wp in input.format.wells}
        
        # Add the blanks
        bs = input.blanks.split(",")
        for well in bs:
            well = well.strip()
            if well not in wells: raise ValueError(f"Well {well} not in plate format")
            blank.doses.append(
                Dose(concentration__value=0.0, concentration__unit="uM", well_pos=wells[well])
            )
        
        # Add the standards
        svs = input.standards.split(",")
        for sv in svs:
            ss = sv.split("=")
            if len(ss)!=2: 
                raise ValueError('Standards must be in the form "well=concentration", found '+s)
            w, c = [s.strip() for s in ss]
            if w not in wells: raise ValueError(f"Well {well} not in plate format")
            standards.doses.append(
                Dose(concentration__value=float(c), concentration__unit="uM", well_pos=wells[w])
            )
            
        db.rollback()
        log(f"Adding plate map {rep(PlateMap).repr(pm)}")
        db.add(pm)
        db.commit()
        return
    
    async def next(self, db: Session, res: AssayResult, inp: AssayIn):
        # Do not queue unless successful
        log(f"AssayStep: res={res}")
        
        # Get the canonical file name and extension
        fn = self.data_file_name(db, inp)
        ext = fn.split(".")[-1]
        
        # Upload the assay data
        file_name = res.assay_data.filename
        if file_name=="": raise UserError("Please provide an assay data file to upload")
        ns = file_name.split(".")
        if len(ns) < 2 or ns[-1]!=ext: raise UserError(f"Please provide a file with a {ext} extension")
        file_type = res.assay_data.content_type
        file_content = await res.assay_data.read()
        log(f"File: {file_name} : {file_type} -> {self.data_store}")
        
        # Save the file
        log(f"Store: {len(file_content)} bytes in {fn}")
        with self.data_store.open(fn, "wb") as f:
            f.write(file_content)
        #self.data_store.save(file_content, fn)

    async def controls(self, ht: Context = Depends(context)):
        """View and manage assay control maps"""
        
        # Query the plate maps, count groups and wells
        groups = ht.db.query(
            func.count(MapGroup.id).label("group_count"), 
            MapGroup.plate_map_id
        ).group_by(MapGroup.plate_map_id).subquery()
        
        wells = ht.db.query(
            func.count(Dose.id).label("well_count"), 
            MapGroup.plate_map_id
        ).filter(
            MapGroup.id == Dose.map_group_id
        ).group_by(MapGroup.plate_map_id).subquery()

        ts = ht.db.query(groups, wells, PlateMap).\
            filter(PlateMap.type == "assay").\
            filter(PlateMap.id == groups.c.plate_map_id).\
            filter(PlateMap.id == wells.c.plate_map_id).\
        all()
        pms = [pm for _, _, _, _, pm in ts]
        for pm in pms:
            log(f"PlateMap: {pm}")
        
        # Columns for the table
        columns = {
            "id": "PlateMap.id",
            "type": "PlateMap.type",
            "name": "PlateMap.name",
            "description": "PlateMap.description",
            "groups": "groups",
            "wells": "wells",
            "format": "PlateMap.format_name",
            "created": "PlateMap.created",
        }
        rep = Rep.join({"groups": int, "map_id1": int, "wells": int, "map_id2": int}, PlateMap)(columns)

        # How to render a plate map
        def pmap(pm):
            t = pm.name
            if pm.name is None: t = "unnamed"
            if pm.description is not None: t = t + " - "+pm.description
            return node("div")(
                ht.h3(f"Plate map {pm.id} ({pm.format_name}): {t}"),
                node("p")("Hover over a well to see more information."),
                control_map(ht, pm),
                node("hr")(),
            )
        
        # Now compose the page
        return ht.page("Assay Controls",
            ht.h2("Assay Controls"),
            ht.h3("The following plate maps are available for assay controls:"),
            reptable(rep, ts),
            tabs(ht, {pm.id: pmap(pm) for pm in pms}),
            ht.link(f"{self.path}/map")("Create a new assay control plate map")
        ).pretty()
    
    async def create_map(self, ht: Context = Depends(context)):
        """Create a new plate map"""
        return ht.page("Create Assay Plate Map",
            ht.h2("Create Assay Plate Map"),
            ht.h3("Enter blanks as a comma separated list of well names (e.g. A10,A11,A12)"),    
            ht.h3("Enter standards as a comma separated list of well concentrations (e.g. A1=80,A2=40,A3=20,...)"),    
            ht.rform(f"{self.path}/map", rep(AssayMapInput))
        ).pretty()
    
    class MapForm(rep(AssayMapInput).form_parser()): pass
    
    async def create_map_post(self, input: MapForm = Depends(), ht: Context = Depends(context)):
        """Create a new plate map"""
        format = ht.db.query(PlateFormat).get(input.format)
        inp = AssayMapInput(
            name=input.name, description=input.description, format=format, 
            blanks=input.blanks, standards=input.standards
        )
        log(f"Create map: {inp}")
        self.new_controls(ht.db, inp)
        return RedirectResponse(url=f"/{self.path}/controls/", status_code=302)
    
    async def download(self, req_id: int, ht: Context = Depends(context)):
        """Download the assay data file"""
        try:
            inp = ht.db.query(AssayIn).get(req_id)
            fn = self.data_file_name(ht.db, inp)
            log(f"Download assay data for {fn} from {self.data_store}")
            fobj = self.data_store.open(fn, "rb")
        except Exception as e:
            log(f"Error: {e}")
            return ht.error(f"Error downloading file: {e}")
        def stream():
            with fobj as f:
                while chunk := f.read(1048): yield chunk
        return StreamingResponse(stream(), 
            media_type="application/octet-stream", 
            headers={'Content-Disposition': 'attachment; filename=assay_'+fn.replace("/", "_")}
        )
    
    def links(self, ht: Context):
        return [
            ht.link(f"{self.path}/controls")("Manage Assay Controls")
        ]
    
    def route(self):
        super().route()
        app.get(f"/{self.router_path}/controls/", response_class=HTMLResponse)(self.controls)
        app.get(f"/{self.router_path}/map/", response_class=HTMLResponse)(self.create_map)
        app.post(f"/{self.router_path}/map/", response_class=HTMLResponse)(self.create_map_post)
        app.get(f"/{self.router_path}/download/"+"{req_id}/", response_class=HTMLResponse)(self.download)

#
#   Tabs and plate map rendering
#
def tabs(ht: Context, xd: dict):
    return node("div", id="tabs")(
        node("div", id="notab")(ht.h3("Select an item to show details")),
        *[node("div", **{"class": "tab"}, id=f"tab-{id}")(x) for id, x in xd.items()]
    )

control_symbol = {
    "positive": "+",
    "negative": "-",
    "experimental": "X",
    "standard": "S"
}

def control_map(ht: Context, pm: PlateMap):    
    ds = ht.db.query(WellPos, Dose, MapGroup).\
        filter(MapGroup.plate_map_id==pm.id).\
        filter(Dose.map_group_id==MapGroup.id).\
        filter(Dose.well_pos_id==WellPos.id).\
    all()
    dss = {wp.well_name: (d, g) for wp, d, g in ds}
    
    def well(wp: WellPos):
        d, g = dss.get(wp.well_name, (None, None))
        if d is None: 
            return node("div", title="Unmapped")(" ")
        t = f"{wp.well_name} ({wp.row} {wp.col}): {g.group_type} {d.concentration__value} {d.concentration__unit}"
        return node("div", title=t)(control_symbol[g.group_type]) 
    
    return plate_map(ht, pm.format_name, well)

        
        
#
#  The Assay workflow
#
class AssayFlow(Workflow):
    def __init__(self):
        super().__init__("Assay", ExtractStep(), DiluteStep(), AssayStep())
                      
        
   


    
