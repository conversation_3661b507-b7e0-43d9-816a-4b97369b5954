from sqlalchemy import <PERSON><PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from datetime import datetime
from base.database import Base, UtcDateTime
import app.model as security
import enum

# Generic Workflow Schema for the Request, Action, and Result (RAR) model.
#   Inputs and outputs are specific to the workflow steps and are linked
#   to the request, action, and result tables in `models_steps.py`.

class Project(Base):
    """Project allows for requests to be grouped into separate instances of the workflow."""
    __tablename__ = 'project'
    # This field defines a default set of records to facilitate new database initialization:
    #   It also causes this entity to be multiple choice in GUI forms.
    __enum__ = [
        ("production", "All requests that matter go here"),
        ("backfill", "Requests for backfilling legacy data")
    ]
    name:     Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)

class Step(Base):
    """A step in the workflow, such as feed, image, treat, etc."""
    __tablename__ = "step"
    __enum__ = [
        ("seed", "Seed a plate"),
        ("feed", "Feed the cells"),
        ("image", "Image a plate"),
        ("analyze", "Analyze the images")
    ]
    name:        Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)

class Station(Base):
    """A station where work for a step can be performed, such as an instrument or a location."""
    __tablename__ = "station"
    __enum__ = [
        ("seed station", "seed", "Where we do the seeding of plates"),
        ("feed station", "feed", "Where we do the feeding of plates"),
        ("cytosmart", "image", "The CytoSmart imaging station"),
        ("olympus", "image", "The Olympus imaging station"),
    ]
    name:        Mapped[str] = mapped_column(primary_key=True)
    step:        Mapped[str] = mapped_column(ForeignKey("step.name"), nullable=False)
    description: Mapped[str] = mapped_column(nullable=False)

class Sop(Base):
    """A standard operating procedure."""
    __tablename__ = "sop"
    name:        Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)
    step:        Mapped[str] = mapped_column(ForeignKey("step.name"), nullable=False)
    bot:        Mapped[bool] = mapped_column(nullable=False) # True if the SOP is performed by a compute worker.
    version:     Mapped[str] = mapped_column(nullable=False) # Version of the SOP, such as 1.0.0
    added:  Mapped[datetime] = mapped_column(UtcDateTime, nullable=False) # Time the SOP was added to the database.
    url:         Mapped[str] = mapped_column(nullable=False) # URL to the SOP, must be a permalink

class SopParam(Base):
    """Parameters for a standard operating procedure."""
    __tablename__ = "sop_param"
    name:        Mapped[str] = mapped_column(primary_key=True)
    step:        Mapped[str] = mapped_column(ForeignKey("step.name"), nullable=False)
    description: Mapped[str] = mapped_column(nullable=False) # Description of the parameter.
    type:        Mapped[str] = mapped_column(nullable=False) # Type of the parameter, such as "int", "float", "str", "bool"
    default:     Mapped[str] = mapped_column(nullable=True)  # Default value for the parameter.
    required:    Mapped[bool] = mapped_column(nullable=False) # True if the parameter is required.

class SopParamValue(Base):
    """Values for a standard operating procedure."""
    __tablename__ = "sop_param_value"
    id:          Mapped[int] = mapped_column(primary_key=True)
    sop:         Mapped[str] = mapped_column(ForeignKey("sop.name"), nullable=False)
    param:       Mapped[str] = mapped_column(ForeignKey("sop_param.name"), nullable=False)
    value:       Mapped[str] = mapped_column(nullable=False) # Value for the parameter.

class Request(Base):
    """A request indicates a scheduled action. 
       Requests that have 'after' in the past are considered queued, those with 'after'
       in the future are considered pending. Requests with 'before' in the past are still
       queued, but overdue.
    """
    __tablename__ = "request"
    id:       Mapped[int]      = mapped_column(primary_key=True)
    project:  Mapped[str]      = mapped_column(ForeignKey("project.name"), nullable=False)
    step:     Mapped[str]      = mapped_column(ForeignKey("step.name"))
    created:  Mapped[datetime] = mapped_column(UtcDateTime, nullable=False) # time when request was created.
    schedule: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False) # Scheduled time for request to become active.
    deadline: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False) # Deadline for the request.
    comment:  Mapped[str]      = mapped_column(nullable=True)  # Operator comment on the request.
    action:   Mapped[int]      = mapped_column(ForeignKey("action.id"), nullable=True)  # Action assigned to the request.
    
class Action(Base):
    """Inserting an action assigns a worker to one or more requests and marks the requests as busy
       until the action is completed by inserting a result.
    """
    __tablename__ = "action"
    id: Mapped[int]           = mapped_column(primary_key=True, autoincrement=True)
    project: Mapped[str]      = mapped_column(ForeignKey("project.name"), nullable=False)
    step: Mapped[str]         = mapped_column(ForeignKey("step.name"), nullable=False)
    operator: Mapped[str]     = mapped_column(ForeignKey("user.name"), nullable=False)
    station: Mapped[str]      = mapped_column(ForeignKey("station.name"), nullable=False)
    sop: Mapped[str]          = mapped_column(ForeignKey("sop.name"), nullable=False)
    started: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False)  # Time the action was started.

class ActionTag(Base):
    """Tags for an action. This allows state for actions, 
       i.e. whether barcodes have been checked or whether a file has been uploaded."""
    __tablename__ = "action_tag"
    id: Mapped[int]      = mapped_column(primary_key=True)
    action: Mapped[int]  = mapped_column(ForeignKey("action.id"), nullable=False)
    name: Mapped[str]    = mapped_column(nullable=False)
    payload: Mapped[str] = mapped_column(nullable=False)
    
class ResultReason(Base):
    __tablename__ = "result_reason"
    __enum__ = [
        ("pass", None, "The action was successful"),
        ("fail", None, "The action failed for an unspecified reason")
    ]
    name: Mapped[str] = mapped_column(primary_key=True)
    step: Mapped[str] = mapped_column(ForeignKey("step.name"), nullable=True)
    description: Mapped[str] = mapped_column(nullable=False)
       
class Result(Base):
    """Inserting a result marks the request as complete and the action as finished.
       If the action failed, the reason for failure is included in the 'fail' field.
    """
    __tablename__ = "result"
    id: Mapped[int]         = mapped_column(ForeignKey("action.id"), primary_key=True)
    ended: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False) # Time the action was finished.
    result: Mapped[str]     = mapped_column(ForeignKey("result_reason.name"), nullable=False)  # Reason for failure. NULL means success.
    comment: Mapped[str]    = mapped_column(nullable=True)  # Operator comment on the result.

    def __repr__(self):
        return f"Result({self.id}, {self.ended}, {self.result}, {self.comment})"



