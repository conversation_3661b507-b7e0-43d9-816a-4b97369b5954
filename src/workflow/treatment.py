from typing import List
from datetime import datetime, timedelta

from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi import Depends
from sqlalchemy.orm import Session
from app.auth import context
from workflow.flow import app

from app.layouts import Context

from base.reps import rep
from plates.model import Plate, Donor, Medium, PlateFormat, Vendor, PlateMap
from treatments.model import LibraryPlate, LibraryMapping
from images.model import Device
from workflow.orm.steps import SeedIn, FeedIn, ImageIn, ScreenAssignmentIn, DoseResponseAssignmentIn, DoseIn
from workflow.flow import FlowStep, app, ResultSubmit, Workflow
    
class ScreeningAssignmentStep(FlowStep):
    def __init__(self):
        self.description = "assign treatments to culture plate for screenig"
        self.filters = {"plate": Plate.type == "culture", "plate_map": PlateMap.type == "screening"}
        super().__init__("screen", ScreenAssignmentIn)
    
    async def next(self, db: Session, res: ResultSubmit, inp: ScreenAssignmentIn):
        # TODO create a TreatmentBatch and assign it to DoseStep request
        db.query(LibraryPlate).filter_by(id=inp.library_plate).first()
        

class DoseResponseAssignmentStep(FlowStep):
    def __init__(self):
        self.description = "assign treatments to culture plate for dose response"
        self.filters = {"plate": Plate.type == "culture", "plate_map": PlateMap.type == "dosing"}
        self.multiple = set(["treatment"])
        super().__init__("dose response", DoseResponseAssignmentIn)
    
    async def next(self, db: Session, res: ResultSubmit, inp: DoseResponseAssignmentIn):
        # TODO figure out how to get collection of treatmetns and create treatment batch
        db.query(PlateMap).filter_by(id=inp.platemap).first()

class DoseStep(FlowStep):
    def __init__(self):
        self.description = "regular dosing of plate"
        self.filters =  {"plate": Plate.type == "culture"}
        super().__init__("dose", DoseIn)
    
    async def next(self, db: Session, res: ResultSubmit, inp: DoseIn):
        # TODO schedule next dosing with same treatment batch the next day
        pass

class TreatmentFlow(Workflow):
    def __init__(self):
        super().__init__("Treatment", DoseStep(), DoseResponseAssignmentStep(), ScreeningAssignmentStep())
