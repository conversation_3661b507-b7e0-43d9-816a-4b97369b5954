from workflow.flow import FlowStep, Workflow, ResultSubmit
from workflow.orm.steps import PipelineIn, PipelineOut
from images.model import ImageSet, Device, ResultSet
from sqlalchemy.orm import Session
from dataclasses import dataclass

@dataclass
class NewResultSetResult(ResultSubmit):
    result_set: ResultSet

class PipelineStep(FlowStep):
    def __init__(self):
        self.description = "Image processing pipeline step"
        super().__init__("pipeline", PipelineIn)

    async def next(self, db: Session, res: NewResultSetResult, inp: PipelineIn):
        pass

class PipelineFlow(Workflow):
    def __init__(self):
        super().__init__("Pipeline", PipelineStep())
