from typing import List
from dataclasses import dataclass

from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi import Depends
from sqlalchemy.orm import Session
from sqlalchemy import text, bindparam
from app.auth import context
from plates.array import PlateArray
from workflow.flow import app, Workflow

from app.layouts import Context

from base.reps import rep
from plates.model import Plate, Donor, Medium, PlateFormat, Vendor, PlateMap
from images.model import Device, ImageSet
from workflow.orm.rar import Request
from workflow.orm.steps import SeedDonor, SeedIn, FeedIn, ImageIn, UploadIn, UploadOut
from workflow.flow import FlowStep, app, ResultSubmit
from workflow.orm.steps import PipelineIn
from base.util import make_logger
log = make_logger(__name__)
#
#  Seeding of culture plates
#
class SeedStep(FlowStep):
    def __init__(self):
        self.description = "Seed culture plates"
        self.multiple = set(["plate"])
        self.once = set(["plate"])
        self.filters = { "plate": Plate.type == "culture" }
        self.array = PlateArray(<PERSON><PERSON>, SeedDonor, "seeding")
        super().__init__("seed", SeedIn)
    
    async def next(self, db: Session, res: ResultSubmit, inp: SeedIn):
        # Feed request
        feed_req = self.new_request_days(db, "feed", days=(1, 4))
        medium = db.query(Medium).get('ZBA')
        feed_in = FeedIn(
            request = feed_req.id, 
            plate = inp.plate, 
            medium = medium.name
        )
        db.add(feed_in)
        
        # Image request
        image_req = self.new_request_days(db, "image", days=(3, 7))
        device = db.query(Device).first()
        image_in = ImageIn(
            request=image_req.id,
            plate=inp.plate,
            device=device.name
        )
        db.add(image_in)
    
    def create_plates(self, db, format: str, vendor: str, plate_nos: List[int]):
        db.rollback()
        with db.begin():
            for p in plate_nos:
                plate = Plate(
                    name   = f"Plate{p:04d}",
                    format = format,
                    type   = "culture",
                    vendor = vendor
                )
                db.add(plate)
        return plate
        
    class PlateBatchIn():
        start: int
        end: int
        format: PlateFormat
        vendor: Vendor
        
    class PbIn(rep(PlateBatchIn).form_parser()): pass
        
    async def plates(self, ht: Context = Depends(context)):
        pbr = rep(SeedStep.PlateBatchIn)
        vendors = ht.db.query(Vendor).filter(Vendor.type=='plate').all()
        pbr.options('vendor', vendors)
        pbr.default('vendor', None)
        ret = ht.page("Create a batch of plates for seeding",
            ht.h2("Create a batch of plates for seeding"),
            ht.h3("Enter the plate batch information:"),
            ht.rform(f"{self.path}/plates", pbr)
        )
        return ret.pretty()
    
    async def plates_post(self, inp: PbIn = Depends(), ht: Context = Depends(context)):
        log(f"New plate batch: {inp}")
        if (inp.end<inp.start):
            return ht.error("End plate number must be greater than start plate number.")
        if (inp.end - inp.start) > 10:
            return ht.error("Will not create more than 10 plates at a time.")
        self.create_plates(ht.db, inp.format, inp.vendor, list(range(inp.start, inp.end+1)))
        return RedirectResponse(url=f"/{self.path}/req/", status_code=302)        

    def links(self, ht: Context):
        return [
            ht.link(f"{self.path}/plates")(f"Create a batch of plates for seeding")
        ] + super().links(ht)
        
    def route(self):
        super().route()
        app.get(f"/{self.router_path}/plates/", response_class=HTMLResponse)(self.plates)
        app.post(f"/{self.router_path}/plates/", response_class=HTMLResponse)(self.plates_post)
    
# Extension to allow media changes    
@dataclass
class NewMediumResult(ResultSubmit):
    medium: Medium
    
#
#  Daily feeding of cells
#
class FeedStep(FlowStep):
    def __init__(self):
        self.description = "Feed culture plates"
        self.multiple = set(["plate"])
        self.filters = { "plate": Plate.type == "culture" }
        self.ResultSubmit = NewMediumResult
        super().__init__("feed", FeedIn)
        
    async def next(self, db: Session, res: NewMediumResult, inp: FeedIn):
        # Next feeding
        req = self.new_request_days(db, "feed", days=(1, 4))
        feed_in = FeedIn(
            request = req.id, 
            plate = inp.plate,
            medium = res.medium.name
        )
        db.add(feed_in)
    
#
#  Weekly imaging of plates
#
class ImageStep(FlowStep):
    def __init__(self):
        self.description = "Image culture plates"
        self.filters = { "plate": Plate.type == "culture" }
        self.barcodes = { "enter" }
        super().__init__("image", ImageIn)
        self.req_rep.default("device", "olympus")
        
    async def next(self, db: Session, res: ResultSubmit, inp: ImageIn):
        # Request image upload
        req = self.new_request(db, "upload")
        upload_in = UploadIn(request = req.id, image_req = inp.request)
        db.add(upload_in)
        
        # Request next imaging
        req = self.new_request_days(db, "image", days=(7, 10))
        image_in = ImageIn(request = req.id, plate = inp.plate, device = inp.device)
        db.add(image_in)

# Extension of results to capture newly created image sets
@dataclass
class NewImageSetResult(ResultSubmit):
    image_set: ImageSet

#
#  Uploading of image data
#
class UploadStep(FlowStep):
    def __init__(self, **kwargs):
        self.description = "Upload image data"
        self.filters = { "image_req": Request.step == "image" }
        self.ResultSubmit = NewImageSetResult
        super().__init__("upload", UploadIn, **kwargs)
        self.bot = True
        
    async def next(self, db: Session, res: NewImageSetResult, inp: UploadIn):
        
        
        
        ims_query = db.execute(
            text(
                (
                    "SELECT distinct(image_channel.channel_name) as channel "
                    + " FROM image_channel JOIN image ON image_channel.image_id"
                    + " = image.id WHERE image.image_set = :ims"
                )
            ),
            {"ims":res.image_set.id}
        )
        image_set_channels = set()
        for row in ims_query:
            image_set_channels.add(row[0])
            
        # select most recent version of pipeline where 
        # pipeline device and channel requirements match
        # that of the image set
        
        if res.image_set.device == 'cytosmart':
            pipeline_version_id_query = db.execute(
                text(
                    """
                    select
                      max(pipeline_version.id) as version_id
                    from
                      pipeline_version
                    join
                      image_pipeline on image_pipeline.id = pipeline_version.image_pipeline
                    where
                      image_pipeline.device = :device
                    and
                      image_pipeline.active = true
                    group by
                      image_pipeline.id
                    """
                ),
                {"device":res.image_set.device}
            )
            
        elif res.image_set.device == 'olympus':
            t = text(
                """
                select
                  max(id) as version_id
                from
                  pipeline_version
                where
                  image_pipeline in (
                    select id from (
                      select
                        id, count(*) as c
                      from 
                        image_pipeline
                      join
                        pipeline_channel on pipeline_channel.image_pipeline = image_pipeline.id
                      where
                        image_channel in (:channel_list)
                      and
                        device = :device
                      and
                        active = true
                      group by
                        id
                    ) as t
                    where t.c = :num_channels
                  )
                group by
                  image_pipeline
                """
            )
            t.bindparams(bindparam('channel_list', expanding=True))
            pipeline_version_id_query = db.execute(
                t,
                {
                    "channel_list": tuple(image_set_channels),
                    "device": res.image_set.device,
                    "num_channels": len(image_set_channels)
                }
            )
        
        pipeline_version_ids = [p.version_id for p in pipeline_version_id_query]
        
            
        for pipeline_version_id in pipeline_version_ids:
            req = self.new_request(db, "pipeline")
            pipeline_in = PipelineIn(request=req.id, image_set=res.image_set.id, pipeline_version=pipeline_version_id)
            db.add(pipeline_in)
        
        # The image set is already in the database
        if res.image_set.id is None:
            raise ValueError("Image set is not stored in database")
        log(f"Linking Image set {res.image_set.id} to request {inp.request}.") 
        uout = db.query(UploadOut).filter(UploadOut.request == inp.request).one_or_none()
        if not uout:
            db.add(UploadOut(request=inp.request, image_set=res.image_set.id))
        else:
            log(f"  already linked: {uout}")
       

class CultureFlow(Workflow):
    def __init__(self):
        super().__init__("Culture", SeedStep(), FeedStep(), ImageStep(), UploadStep())
                      
        
    


    
