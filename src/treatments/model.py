from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import Foreign<PERSON>ey, UniqueConstraint, func
from sqlalchemy.orm.collections import attribute_mapped_collection
from base.database import Base, UtcDateTime
from typing import List
from datetime import datetime

class LibraryManifest(Base):
    __tablename__ = "library_manifest"
    id: Mapped[int] = mapped_column(primary_key=True)
    vendor: Mapped[str] = mapped_column(ForeignKey("vendor.name"))
    file_uri: Mapped[str] = mapped_column(unique=True)
    plates: Mapped[List["LibraryPlate"]] = relationship("LibraryPlate")
    created: Mapped[datetime] = mapped_column(UtcDateTime, default=func.now, nullable=True)

class LibraryPlate(Base):
    __tablename__ = "library_plate"
    __table_args__ = (UniqueConstraint("library_manifest", "manifest_plate_number"),)
    id: Mapped[int] = mapped_column(primary_key=True)
    library_manifest: Mapped[int] = mapped_column(ForeignKey("library_manifest.id"))
    manifest_plate_number: Mapped[int]
    mapping = relationship(
        "LibraryMapping",
        collection_class=attribute_mapped_collection("well_position"),
        cascade="all, delete-orphan",
    )

class LibraryMapping(Base):
    __tablename__ = "library_mapping"
    id: Mapped[int] = mapped_column(primary_key=True)
    library_plate: Mapped[int] = mapped_column(ForeignKey("library_plate.id"))
    treatment: Mapped[int] = mapped_column(ForeignKey("treatment.id"))
    well_position: Mapped[int] = mapped_column(ForeignKey("well_pos.id"))

    def __init__(self, treatment, well_position):
        self.treatment = treatment
        self.well_position = well_position