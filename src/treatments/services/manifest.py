
from treatments.model import LibraryPlate, LibraryMapping, LibraryManifest
from plates.model import Vendor, TreatmentType, Treatment, PlateFormat, WellPos
from typing import List, Dict, Any

from sqlalchemy.orm import Session
from uuid import uuid4
from app.layouts import Context

def create_new_manifest(vendor: str, file_uri: str, treatment_type: str, manifest_data: Dict[int, Dict[str, str]], context: Context):
    manifest = LibraryManifest()
    manifest.file_uri = file_uri
    manifest.vendor = vendor

    for plate_number, plate_map in manifest_data.items():

        plate = LibraryPlate()
        plate.manifest_plate_number = plate_number
        manifest.plates.append(plate)


        if len(plate_map) < 96:
            plate_format = '96-well'
        else:
            plate_format = '384-well'

        well_positions = context.db.query(WellPos).filter_by(plate_format=plate_format).all()

        for well_name, treatment_name in plate_map.items():
            treatment = Treatment()
            treatment.name = treatment_name
            treatment.type = treatment_type
            treatment.mcl_id = f"{vendor}_{plate_number}_{uuid4().hex[:5]}"
            context.db.add(treatment)
            context.db.flush()
            context.db.refresh(treatment)
            well = next(pos.id for pos in well_positions if pos.well_name == well_name)
            plate.mapping[well] = LibraryMapping(treatment.id, well)
            
    context.db.add(manifest)
    context.db.commit()