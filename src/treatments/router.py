from fastapi import APIRouter, Depends, Body
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.requests import Request
from app.layouts import Context
from app.auth import context
#from treatments.orm.model import Treatment
from plates.model import Plate, Treatment, TreatmentType, Vendor
from typing import Dict, Any
from pydantic import BaseModel
from app import html
from io import BytesIO
import pandas as pd
import string

from app.cruds import crud




ROOT_URL = "/treatments"

router = APIRouter(prefix=ROOT_URL)



crud_path, orm_model = crud('/cruds', Treatment, router)

@router.get("/")
def index_treatments(ht: Context = Depends(context)):
    return {"res":"OK"}

class TreatmentSearch(BaseModel):
    name: str

@router.post("/search")
async def treatment_name_search(req: Request, ht: Context = Depends(context)):
    d = await req.json()
    name = d['name']
    results = ht.db.query(Treatment).filter(Treatment.name.contains(name)).all()
    treatment_list = [{"id":t.id, "name":t.name} for t in results]
    return {"results": treatment_list}


@router.post("/plates")
async def plate_name_search(req: Request, ht: Context = Depends(context)):
    d = await req.json()
    name = d['name']
    results = ht.db.query(Plate).filter(Plate.name.contains(name)).all()
    plate_list = [{'id':p.id, 'name':p.name} for p in results]
    return {"results": plate_list}


from treatments.model import LibraryManifest

@router.get("/manifest", response_class=HTMLResponse)
def manifest_index(request: Request, ht: Context = Depends(context)):
    manifests = ht.db.query(LibraryManifest).all()
    return ht.page(
        "manifests",
        *(html.node("div")(f"{manifest.vendor} {manifest.created}") for manifest in manifests)
    ).pretty()

@router.get("/manifest/new", response_class=HTMLResponse)
async def manifest_form(request: Request, ht: Context = Depends(context)):
    treatment_types = ht.db.query(TreatmentType).all()
    vendors = ht.db.query(Vendor).all()
    return ht.page("manifest upload",
        html.node("div")(
            html.node("p")("upload spreadsheet with three columns: plate number, well location, and treatment.")
        ),
        html.node("form", method="post", enctype="multipart/form-data")(
            html.node("div")(
                html.node("label", **{"for":"manifest"})("manifest spreadsheet"),
                html.node("input", type="file", id="manifest", name="manifest")
            ),
            html.node("div")(
                html.node("label", **{"for":"treatment-type"})("treatment type"),
                html.node("select", id="treatment-type", name="treatment-type")(
                    *(html.node("option", value=t.name)(t.name) for t in treatment_types)
                )
            ),
            html.node("div")(
                html.node("label", **{"for":"vendor"})("vendor"),
                html.node("select", id="vendor", name="vendor")(
                    *(html.node("option", value=v.name)(v.name) for v in vendors)
                )
            ),
            html.node("div")(
                html.node("button")("Submit")
            )
        )
    ).pretty()

from treatments.services.manifest import create_new_manifest
from uuid import uuid4
from base.clients import storage
import pandas as pd
from io import BytesIO
from starlette import status

@router.post("/manifest/new")
async def manifest_creation(request: Request,  ht: Context = Depends(context)):
    async with request.form() as form:
        file_type = form["manifest"].content_type
        file_content = await form["manifest"].read()
        vendor = form["vendor"]
        treatment_type = form["treatment-type"]
    
    file_uri = f"library_manifests/{str(uuid4())}.csv"

    bucket = storage().bucket("mellicell_development_bucket")
    blob = bucket.blob(file_uri)
    blob.upload_from_string(file_content, content_type=file_type)
    df = pd.read_csv(BytesIO(file_content))
    manifest_data = {}
    for plate_number in df["plate_number"].unique():
        plate_map = {}
        for i, row in df.loc[df["plate_number"] == plate_number].iterrows():
            well_name = row["well_name"]
            well_name_corrected = well_name[0] + str(int(well_name[1:]))
            plate_map[well_name_corrected] = row["treatment"]
        manifest_data[plate_number] = plate_map
    create_new_manifest(vendor=vendor, file_uri=file_uri, treatment_type=treatment_type, manifest_data=manifest_data, context=ht)
    return RedirectResponse(f'{ROOT_URL}/manifest/', status_code=status.HTTP_302_FOUND)
