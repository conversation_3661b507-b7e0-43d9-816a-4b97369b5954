from datetime import datetime
from typing import Callable
from fastapi import APIRouter, Request,  Depends
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from sqlalchemy import text
import pandas as pd
import os

from app.html import Html
from base.util import make_logger
from base.io import cache_store
from base.reps import Rep, rep, JointRep
from app.table import reptable

from images.model import Device, ImageSet, ImagePipeline, PipelineGoal, Channels, PipelineVersion, PipelineRequest, Image
from images.match import ImageMatcher, ImageAction, ImageSetRow
from images.repo import Repo
from images.differentiation import Differentiation
from plates.model import Plate, WellPos
from plates.render import plate_map
from workflow.orm.steps import SeedIn
from app.cruds import crud
from app.layouts import Context
from app.auth import context
from app.store import cache_server

log = make_logger(__name__)

router = APIRouter(prefix="/imaging")

img_cache = cache_store()

# Some reps
plate_rep = rep(Plate)
iact_sel = {
    "plate": "plate.name",
    "day": "day",
    "action": "action.id",
    "operator": "action.operator",
    "station": "action.station",
    "completed": "result.ended",
    "result": "result.result",
    "comment": "result.comment",
}
iact_sel_all = iact_sel | {
    "upload_comment": "upload.comment"
}
iset_rep = rep(ImageSet) - "bt_key"
iact_rep = JointRep.from_class(ImageAction)
iact_rep_all = iact_rep(iact_sel_all)
iact_rep_sel = iact_rep(iact_sel)
repo_rep = rep(ImageSetRow)

# Some cruds
cruds = [
    crud("devices", Device, router),
    crud("imagesets", ImageSet, router),
    #crud("pipeline", ImagePipeline, router),
    #crud("pipelinegoals", PipelineGoal, router),
]

def ims_repo(ims: ImageSet):
    name = ims.device
    return Repo(name)

@router.get("/plates", response_class=HTMLResponse)
async def plate_list(ht: Context = Depends(context)):
    # subq = ht.db.query(SeedIn).filter(SeedIn.plate == Plate.id).exists()
    subq = ht.db.query(ImageSet).filter(ImageSet.plate_id == Plate.id).exists()
    plates = ht.db.query(Plate).filter(subq).all()
    ret = ht.page("Imaging Home",
        ht.h2("Image processing reports"),
        ht.h3("Pick one or more plates from this list of plates:"),
        reptable(plate_rep, plates)(id="plates", href=ht.url("imaging/plate")),
    )
    return ret.pretty()

@router.get("/plate/{plate_id}", response_class=HTMLResponse)
async def plate_isets(plate_id: int, ht: Context = Depends(context)):
    log(f"Selected plate: {plate_id}")
    plate = ht.db.get(Plate, plate_id)
    matcher = ImageMatcher(ht.db, plate, repos=[Repo("cytosmart"), Repo("olympus")])
    not_uploaded = [ia for ia in matcher.actions if ia.upload is None]
    
    def ims_list(id: str, rep: Rep, phrase: str, imss):
        if len(imss) >0:
            return ht.h3(f"Image sets {phrase}:") + \
            reptable(rep, imss)(id=id, href=ht.url("imaging/iset"))
        else:
            return None

    diff_form = ht.div(
        ht.h3("Run Differentiation Analysis:"),
        ht.form(action=f"/imaging/plate/{plate_id}/differentiation", method="get")(
            ht.label(for_="day")("Culture Day: "),
            ht.input(type="number", id="day", name="day", min="1", max="60", required="required")(),
            " ",
            ht.input(type="submit", value="Analyze")()
        )
    )

    ret = ht.page(f"Plate {plate.name}",
        ht.h2(f"Imaging summary for {plate.name}"),
        diff_form,
        ims_list("isets", iset_rep, "linked to the plate", matcher.imss),
        # ht.h3("Image sets linked to the plate:"),
        #     reptable(iset_rep, matcher.imss)(id="isets", href=ht.url("imaging/iset")),
        ims_list("iacts", iact_rep_sel, "that are not yet uploaded", not_uploaded),
        # ht.h3("Image sets that are not yet uploaded:"),
        #     reptable(iact_rep_sel, not_uploaded)(id="iacts"),
        ims_list("untracked", iset_rep, "that have not been tracked in the workflow", matcher.untracked),
        ht.br(), 
        ht.h2("Further details:"), 
        ht.br(),
        ht.h3("All tracked imaging operations:"),
            reptable(iact_rep_all, matcher.actions)(id="iacts"),
        ht.h3(f"All image sets in the repositories ({len(matcher.repo_imss)}):"),
            reptable(repo_rep, matcher.repo_imss)(id="repo_isets"),
    )
    return ret.pretty()

@router.get("/plate/{plate_id}/differentiation", response_class=HTMLResponse)
async def plate_differentiation(plate_id: int, day: int, ht: Context = Depends(context)):
    plate = ht.db.get(Plate, plate_id)
    if not plate:
        return HTMLResponse("Plate not found", status_code=404)
    
    log(f"Running differentiation for plate {plate.name}, day {day}")
    
    try:
        # Get image set for the specified day
        repo = Repo("olympus")
        image_set = repo.image_set(plate.name, day)
        
        if not image_set:
            return ht.page(f"Differentiation Error",
                ht.h2(f"Error: No images found"),
                ht.p(f"No images found for {plate.name}, day {day}"),
                ht.a(href=f"/imaging/plate/{plate_id}")("Back to plate")
            ).pretty()
        
        # Process all wells
        results = []
        store = cache_store() / "differentiation" / plate.name / f"Day{day}"
        os.makedirs(str(store), exist_ok=True)
        
        # Process a subset of wells for demonstration (first 5)
        well_names = list(image_set.wells.keys())[:5]  # Limit to 5 wells for demo
        
        for well_name in well_names:
            try:
                diff = Differentiation(image_set, plate.name, well_name, day)
                result = diff.measure()
                result_dict = result.iloc[0].to_dict()
                result_dict['well'] = well_name
                results.append(result_dict)
                log(f"Processed well {well_name}")
            except Exception as e:
                log(f"Error processing well {well_name}: {e}")
                continue
        
        if not results:
            return ht.page(f"Differentiation Error",
                ht.h2(f"Error: Processing failed"),
                ht.p(f"Failed to process any wells for {plate.name}, day {day}"),
                ht.a(href=f"/imaging/plate/{plate_id}")("Back to plate")
            ).pretty()
        
        # Create results table
        df = pd.DataFrame(results)
        
        # Create HTML table from DataFrame
        table_html = ht.table(
            ht.tr(*[ht.th(col)() for col in df.columns])(),
            *[ht.tr(*[ht.td(str(cell))() for cell in row])() for _, row in df.iterrows()]
        )
        
        # Create results page
        return ht.page(f"Differentiation Results - {plate.name} Day {day}",
            ht.h2(f"Differentiation Results for {plate.name}, Day {day}"),
            ht.p(f"Processed {len(results)} wells"),
            table_html,
            ht.br(),
            ht.a(href=f"/imaging/plate/{plate_id}")("Back to plate")
        ).pretty()
        
    except Exception as e:
        log(f"Error in differentiation: {e}")
        import traceback
        traceback.print_exc()
        return ht.page(f"Differentiation Error",
            ht.h2(f"Error during differentiation"),
            ht.p(f"An error occurred: {str(e)}"),
            ht.a(href=f"/imaging/plate/{plate_id}")("Back to plate")
        ).pretty()

@router.get("/iset/{iset_id}", response_class=HTMLResponse)
async def iset_images(iset_id: int, ht: Context = Depends(context)):
    ims = ht.db.get(ImageSet, iset_id)
    repo = ims_repo(ims)
    plate = ims.plate
    t = f"Image set {iset_id} for {plate.name} day {ims.day}. Device: {ims.device}, lighting: {ims.lighting}"
    log(t)
    plate = ims.plate
    log(f"Selected image set: {iset_id} -> {plate} {ims.day}")
    wells = ht.db.query(WellPos, Image).\
        filter(Image.image_set==ims.id).\
        filter(WellPos.id==Image.well_pos).\
        all()
    well_map = {wp.id: im for wp, im in wells}
    
    def well(wp: WellPos):
        """Function that creates the html for a single well."""
        link = False
        if wp.id in well_map:
            im = well_map[wp.id]
            t = f"{wp.well_name} {im.object_uri}"
            rwell = repo.get_well(ims.bt_key, wp.well_name)
            rim = rwell.default_image()
            v = cache_server.thumb(ht, rim.cfn, style="width: 90%;") or ""
#            v = ht.img(src=f"/store/cache/bin/{rim.cfn}-thumb.jpg", style="width: 90%;")()
#            v = ht.a(href=f"/imaging/image/{im.id}")(v)
            link = True
        else:
            t = f"{wp.well_name}, image not cached"
            v = ""
        # Create and return the html node for the well
        n = ht.div(v)
        if link:
            n = ht.a(href=f"/imaging/image/{im.id}")(n)
        return n(title=t)

    ret = ht.page(t, ht.h3(t),
        ht.p("Click on a well to see the image:"),
        plate_map(ht, plate.format, well),
        ht.link(f"qc/iset/{iset_id}")("Total lipid size data"),
    )
    return ret.pretty()


@router.get("/image/{img_id}", response_class=HTMLResponse)
async def view_image(img_id: int, ht: Context = Depends(context)):
    log(f"Selected image: {img_id}, user: {ht.user.name}")
    img = ht.db.get(Image, img_id)
    ims = ht.db.get(ImageSet, img.image_set)
    plate = ims.plate
    wp = ht.db.get(WellPos, img.well_pos)
    repo = ims_repo(ims)
    well = repo.get_well(ims.bt_key, wp.well_name)
    t = f"Image {img_id} for {plate.name} day {ims.day} well {wp.well_name}"
    log(t)
    ihs = []
    
    for k, im in well.images.items():
        im.encode(img_cache)
        ihs.append(
            ht.div(
                ht.when(ht.admin)(
                    ht.p(f"Image {k}, URI: {img.object_uri}"),
                    ht.p(f"Image URL: /imaging/image/{repo.name}/{im.fn}"),
                    ht.p(f"JPG URL: /img/{im.cfn}.jpg"),
                    ht.a(href=f"{im.signed_url()}")("See the image, from cloud"),
                    ht.a(href=f"/store/cache/bin/{im.cfn}.jpg")("See the image, streamed"),
                ),
                ht.img(src=f"/store/cache/bin/{im.cfn}.jpg", style="width: 100%;")(),
                #ht.img(src=f"/static/cache/{im.cfn}.jpg", style="width: 100%;")(),
            )
        )
    log(f"Found {len(ihs)} images")
    return ht.page(t, ht.h3(t), *ihs).pretty()

@router.get("/image/{repo_name}/{fn:path}", name="path-convertor", response_class=StreamingResponse)
async def serve_image(repo_name: str, fn: str, ht: Context = Depends(context)):
    """Serve an image file from a repository."""
    log(f"Streaming {fn} from {repo_name}")
    repo = Repo(repo_name)
    fobj = repo.storage.open(fn, "rb")
    def stream():
        with fobj as f:
            while chunk := f.read(1048): yield chunk
    return StreamingResponse(stream(), 
        media_type="application/octet-stream", 
        #headers={'Content-Disposition': 'attachment; filename=assay_'+fn.replace("/", "_")}
    )

