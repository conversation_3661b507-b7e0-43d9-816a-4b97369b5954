import json
import os
from pathlib import Path
from typing import NamedTup<PERSON>, <PERSON><PERSON>, List
import re
import shutil
from datetime import datetime
from time import sleep
import imageio
import javabridge as jb
import bioformats as bf
from imageio.v3 import imread, imwrite
from base.io import FileStore
from base.util import make_logger
log = make_logger(__name__)
#
# Parse a VSI file
#   This requires a Java VM to be running, with JAVA_HOME environment variable set.
#   For example on a Mac:
#       brew install openjdk
#       export JAVA_HOME=/opt/homebrew/Cellar/openjdk/23.0.2
#       export JAVA_HOME=/opt/homebrew/opt/openjdk/libexec/openjdk.jdk/Contents/Home
#
class java_vm:
    def __enter__(self):
        jb.start_vm(class_path=bf.JARS)
        init_logger()
        return self
    def __exit__(self, exc_type, exc_value, traceback):
        jb.kill_vm()
        return False

class VsiInfo(NamedTuple):
    series: int
    channels: List[str]
    width: int
    height: int
    resolution: Tuple[float,float]
    units: str
    created: datetime
    
    def __repr__(self):
        res = "None"
        if self.resolution is not None:
            res = f"{self.resolution[0]:.4f}x{self.resolution[0]:.4f} {self.units}" 
        return f"VsiInfo({self.width}x{self.height}, {res} {self.units}, {self.channels} {self.created})"

class VsiImage:
    def __init__(self, store, fn, info_only: bool = False):
        res_pat = re.compile(r'\(\s*([\d.]+)\s*,\s*([\d.]+)\s*\)')
        
        if isinstance(store, FileStore):
            # Shortcut if we are already on the local file system
            file = store._path(fn)
            temp = None
        else:
            # Copy the files to the local file system
            bn = os.path.basename(fn)
            if not bn.endswith(".vsi"):
                raise ValueError(f"Expected a VSI file, not '{fn}'")
            supp = "_"+bn[:-4]+"_"
            dir = os.path.join(os.path.dirname(fn), supp)
            temp = f".temp.{os.getpid()}"
            store.cp_local(fn, temp)
            # fake=info_only doesn't work....
            store.cp_local(dir, os.path.join(temp, supp))   
            file = os.path.join(temp, bn)

        self.store = store
        self.fn = fn
        
        try:
            # Open the VSI file
            reader = bf.ImageReader(file)
            ns = reader.rdr.getSeriesCount()
            info = []
            metas = []
            for level in range(ns):
                reader.rdr.setSeries(level)
                meta = jb.jutil.jdictionary_to_string_dictionary(reader.rdr.getSeriesMetadata())
                
                cal = meta.get('Calibration')
                if cal is not None:
                    m = res_pat.match(cal)
                    cal = (float(m.group(1)), float(m.group(2)))
                    
                created = meta.get('Creation time (UTC)')
                if created is not None:
                    created = datetime.fromisoformat(created+"-00:00")  # -00:00 is for UTC
                    
                channels = []
                nc = reader.rdr.getImageCount()
                try:
                    if nc == 1:
                        channels = [meta[f'Channel name']]
                    else:
                        channels = []
                        for i in range(nc):
                            channels.append(meta.get(f'Channel name #{i+1}'))
                except KeyError as x:
                    channels = None
                    
                inf = VsiInfo(
                    series=level,
                    channels=channels,
                    width=reader.rdr.getSizeX(),
                    height=reader.rdr.getSizeY(),
                    resolution=cal,
                    units=meta.get('Calibration units'),
                    created=created
                )
                #log(f"  Info: {inf}")
                keys = [k for k in meta.keys() if '#' not in k]
                metas.append(keys)
                if inf.resolution is not None:
                    info.append(inf)
            if len(info) < 1:
                raise ValueError(f"No series found in {file}")
            elif len(info) == 1:
                log(info[0])
            else:
                log(f"Multiple series found in {file}: {len(info)}")
                for i, inf in enumerate(info):
                    log(f"  Series {i}: {inf}")
                log.warning(f"Using first series: {info[0]}")
            self.meta = metas[info[0].series]
            self.info = info[0]
            if not info_only:
                self.data = reader.read(series=0, rescale=False).astype('uint16')
                log(f"Data: {self.data.shape} {self.data.dtype} {reader.rdr.getPixelType()}")  # 3 is UINT16
        finally:
            if temp is not None:
                shutil.rmtree(temp)

    def test_meta(self, name):
        def json_serial(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Type {type(obj)} not serializable")    
        fn = f"imgs/{name}-meta.tif"
        meta = json.loads(json.dumps(self.info._asdict(), default=json_serial))
        log(f"Writing {fn} with metadata: {meta}")
        imwrite(fn, self.data, compression="jpeg2000", metadata=meta)
        data = imread(fn)
        log(f"Read {fn} -> {type(data)} {data.shape} {data.dtype} meta: {hasattr(data, 'meta')}")
        if hasattr(data, 'meta'):
            log(f"  Metadata: {data.meta}")
            assert data.meta == meta
        assert (data == self.data).all()

        
    def test_format(self, name, format):
        
        def test_one(ch, data, **kwargs):
            log(f"  Testing {format} format on {self.file}:{ch} {data.shape}{kwargs}")
            tag = kwargs.get("compression") or kwargs.get("compress_level")
            tag = f"-{tag}" if tag is not None else ""
            fn = f"imgs/{name}-{ch}{tag}.{format}"
            imwrite(fn, data, **kwargs)
            data2 = imread(fn)
            assert (data2 == data).all()
        
        def test(ch, data):
            test_one(ch, data)
            if format == 'tiff':
                kwargss = [{"compression": a} for a in ["lzw", "jpeg2000", "deflate", "webp"]] 
                # 'lzw' needs imagecodecs
                # 'packbits' has no encoder?
            elif format == 'png':
                kwargss = [{'compress_level': a} for a in [0, 3, 6, 9]]
            for kwargs in kwargss:
                test_one(ch, data, **kwargs)
            
        """Test writing and reading lossless 16 bit PNG files"""
        if len(self.data.shape) == 2 or format == 'tiff':
            log(f"Testing {format} format on {self.data.shape}")
            test(f"all{len(self.info.channels)}", self.data)
        elif len(self.data.shape) == 3:
            # We apparently can't write 16 bit PNG files with 3 channels
            log(f"Testing {format} format on {self.data.shape}, {len(self.info.channels)} channels")
            for i, ch in enumerate(self.info.channels):
                test(ch, self.data[:,:,i])

def init_logger():
    """This is so that Javabridge doesn't spill out a lot of DEBUG messages
    during runtime.
    From CellProfiler/python-bioformats.
    """
    rootLoggerName = jb.get_static_field(
        "org/slf4j/Logger", "ROOT_LOGGER_NAME", "Ljava/lang/String;"
    )
    rootLogger = jb.static_call(
        "org/slf4j/LoggerFactory", "getLogger", "(Ljava/lang/String;)Lorg/slf4j/Logger;", rootLoggerName
    )
    logLevel = jb.get_static_field(
        "ch/qos/logback/classic/Level", "WARN", "Lch/qos/logback/classic/Level;"
    )
    jb.call(rootLogger,
            "setLevel", "(Lch/qos/logback/classic/Level;)V", logLevel
    )
        
def cleanup_temp_files():
    """Clean up any temporary directories created by VsiImage"""
    import glob
    import shutil
    import os
    
    # Find all .temp.* directories in the current working directory
    temp_dirs = glob.glob(".temp.*")
    for temp_dir in temp_dirs:
        try:
            if os.path.isdir(temp_dir):
                shutil.rmtree(temp_dir)
                print(f"Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"Error cleaning up {temp_dir}: {e}")