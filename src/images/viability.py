import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import scipy
import skimage
import scipy.ndimage as ndi
from imageio.v2 import imread, imwrite
from base.io import Store, cache_store, File, cached, make
from PIL import Image as PilImage, ImageDraw

from images.model import Image, ImageSet
from images.pipeline import Pipeline
from base.util import lazy, make_logger
from images.repo import Repo
from images.analysis import Analysis, Channel

log = make_logger(__name__)
repo = Repo("olympus-tiff")

class Viability(Analysis):
    def __init__(self, pipeline: Pipeline, image_set: ImageSet, well: str):
        super().__init__("viability", pipeline, image_set, well)
    
    # Define the three input channels
    @cached("dapi.tif", Channel)
    def dapi(self):
        return self.load("DAPI")
    
    @cached("fitc.tif", Channel)
    def fitc(self):
        return self.load("FITC")
    
    @cached("tritc.tif", Channel)
    def tritc(self):
        return self.load("TRITC")
    
    # Channels in RGB order
    def channels(self):
        return {"FITC": self.fitc, "TRITC": self.tritc, "DAPI": self.dapi}
    
    # Combine the three channels into a color image
    @cached("color.tif")
    def color(self):
        img = np.stack(self.channels().values(), axis=-1)
        return img
    
    @lazy
    def foreground(self):
        # sum = np.zeros(self.dapi.img.shape, dtype=np.float32)
        # for ch in self.channels().values():
        #     blurr = ch.blur(ch.img, 10)
        #     sum = np.maximum(sum, blurr)
        return self.dapi.corrected
    
    @cached("mask.tif")
    def mask(self):
        img = self.foreground
        
        log(f"Masking {self} {img.shape}")
        thresh = skimage.filters.threshold_otsu(img)
        log(f"Threshold: {thresh}")
        bmask = img > thresh
        log(f"Binary: {np.unique(bmask, return_counts=True)}")
        self.plot(bmask, "mask-view.png")
        return bmask.astype(np.uint8) * 255
    
    @lazy
    def blobs(self):
        # find blobs
        ch = self.fitc
        img = ch.corrected
        #img = ch.blur(img, 20)
        thresh = 0.3*skimage.filters.threshold_otsu(img)/ch.maxv
        log(f"Finding blobs, threshold = {thresh:.5f}")
        blobs = skimage.feature.blob_log(img, min_sigma=10, max_sigma=100, num_sigma=10, threshold=thresh)
        log(f"Blobs: {blobs.shape}")
        df = pd.DataFrame(blobs, columns=["x", "y", "r"])
        return df.astype(int)
    
    @cached("cells.tsv")
    def cells(self):
        self.mask
        # distance = ndi.distance_transform_edt(self.mask)
        # self.plot(distance, "avg-distance.png")
        # local_max_coords = skimage.feature.peak_local_max(distance, min_distance=7)
        thresh = skimage.filters.threshold_otsu(self.foreground)
        local_max_coords = skimage.feature.peak_local_max(self.foreground, min_distance=10, threshold_abs=thresh)
        log(f"Local Maxima: {local_max_coords.shape}")
        
        # compute pairwise distances
        dists = scipy.spatial.distance.pdist(local_max_coords)
        self.histogram(dists, "dists-hist.png", range=(0, 500))
        
        # compute y distribution
        self.histogram(local_max_coords[:,1], "y-hist.png")
        
        # compute channel averages for each cell
        w = 15
        dead = []
        live = []
        for x, y in local_max_coords:
            dead.append(self.tritc.img[x-w:x+w, y-w:y+w].mean())
            live.append(self.fitc.img[x-w:x+w, y-w:y+w].mean())
        df = pd.DataFrame(local_max_coords, columns=["x", "y"]).join(pd.DataFrame({"dead": dead, "live": live}))
        log(f"Cells: {df.shape}\n{df}")
        
        # create a scatter plot of the cells
        plt.figure(figsize=(10, 6))
        plt.scatter(df.dead, df.live)
        plt.xlabel("Dead (tritc)")
        plt.ylabel("Live (fitc)")
        plt.savefig(self.store.open("scatter.png", "wb"), format="png")
        return df
    
    def annotate(self, img):
        pim = PilImage.fromarray((img/256).astype(np.uint8), mode="RGB")
        draw = ImageDraw.Draw(pim)
        # for x, y, dead, live in self.cells.itertuples(index=False):
        #     draw.ellipse((y-20, x-20, y+20, x+20), outline ='yellow')
        #     pim.putpixel((y, x), (255, 255, 255))
        for x, y, r in self.blobs.itertuples(index=False):
            draw.ellipse((y-r, x-r, y+r, x+r), outline ='green')
            pim.putpixel((y, x), (255, 255, 255))
        out = np.array(pim)
        return out
    
    @cached("view.tif")
    def view(self):
        img = np.stack([self.tritc.img, self.fitc.img, self.dapi.img], axis=-1)
        log(f"View: {img.shape} {img.dtype} ")
        img = self.annotate(img)
        imgs = np.stack([self.tritc.scaled, self.fitc.scaled, self.dapi.scaled], axis=-1)
        imgs = self.annotate(imgs)
        imwrite(self.store.open("view-scaled.tif", "wb"), imgs, format="tiff")
        for chn, ch in self.channels().items():
            self.plot(ch.img, f"{chn.lower()}-view.png")
            self.plot(ch.background, f"{chn.lower()}-bg-view.png")
            self.plot(ch.corrected, f"{chn.lower()}-corr-view.png")
        print(self.blobs)
        return img

    @staticmethod
    def compute(cls, pipeline) -> pd.DataFrame:
        log(f"Computing {len(pipeline.wells)}")
        for w in pipeline.wells:
            if w.well != "D2":
                continue
            log(f"\nWELL: {w.well} {w}")
            try:
                w.view
            except ValueError as x:
                log(f"ERROR: {x}")
        # w = pipeline.wells[5]
        # log(f"  {w}")
        # w.view


def plot_image(img, fd):
    # scale image to 0-255
    #log(f"Image type: {img.dtype}")
    if img.dtype == bool:
        img = img.astype(np.uint8) * 255
    else:
        img = (255.0 / img.max() * (img - img.min())).astype(np.uint8)
    plt.figure(figsize=(10, 6))
    plt.imshow(img, cmap='gray')
    plt.axis('off')
    plt.savefig(fd, format="png")
    plt.close()
    
def watershed_nuclei(binary_arr):
    distance = ndi.distance_transform_edt(binary_arr)

    local_max_coords = skimage.feature.peak_local_max(distance, min_distance=7)
    log(f"Local Maxima: {local_max_coords.shape}")
    local_max_mask = np.zeros(distance.shape, dtype=bool)
    local_max_mask[tuple(local_max_coords.T)] = True
    markers = skimage.measure.label(local_max_mask)

    segmented_cells = skimage.segmentation.watershed(-distance, markers, mask=binary_arr)
    log(f"Unique: {np.unique(segmented_cells, return_counts=True)}")

    return segmented_cells

def segment_nuclei(dapi_arr):
    blurr = skimage.filters.gaussian(dapi_arr, sigma=1)
    thresh = skimage.filters.threshold_otsu(blurr)
    binary_arr = blurr > thresh    
    return watershed_nuclei(binary_arr)
