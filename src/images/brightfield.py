from dataclasses import dataclass
from typing import Optional
import numpy as np
import pandas as pd
from cellpose import models
from cellpose.io import imread
import skimage
from base.io import Store, cache_store, File, cached, make
from google.cloud import storage
import os
import json
import random
import string

from images.analysis import Analysis
from images.model import Image, ImageSet
from images.pipeline import Pipeline
from base.util import lazy, make_logger
from images.repo import Repo
from images.analysis import Analysis


log = make_logger(__name__)

class BrightfieldAnalysis(Analysis):
    """Base class for brightfield analysis"""
    def __init__(self, pipeline: Pipeline, image_set: ImageSet, well: str):
        super().__init__("brightfield", pipeline, image_set, well)
        self.project_id = "development-311316"
        self._image = None
        self._current_file = None
        self.model_bucket_name = "mellicell_ml_models"
        self.mask_bucket_name = "cytosmart_masks_mellicell"
        self.model_name = "MCL_CP_005"

    @lazy
    def load_image(self): # This is to be modified to use the superclass load() method
        """Load the brightfield image from repository or local file"""
        if self._image is not None:
            return self._image
            
        if self._current_file and os.path.exists(self._current_file):
            # Load from local file if available
            self._image = imread(self._current_file)
        else:
            # Load from repository
            rwell = self.repo.get_well(self.image_set.bt_key, self.well)
            if not rwell or not rwell.images:
                raise ValueError(f"No images found for {self.well} in {self.image_set.bt_key}")
            
            image_files = [im for im in rwell.images.values() if im.channel.lower() == "bf"]
            if not image_files:
                raise ValueError(f"No BF images found for {self.well}")
            
            rim = image_files[0]
            log(f"Loading image: {rim.fn}")
            
            # Download to a temporary file first
            temp_filename = f"temp_{os.path.basename(rim.fn)}"
            with open(temp_filename, 'wb') as temp_file:
                temp_file.write(self.repo.storage.open(rim.fn, "rb").read())
            
            # Now read from the temporary file
            self._image = imread(temp_filename)
            
            # Clean up the temporary file
            if os.path.exists(temp_filename):
                os.remove(temp_filename)
            
        return self._image
    
    @staticmethod
    def make_foreground(mask):
        """Create foreground mask for cluster analysis"""
        mask = mask.astype(bool)
        r0 = skimage.morphology.rectangle(20, 2)
        r90 = skimage.morphology.rectangle(2, 20)
        diamond = skimage.morphology.diamond(5)
        mask = skimage.morphology.binary_erosion(
            skimage.morphology.binary_dilation(
                skimage.morphology.binary_dilation(mask, r0),
                r90
            ),
            diamond
        )
        mask = skimage.measure.label(mask)
        return mask

    @staticmethod
    def q25(x):
        return x.quantile(0.25)

    @staticmethod
    def q50(x):
        return x.quantile(0.5)

    @staticmethod
    def q75(x):
        return x.quantile(0.75)

    @staticmethod
    def ratio_big_to_all(x):
        x = x.values
        if pd.isna(x.max()) or pd.isna(x.sum()):
            return None
        else:
            return float(x.max())/float(x.sum())

    @staticmethod
    def ratio_two_biggest(x):
        x = sorted(x.values, reverse=True)
        if len(x) > 1:
            return float(x[0])/float(x[1])
        else:
            return None
        
    @staticmethod
    def get_blob(bucket_name, object_name):
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(object_name)
        return blob

    def segment(self, image=None, storage_client=None):
        """Generate cell mask using Cellpose"""
        if image is None:
            image = self.load_image
        if storage_client is None:
            storage_client = storage.Client(project=self.project_id)
        # Ensure model is available
        if not os.path.exists(self.model_name):
            model_bucket = storage_client.bucket(self.model_bucket_name)
            model_object = model_bucket.blob(self.model_name)
            model_object.download_to_filename(self.model_name)

        model = models.CellposeModel(gpu=False, pretrained_model=self.model_name)
        mask, _, _ = model.eval(image)
        return mask

    def measure(self, mask, metadata, objectURI):
        """Calculate both individual and aggregated measurements"""

        pixel_size = 1000/metadata['pixelsPerMm'] # 1000um/Npix (gives um per pix)
        fg_mask = self.make_foreground(mask)

        # make measurment of cellpose masks
        cp_df = pd.DataFrame(
            skimage.measure.regionprops_table(
                mask,
                properties=(
                    'label',
                    'area',
                    'perimeter',
                    'centroid'
                )
            )
        )

        cp_df.columns = [c.replace('-', '_') for c in cp_df.columns]
        cp_df['circularity'] = (4*np.pi*cp_df['area'])/(cp_df['perimeter']**2)
        # get label from foreground mask for cluster analysis
        clusters = cp_df[['centroid_0', 'centroid_1']].apply(
            lambda x: int(fg_mask[int(x[0]), int(x[1])]),
            axis=1
        )
        if len(clusters) > 0:
            cp_df['cluster'] = clusters
        else:
            cp_df['cluster'] = None

        cp_df['image'] = objectURI

        agg_df = cp_df.groupby(['cluster', 'image']).agg(
            {'area': ['count', 'mean', 'std',
                    'min', self.q25, self.q50, self.q75,
                    'max', self.ratio_two_biggest, self.ratio_big_to_all],
            'circularity': ['mean', 'std', 'min', 'max']
            }
        )

        agg_df_area = agg_df['area']
        agg_df_circ = agg_df['circularity']
        agg_df_area.columns = [f"{cname}_area" for cname in agg_df_area.columns]
        agg_df_circ.columns = [f"{cname}_circ" for cname in agg_df_circ.columns]
        agg_df = pd.merge(
            agg_df_area,
            agg_df_circ,
            left_index=True,
            right_index=True
        )
        agg_df = agg_df.reset_index()
        agg_df['image'] = objectURI
        del (agg_df_area)
        del (agg_df_circ)

        cp_df['area'] = cp_df['area']*(pixel_size**2)
        cp_df['perimeter'] = cp_df['perimeter']*pixel_size

        agg_df["mean_area"] = agg_df["mean_area"]*(pixel_size**2)
        agg_df["min_area"] = agg_df["min_area"]*(pixel_size**2)
        agg_df["q25_area"] = agg_df["q25_area"]*(pixel_size**2)
        agg_df["q50_area"] = agg_df["q50_area"]*(pixel_size**2)
        agg_df["q75_area"] = agg_df["q75_area"]*(pixel_size**2)
        agg_df["max_area"] = agg_df["max_area"]*(pixel_size**2)

        agg_df['experiment_id'] = metadata['experimentId']
        agg_df['scan_number'] = metadata['scanNumber']
        agg_df['experiment_name'] = metadata['experimentName']
        agg_df['plate_num_wells'] = metadata['numberOfWells']
        agg_df['pixels_per_mm'] = metadata['pixelsPerMm']
        agg_df = agg_df.astype(
            {
                "cluster":int,
                "mean_circ":float,
                "std_circ":float,
                "min_circ":float,
                "max_circ":float,
                "count_area":int,
                "mean_area":float,
                "std_area":float,
                "min_area":float,
                "q25_area":float,
                "q50_area":float,
                "q75_area":float,
                "max_area":float,
                "ratio_two_biggest_area":float,
                "ratio_big_to_all_area":float,
                "image":str,
                'experiment_id': str,
                'scan_number': int,
                'experiment_name': str,
                'plate_num_wells': int,
                'pixels_per_mm': int
            }
        )

        cp_df = cp_df.astype(
            {
                "label":int,
                "area":float,
                "perimeter":float,
                "centroid_0":float,
                "centroid_1":float,
                "cluster":int,
                "image":str
            }
        )
        self.agg_df, self.cp_df = agg_df, cp_df
        return self.cp_df, self.agg_df

    def process(self, bucket_name, object_name):
        """Process an image using only the repository pattern"""
        # Use provided values or defaults from the instance
        parts = object_name.split('/')
        bt_key = '/'.join(parts[:-2])  # Everything before the well directory
        well = parts[-2]  # The well directory name
        
        # Set these values for the repository access
        self.image_set.bt_key = bt_key
        self.well = well
        storage_client = storage.Client(project=self.project_id)
        
        bucket = storage_client.bucket(bucket_name)
        object = bucket.blob(object_name)
        uuid = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
        fname = uuid + '_' + os.path.basename(object.name)
        mask_bucket = storage_client.bucket(self.mask_bucket_name)
        mask_object = mask_bucket.blob(object.name)

        image_details_blob_name = '/'.join(object.name.split('/')[:-2]) + "/scan_metadata.json"
        image_details_blob = bucket.blob(image_details_blob_name)
        
        image_details = json.loads(image_details_blob.download_as_string().decode('utf-8'))

        # Load image from repository
        image = self.load_image
        
        # Get metadata from repository
        rwell = self.repo.get_well(bt_key, well)
        if not rwell:
            raise ValueError(f"Well {well} not found in {bt_key}")
            
        # Get metadata (assuming it's available in the image set)
        metadata = self.image_set.metadata
        if not metadata:
            raise ValueError(f"Metadata not available for {bt_key}")
            
        objectURI = f"gs://{bucket_name}/{object_name}"
        
        mask = self.segment(image, storage_client)
        
        overlay = skimage.img_as_ubyte(skimage.color.label2rgb(mask, image))
        overlay_path = "overlay.jpg"
        skimage.io.imsave(overlay_path, overlay)
        
        if hasattr(self, 'storage_client') and self.storage_client:
            mask_bucket = self.storage_client.bucket(self.mask_bucket_name)
            mask_object = mask_bucket.blob(f"{bt_key}/{well}/{well}_export.jpg")
            mask_object.upload_from_filename(overlay_path)
            
        measurements = self.measure(mask, image_details, objectURI)
        return measurements