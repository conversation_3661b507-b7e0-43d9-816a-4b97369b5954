from images.repo import ImageSet, Repo
from imageio.v2 import imwrite
from base.io import cached, cache_store
from base.util import lazy
import numpy as np
import skimage
import skimage.filters
import skimage.measure
import skimage.segmentation
import skimage.morphology
from scipy import ndimage as ndi
import os
import imageio
import pandas as pd

class Channel:
    """Simplified Channel class that doesn't depend on Analysis"""
    def __init__(self, owner, name, img):
        self.owner = owner 
        self.name = name  
        self.img = img 
        if img.dtype == np.uint16:
            self.maxv = 2**16-1
        else:
            raise ValueError(f"Unsupported image dtype: {img.dtype}")
        self.store = owner.store / name
        
    def __repr__(self):
        return f"{self.name}({'x'.join([str(n) for n in self.img.shape])} {self.img.dtype})"
    
    # Methods needed to cache channels using the @cached decorator
    @classmethod
    def writer(cls, fd, ch):
        imwrite(fd, ch.img, format="tiff")
        
    @classmethod
    def reader(cls, fd, *, deco=None, owner=None):
        return Channel(owner, deco.func.__name__, imageio.v2.imread(fd, format="tiff"))

    mode = "b"

    # utility functions
    def blur(self, img: np.ndarray, sigma: float):
        imgf = img.astype(np.float32)
        blur = skimage.filters.gaussian(imgf, sigma=sigma, preserve_range=True)
        return np.minimum(blur, self.maxv).astype(self.img.dtype)

    @cached("bg.tif")
    def background(self):
        """Determine background"""
        background = skimage.restoration.rolling_ball(self.blur(self.img, 20), radius = 100)
        return np.minimum(background, self.img)
    
    @lazy
    def corrected(self):
        """Corrected image"""
        return self.img - self.background

    @lazy
    def scaled(self):
        imgf = self.img.astype(np.float32)
        blurr = skimage.filters.gaussian(imgf, sigma=10)
        thresh = skimage.filters.threshold_otsu(blurr)
        return np.minimum(self.maxv, imgf * (0.1*self.maxv) / thresh).astype(self.img.dtype)


class Differentiation:
    """Differentiation efficiency for Fluorescent images"""
    def __init__(self, image_set: ImageSet, plate_name: str, well: str, day: int):
        self.image_set = image_set
        self.plate_name = plate_name
        self.well = well
        self.repo = Repo("olympus")
        self.day = day
        self.lipid_channels = ["Cy5", "TRITC"]
        self.data = {}
        self.store = ( cache_store() / 
            "differentiation" /
            self.plate_name /
            f"Day{self.day}" / 
            well
        )
        # Create results directory in the cache
        self.results_dir = cache_store() / "differentiation" / self.plate_name / f"Day{day}" / "results"
        os.makedirs(str(self.results_dir), exist_ok=True)
    
    def load(self, itag):
        """Load an image channel from the cloud repository"""
        rwell = self.repo.find_well(self.plate_name, self.day, self.well)
        rim = rwell.img_for_tag("raw")
        channels = rim.info.channels
        if itag not in channels:
            # See if the info is correct
            rim.data()
            raise ValueError(f"Channel {itag} not found in {channels}")
        img = rim.data()
        for i, ch in enumerate(channels):
            self.data[ch] = img[..., i]
        img = self.data[itag]
        scaled = (255.0 / img.max() * (img - img.min())).astype(np.uint8)
        # self.plot(img, f"{itag.lower()}-view.png")
        imwrite(self.store.open(f"{itag.lower()}-scaled.png", "wb"), scaled, format="png")
        return Channel(self, itag.lower(), img)

    # Define the three input channels
    @cached("dapi.tif", Channel)
    def dapi(self):
        return self.load("DAPI")
    
    @cached("cy5.tif", Channel)
    def cy5(self):
        try:
            return self.load("Cy5")
        except:
            return None
    
    @cached("tritc.tif", Channel)
    def tritc(self):
        try:
            return self.load("TRITC")
        except:
            return None
    
    @property
    def lipid(self):
        """Get the available lipid channel (either Cy5 or TRITC)"""
        for channel_name in self.lipid_channels:
            try:
                if channel_name == "Cy5" and self.cy5:
                    return self.cy5
                elif channel_name == "TRITC" and self.tritc:
                    return self.tritc
            except:
                continue
        raise ValueError(f"No lipid channel found for {self.well}. Available channels: {self.lipid_channels}")
    
    @cached("nuclei_mask.tif")
    def segment_nuclei(self):
        """Segment nuclei from DAPI channel"""
        dapi_img = self.dapi.img
        
        # h, w = dapi_img.shape
        dapi_arr = dapi_img[int(dapi_img.shape[0] * 0.15):int(dapi_img.shape[0] * 0.85), int(dapi_img.shape[1] * 0.15):int(dapi_img.shape[1] * 0.85)].copy()
        filtered_image = skimage.filters.difference_of_gaussians(dapi_arr, 1, 12)
        normalized = filtered_image / np.max(filtered_image)
        
        blurr = skimage.filters.gaussian(normalized, sigma=1)
        thresholds = skimage.filters.threshold_multiotsu(blurr)
        regions = np.digitize(blurr, bins=thresholds)
        binary_arr = regions > 0
        distance = ndi.distance_transform_edt(binary_arr)
        
        local_max_coords = skimage.feature.peak_local_max(distance, min_distance=7)
        local_max_mask = np.zeros(distance.shape, dtype=bool)
        local_max_mask[tuple(local_max_coords.T)] = True
        markers = skimage.measure.label(local_max_mask)
        
        segmented_cells = skimage.segmentation.watershed(-distance, markers, mask=binary_arr)
        
        # Filter out odd shapes
        exclude = []
        for region in skimage.measure.regionprops(segmented_cells):
            label = region.label
            circularity = (4*np.pi*region.area)/(region.perimeter**2)
            if region.area < 50 or region.area > 3000 or circularity > 1.2 or circularity < 0.8:
                exclude.append(label)
        
        segmented_cells = np.where(np.isin(segmented_cells, exclude), 0, segmented_cells)
        return segmented_cells

    @cached("lipid_mask.tif")  # Comment out the cached decorator temporarily
    def lipid_foreground(self):
        """Segment lipid droplets"""
        lipid_img = self.lipid.img
        
        h, w = lipid_img.shape
        lipid_arr = lipid_img[int(h * 0.15):int(h * 0.85), int(w * 0.15):int(w * 0.85)].copy()
        
        blurr = skimage.filters.gaussian(lipid_arr, sigma=1)
        thresh = skimage.filters.threshold_otsu(blurr)
        binary_arr = blurr > thresh
        binary_arr = skimage.morphology.binary_dilation(binary_arr)
        
        mask_path = str(self.store / "lipid_mask.tif")
        os.makedirs(os.path.dirname(mask_path), exist_ok=True)
        imageio.v2.imsave(mask_path, binary_arr.astype(np.uint8) * 255)

        return imageio.v2.imread(mask_path)
    
    # @cached("results.csv")
    def measure(self):
        """Measure differentiation by counting nuclei with lipid staining"""
        nuclear_labels = self.segment_nuclei
        lipid_mask = self.lipid_foreground
        
        positive_count = 0
        negative_count = 0
        tally = []
        
        for prop in skimage.measure.regionprops(nuclear_labels):
            x, y = np.transpose(np.array([coord for coord in prop.coords]))
            #x, y = np.transpose(np.array([prop.centroid for prop in skimage.measure.regionprops(nuclear_labels)]))
            x = x.astype(np.uint16)
            y = y.astype(np.uint16)
            try:
                values = lipid_mask[x, y]
                if np.max(values) > 0:
                    tally.append(1)
                else:
                    tally.append(0)
            except IndexError:
                print("out if index")
                continue
        
        # Visualization
        h, w = lipid_mask.shape
        mask = np.zeros((h, w, 3), dtype=np.uint8)
        mask[:, :, 0] = lipid_mask.astype(np.uint8) * 255
        mask[:, :, 2] = (nuclear_labels > 0).astype(np.uint8) * 255

        results_path = f"results/{self.well}_day{self.day}.jpeg"
        with self.store.open(f"../{results_path}", "wb") as f:
            imageio.v2.imwrite(f, mask, format="jpeg")

        # result_image = str(self.results_dir / f"{self.well}_day{self.day}.jpeg")
        # os.makedirs(os.path.dirname(result_image), exist_ok=True)
        # imageio.v2.imsave(result_image, mask)

        values = np.array(tally)
        positive_count = np.sum(values > 0)
        negative_count = np.sum(values == 0)

        df = pd.DataFrame([{
            "well": self.well,
            "positive": positive_count,
            "negative": negative_count,
            "total": positive_count + negative_count,
            # "percent_positive": 100 * positive_count / (positive_count + negative_count) if (positive_count + negative_count) > 0 else 0
        }])

        # csv_path = str(self.store / "results.csv")
        # os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        # df.to_csv(csv_path)

        return df

    @property
    def result(self):
        """Get the differentiation result"""
        df = self.measure()
        return {
            "well": self.well,
            "positive": int(df["positive"].iloc[0]),
            "negative": int(df["negative"].iloc[0]),
            "total": int(df["total"].iloc[0]),
            "percent_positive": round(100 * df["positive"].iloc[0] / df["total"].iloc[0], 2) if df["total"].iloc[0] > 0 else 0
        }
    
