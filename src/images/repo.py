import traceback
from typing import <PERSON><PERSON><PERSON><PERSON>, Set, List
import os, io, re, json, numpy as np, pandas as pd
from datetime import datetime, timedelta, timezone
from pytz import UTC
from PIL import Image as PilImage
from PIL import ExifTags
from tqdm import tqdm

from base.appinfo import appinfo
from base.util import lazy, lform, oform, tform
from base.clients import bigquery, storage
from base.io import cache_store, GcsStore
import base.json
from base.util import make_logger
from images.parse import VsiImage
log = make_logger(__name__)

#
#  Access the image cloud repository
#
class ImageRepo:
    def __init__(self, name: str, project: str, bucket: str):
        self.name = name
        self.project = project
        self.bucket = bucket

    @lazy
    def image_sets(self):
        imss = get_image_sets(self)
        return imss
    
    @lazy
    def storage(self):
        return GcsStore(self.bucket, self.project)
    
    def find_well(self, plate_name: str, day: int, well_name: str):
        for ims in self.image_sets.image_sets.values():
            if ims.plate_name == plate_name:
                log(f"Image set: {ims.plate_name} {ims.day}")
            if ims.plate_name == plate_name and ims.day == day:
                return ims.wells.get(well_name)
        raise ValueError(f"Image set not found: {plate_name} {day}")

    def __repr__(self) -> str:
        return f"ImageRepo({self.name}, {self.project}, {self.bucket})"

    def plate(self, plate_name: str):
        """Return the image sets for a plate"""
        return  PlateImageSets(self.image_sets, [plate_name])
    
    def image_set(self, plate_name: str, day: int):
        """Return the image set for a plate and day"""
        pims = PlateImageSets(self.image_sets, [plate_name])
        for ims in pims.image_sets:
            if ims.day == day:
                return ims
        return None
    
    def get_well(self, bt_key: str, well_name: str):
        ims = self.image_sets.bt_key_map.get(bt_key)
        plate, key = bt_key.split("/")
        if ims is None:
            # Check older format
            alt = f"{plate}/{key[-2].replace('B','P')+'_'+key[:-3]}"
            ims = self.image_sets.bt_key_map.get(alt)
            if ims is None:
                ims = self.image_sets.bt_key_map.get(alt+"_01")
        if ims is None:
            pims = PlateImageSets(self.image_sets, [plate])
            log(f"Not found in {self.name}: {bt_key} -> {alt} -> {alt+'_01'}")
            for ims in pims.image_sets:
                log(f"  {ims.bt_key} {ims.bt_key==bt_key}")
            log(f"Present keys: {len(self.image_sets.image_sets)}")
            for key in self.image_sets.image_sets.keys():
                if plate[-3:] in key:
                    log(f"  {key} {key==bt_key}")
            raise ValueError(f"Image set not found in {self}: {bt_key}")
        well = ims.wells.get(well_name)
        if well is None:
            log(f"Well is missing: {well_name} in {ims.wells.keys()}")
        return well


class CytosmartRepo(ImageRepo):
    def __init__(self):
        super().__init__("cytosmart", "development", "cytosmart_brightfield_images_mellicell")

class OlympusRepo(ImageRepo):
    def __init__(self):
        super().__init__("olympus", "development", "mellitos-lab-data")

class OlympusTiffRepo(ImageRepo):
    def __init__(self):
        super().__init__("olympus-tiff", "production", "ix83-tiff-files")

class OlympusRawRepo(ImageRepo):
    def __init__(self):
        super().__init__("olympus-raw", "production", "ix83-raw-files")

all_repos = [CytosmartRepo(), OlympusRepo(), OlympusTiffRepo(), OlympusRawRepo()]

_repos = {}
def Repo(name: str):
    global _repos
    if name in _repos:
        return _repos[name]
    for r in all_repos:
        if r.name == name:
            _repos[name] = r
            return r
    raise ValueError(f"Unknown repo: {name}")

#
#   Parsing repo from GCS
#
plate_maps = {
    0: [f"{r}{c}" for r in 'ABCDEFGHIJKLMNOP' for c in range(1, 25)],
    6: [f"{r}{c}" for r in 'AB' for c in range(1, 4)],
    12: [f"{r}{c}" for r in 'ABC' for c in range(1, 5)],
    96: [f"{r}{c}" for r in 'ABCDEFGH' for c in range(1, 13)],
    384: [f"{r}{c}" for r in 'ABCDEFGHIJKLMNOP' for c in range(1, 25)]
}

class ImageInfo(NamedTuple):
    width: int
    height: int
    resx: float
    resy: float
    channels: List[str]
    created: datetime
    version: int = 1
    
    def __repr__(self):
        return f"{self.width}x{self.height}, {self.resx:.2f}x{self.resy:.2f}, [{','.join(self.channels)}], {self.created}"

class ImageFile:
    def __init__(self, well: "Well", ext: str, ch: str, fn: str, create_time: datetime):
        if '.'+ext not in fn:
            if not (ext == 'vsi' and fn.endswith('.ets') ):
                raise ValueError(f"Bad extension (expected ets): {fn}")
        self.well = well
        self.ext = ext
        self.channel = ch
        self.fn = fn
        self.create_time = create_time
        self.extras = []
    
    def __repr__(self):
        return f"ImageFile({self.well.name}, {self.ext}, {self.channel}, {self.fn})"
       
    @lazy
    def cfn(self):
        iset = self.well.image_set
        return f"{iset.repo.name}/{iset.bt_key}/{self.well.name}-{self.ext}-{self.channel}"            

    @lazy
    def info(self):
        """ Return the image info
              The image info is specified in the original image file. To avoid 
              repeated loading and parsing of the image files, we cache the info
              in the same storage bucket as the image files.
              There are two levels of caching: 
              1. Per image file, here in this routine
              2. Per image set, in the image set object
            Data is extracted and cached in the same storage bucket as the files
        """
        version = ImageInfo.__new__.__defaults__[0]
        caching = True
        # First see if we have an image set level cache dictionary to get the info from
        if caching and 'info' in self.well.image_set.__dict__:
            return self.well.image_set.info[self.well.name+"-"+self.ext+"-"+self.channel]
        # Then, see if we can read the info from the image file level cache json file
        repo = self.well.image_set.repo
        fni = self.fn.replace('.'+self.ext, '.json')
        if caching and repo.storage.exists(fni):
            log(f"Loading info for {self.well}-{self.ext}-{self.channel} from {fni}")
            with repo.storage.open(fni, "r") as fd:
                try:
                    info = base.json.decode(ImageInfo, fd.read())
                    if isinstance(info.created, str):
                        info = info._replace(created=datetime.fromisoformat(info.created))
                    if info.version != version:
                        log.warning(f"  Cache file is invalid: {info.version} != {version}")
                    elif info.resx == 0.0 or info.resy == 0.0:
                        log.warning(f"  Invalid resolution: {info.resx} {info.resy}")
                    else:
                        log(f"Info: {info}")
                        return info
                except Exception as e:
                    traceback.print_exception(type(e), e, e.__traceback__)
                    log.warning(f"  Error decoding: {e}, rewriting")
        # If we get here, we need to extract the info from the original image file
        t0 = datetime.now()
        log(f"Loading info for {self.well.name}-{self.ext}-{self.channel} from {repo.storage.tag}{self.fn}")
        # We deal with two types of image files: VSI and regular image files.
        if self.ext == 'vsi':            
            # Info comes from the file itself
            vf = VsiImage(repo.storage, self.fn, info_only=True)
            info = ImageInfo(
                width = vf.info.width, 
                height = vf.info.height, 
                resx = vf.info.resolution[0], 
                resy = vf.info.resolution[1],
                channels = vf.info.channels if vf.info.channels is not None else [],
                created = vf.info.created
            )
            if isinstance(info.created, str):
                raise ValueError(f"Bad date: {info.created}")
        else:
            stream = repo.storage.stream(self.fn)
            im = PilImage.open(stream)
            log(f"    {im.format}, {im.size}, {im.mode}")
            # exif = im.getexif()
            # gps_ifd = exif.get_ifd(ExifTags.IFD.GPSInfo)
            # #log(gps_ifd[ExifTags.GPS.GPSDateStamp])  # 1999:99:99 99:99:99 36867
            # log(f"INFO: {exif.get_ifd(36867)}")
            # log(gps_ifd[36867])  # 1999:99:99 99:99:99 36867
            #
            # Size comes from the image file, resolution is from the image set meta data
            x, y = im.size
            im.close()
            res = 0.0
            if hasattr(self.well.image_set, 'pixelsPerMm') and self.well.image_set.pixelsPerMm is not None:
                res = 1000.0 / self.well.image_set.pixelsPerMm
            else:
                log(f"No pixels per mm: {self.well.image_set} {hasattr(self.well.image_set, 'pixelsPerMm')}")
            info = ImageInfo(x, y, res, res, [self.channel], self.create_time)
        # Now, try to write the info to the image file level cache.
        try:
            js = base.json.encode(info)
            with repo.storage.open(fni, "w") as fd:
                fd.write(js)
            log(f"  {datetime.now()-t0}, saved to {fni}")
            log(f"    {js}")
        except Exception as e:
            # If we can't write for some reason, return info without caching.
            log(f"  {datetime.now()-t0}, error saving to {fni}: {type(e)} {e}")
        return info

    def data(self):
        if self.ext == 'vsi':
            vf = VsiImage(self.well.image_set.repo.storage, self.fn)
            return vf.data
        raise ValueError(f"Data fetch not implemented for {self.ext} files.")

    def encode(self, store, typ: str = "jpg", size: int = 1024):
        repo = self.well.image_set.repo
        fn = f"{self.cfn}.{typ}"
        #if store.exists(fn):
        #    store.delete(fn)
        if not store.exists(fn):
            stream = repo.storage.stream(self.fn)
            log(f"Open {repo.storage.tag}{self.fn} -> {stream}")
            im = PilImage.open(stream)
            log(f"Loaded {im.mode} image from {repo.storage.tag}{self.fn}")
            # Needed this kludge for some reason.
            if im.mode.startswith("I"):
                arr = np.array(im) // 256
                im = PilImage.fromarray(arr.astype("uint8"))
            im.thumbnail((size, size))
            t = 'jpeg' if typ == 'jpg' else typ
            with store.open(f"{self.cfn}.{typ}", "wb") as fd:
                im.save(fd, t)
            im.thumbnail((256, 256))   
            with store.open(f"{self.cfn}-thumb.{typ}", "wb") as fd:
                im.save(fd, t)
            log(f"Saved .{typ} in {store.tag}{fn}")
        else:
            log(f"{typ} exists: {store.tag}{fn}")
        log(f"  {repo.storage.size(self.fn)} -> {store.size(fn)}")
        return fn
    
    def signed_url(self):
        repo = self.well.image_set.repo
        return repo.storage.signed_url(self.fn)


class Well:
    def __init__(self, ims: "ImageSet", name: str):
        self.name = name
        self.image_set = ims
        self.images = {}
    
    def __repr__(self):
        return f"Well {self.name} [{self.ch_str()}]"
    
    def check(self):
        for k, im in self.images.items():
            if im.ext == 'vsi':
                if not im.fn.endswith('.vsi'):
                    return f"No VSI: {im}"
                if len(im.extras) == 0:
                    return f"No ETS: {im}"
        return None
            
    def ch_str(self):
        return ",".join(self.images.keys())
    
    def add_image(self, fn: str, extension: str, channel: str, create_time: datetime):
        if channel == 'bf':
            raise ValueError(f"Bad channel: {channel}")
        ext = extension
        if extension == 'ets':
            ext = 'vsi'
        key = ext+"-"+channel
        img = self.images.get(key)
        if img:
            if extension == 'ets':
                img.extras.append(fn)
                return
            if extension == 'vsi':
                if img.ext == 'vsi':
                    img.extras.append(img.fn)
                    img.fn = fn
                    return
            log(f"Now: {fn}")
            log(f"Was: {img.fn}")
            raise ValueError(f"Duplicate image in {self.image_set.id} {self.name}: {extension} {channel}")
        img = ImageFile(self, ext, channel, fn, create_time)
        self.images[key] = img
           
    def list_images(self):
        for k, im in self.images.items():
            log(f"  {k}: {im.info} {im.fn}")

    def default_image(self):
        for k, im in self.images.items():
            if k.startswith('jpg'): return im
        for k, im in self.images.items():
            if k.startswith('tif'): return im
        return next(iter(self.images.values()))
    
    def img_for_tag(self, ch):
        for k, im in self.images.items():
            if k.endswith("-"+ch): return im
        raise ValueError(f"Channel not found: {ch}")
        
    def img_tags(self):
        return list(set([k.split("-")[1] for k in self.images.keys()]))
    
    def channels(self):
        chs = set()
        for k, img in self.images.items():
            itag = k.split('-')[1]
            if itag == 'raw':
                if img.info.channels is not None:
                    for ch in img.info.channels:
                        chs.add(ch)
            else:
                chs.add(itag)
        return list(chs)
        
    
        
        
class ImageSet:
    def __init__(self, repo, group, id, experiment_id):
        self.repo = repo
        self.group = group
        self.id = id
        self.experiment_id = experiment_id
        self.bt_key = f"{self.experiment_id}/{self.id}"
        self.wells = {}
        self.meta = None

    def __repr__(self):
        if not hasattr(self, 'day'):
            return f"Iset {self.id}"
        return f"Iset {self.repo.name}:{self.plate_name}-{self.day} {self.id}"
    
    def add_well(self, wn: str):
        if wn not in self.wells:
            wi = Well(self, wn)
            self.wells[wn] = wi
        else:
            wi = self.wells[wn]
        return wi

    def close(self):
        # Add the meta data to the object
        if self.meta:
            self.__dict__.update(self.meta)
        else:
            # Guess at the plate format
            self.numberOfWells = 384
            for nw, ws in plate_maps.items():
                if nw>0:
                    ew = [w for w in self.wells if w not in ws]
                    #log(f"Plate {nw} {len(ew)} {ew}")
                    if len(ew) == 0 and self.numberOfWells > nw:
                        self.numberOfWells = nw
        all_wells = plate_maps[self.numberOfWells]

        # Check for bad wells and remove them
        bad = []
        msgs = set()
        for wn, w in self.wells.items():
            chk = w.check()
            if chk is not None:
                bad.append(wn)
                msgs.add(chk)
        for wn in bad:
            del self.wells[wn]
        if len(bad) > 0:
            log(f"Removed {len(bad)} bad wells: {','.join(msgs)}")

        # Check for missing or extra wells
        self.missing_wells = [well for well in all_wells if well not in self.wells]
        self.extra_wells = [well for well in self.wells.keys() if well not in all_wells]

        # Check some other conditions
        if self.meta:
            if self.experimentId != self.experiment_id:
                raise Exception(f"Experiment ID mismatch: {self.experimentId} != {self.experiment_id}")
            if str(self.scanNumber) != self.id:
                raise Exception(f"Scan Number mismatch: {oform(self.scanNumber)} != {oform(self.id)}")
        
        # Parse the plate name
        if not hasattr(self, 'plate_name') or not hasattr(self, 'day'):
            #pat = re.compile(r'(Plate|PD)?\D*(?P<plate>\d+)_Day(?P<day>\d+).*')
            pat = re.compile(r'(?P<pref>Plate|PD)?\D*(?P<plate>\d+)_Day(?P<day>\d+).*')
            m = pat.match(self.experimentName)
            if not m:
                #log("Meta: "+str(self.meta))
                log(f"No plate id: {self.experimentName} - {pat.pattern}")
                self.plate_name = None
                self.day = None
            else:
                #log(f"Plate: {self.experimentName} -> {m.group('pref')} {m.group('plate')} {m.group('day')}")
                pref = m.group('pref') if m.group('pref') else 'Plate'
                self.plate_name = pref + m.group('plate')
                self.day = int(m.group('day'))
            
        #log(f"BigTable: {conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_CLUSTER_TABLE}")
        
    @lazy
    def info(self):
        """Return the image info for all images in the image set
            This is the image set level cache of the image info. Info for all image files
            is collected and stored as a dataframe in the image set repository bucket.
            It is exposed as a dictionary of ImageInfo objects, indexed by the image file id.
        """
        version = ImageInfo.__new__.__defaults__[0]
        caching = True
        repo = self.repo
        # top level name by image set storage id (bt_key) and image info version
        fni = "info/"+self.bt_key.replace('/', '_') + f"-{version}.tsv"
        if caching and repo.storage.exists(fni):
            log(f"Loading info for {self} from {fni}")
            with repo.storage.open(fni, "r") as fd:
                try:
                    # read all info records for this image set
                    df = pd.read_csv(fd, sep='\t')
                    info = {}
                    # convert dataframe to dictionary of ImageInfo objects
                    for _, row in df.iterrows():
                        vals = {k:v for k,v in row.items() if k!="image"}
                        # check for invalid resolution to force recaching of recently 
                        # fixed resolution issues.
                        if vals['resx'] == 0.0 or vals['resy'] == 0.0:
                            raise ValueError(f"Invalid resolution: {vals['resx']} {vals['resy']}")
                        # parse the channels list
                        v = ''.join(vals['channels'])[1:-1].replace("'","").replace(" ","").split(',')
                        vals['channels'] = [c for c in v if c!=""]
                        # convert the created date string to datetime
                        vals['created'] = datetime.fromisoformat(vals['created'])
                        # All other items (numbers and strings) should be good as is
                        info[row['image']] = ImageInfo(**vals)
                    return info
                except Exception as e:
                    traceback.print_exception(type(e), e, e.__traceback__)
                    log.warning(f"  Error reading info cache: {e}, rewriting")
        log(f"Loading info for {self}")
        info = {w.name+"-"+k: img.info for w in self.wells.values() for k, img in w.images.items()}
        
        # Write the info to the image set level cache as a dataframe
        log(f"Storing info for {self} to {fni}")
        df = pd.DataFrame.from_dict(info, orient='index')
        df.index.name = 'image'
        df.reset_index(inplace=True)
        print(df)
        with repo.storage.open(fni, "w") as fd:
            df.to_csv(fd, sep='\t', index=False)
        return info

    def well_stats(self):
        return f"{len(set(self.wells))}/{self.numberOfWells} wells, "+\
            f"{len(self.missing_wells)} missing, {len(self.extra_wells)} extra"
    
    def channels(self):
        chs = set()
        for w in self.wells.values():
            # add to set
            chs.update(w.channels())
        return list(chs)
    
    def counts(self, table):
        log(f"Summarizing {table} for {self.experiment_id}/{self.id}")
        df = bigquery().query(f"""
            SELECT 
              count(*) as clusters, 
              count(distinct image) as good,
              avg(max_area) as csize
            FROM `{table}`
            WHERE image LIKE '%{self.experiment_id}/{self.id}%';"""
        ).result().to_dataframe()
        df['scan'] = self.id
        df['day'] = self.day
        df['wells'] = self.numberOfWells
        log(f"Counts: {df}")
        return df
    
    def query(self, table, cols: str = "*"):
        log(f"Querying {table} for {self.experiment_id}/{self.id}")
        df = bigquery().query(
            f"""SELECT {cols} FROM `{table}`
            WHERE image LIKE '%{self.experiment_id}/{self.id}%';"""
        ).result().to_dataframe()
        df['well'] = df['image'].apply(lambda x: re.search(r'(?P<well>[A-P]\d+)_export.jpg', x).group('well'))
        df = df.drop(columns=['image'])
        return df
    
    def cluster_df(self):
        return self.query(
          f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_CLUSTER_TABLE}",
          "image, cluster, count_area, mean_area, std_area, min_area, max_area"
        )
    
    def lipid_df(self):
        return self.query(f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_LIPID_TABLE}")
    
    def qcparms(self):
        df = self.lipid_df()
        by_well = df.groupby('well')
        by_cluster = df.groupby(['well', 'cluster'])

        df_well = by_well.agg({'area': ['count', 'mean', 'max'], 'circularity': ['mean', 'min']})
        df_well['scan'] = self.id
        # log(f"Wells: {df_well}")
        return df_well, {
            "scan": self.id,
            "wells": self.numberOfWells,
            "day": self.day,
            "good": len(by_well),
            "clusters": len(by_cluster),
            "csize": by_cluster.count()["area"].mean(),
        } 
        
    def size_params(self, excluded_wells: Set[int] = None):
        df0 = self.cluster_df()
        df = df0
        if excluded_wells:
            df = df0[~df0['well'].isin(excluded_wells)]
        return {
            "plate": self.plate_name,
            "day": self.day,
            "wells": df0.shape[0],
            "count": df.shape[0],
            "mean": np.mean(df['mean_area']),
            "max":  np.mean(df['max_area']),
            "mean-std": np.std(df['mean_area']),
            "max-std":  np.std(df['max_area'])
        }

class ImageSets:
    def __init__(self, repo: ImageRepo):
        self.repo = repo
        self.image_sets = dict()
        self.image_count = 0
        self.skipped = {}
        
        self.pat_dir = re.compile(r'MELLICELL-LS-01/(Plate|)(?P<group>PD|)(?P<plate>\d+)(_(?P<ptag>\d+))?/Day ?(?P<day>\d+)(_(?P<dtag>\d+))?/(Session(?P<ses>\d+)/)?[FP_].*')
        self.pat_fn = re.compile(r'.*/_?(?P<typ>[PF])_(Plate|)(?P<group>PD|)(?P<plate>\d+)(_(?P<ptag>\d+))?_Day(?P<day>\d+)(?P<tag0>_\d+)?_(Session(?P<ses>\d+)_)?(?P<well>([a-zA-Z]\d+|current))(?P<cur>_[^/]*|)?_(?P<tag2>\d+).*\.(?P<ext>vsi|ets)')


    @lazy
    def bt_key_map(self):
        return {ims.bt_key: ims for ims in self.image_sets.values()}
    
    def parse(self, name: str, created: str, metas: dict):
        # Parse the time stamp
        create_time = datetime.fromisoformat(created)
        if self.repo.name == 'cytosmart':
            return self.parse_cytosmart(name, create_time, metas)
        elif name.startswith('MELLICELL-LS-01'):
            return self.parse_olympus(name, create_time)
        elif name.startswith('windowsService'):
            return self.parse_olympus_raw(name, create_time)
        elif name.startswith('java/convert'):
            return self.parse_olympus_tiff(name, create_time)
        else:
            return None
            #raise ValueError(f"Unrecognized repo content: {self.repo.name} {name}")
            
    def skip(self, reason: str, name: str):
        if reason not in self.skipped:
            self.skipped[reason] = [name]
        else:
            self.skipped[reason].append(name)
            
    def parse_olympus(self, name: str, create_time: datetime):
        #log(f"Parsing {create_time} {name}")
        # Skip various fields for various reasons
        parts = name.split('/')
        skip = None
        if parts[1] == 'Grids': skip = "grids"
        elif parts[1] == 'logs': skip = "logs"
        elif parts[3] == '_temp': skip = "temp" 
        elif parts[2] == 'Exposures': skip = "exposures"
        elif name.endswith('.lnk'): skip = "link"
        elif name.endswith('.tif'): skip = "tiff"
        elif "BF_93" in parts[3]: skip = "BF_93"
        elif "Plate902" in name: skip = "Plate902"
        elif "PD003528" in name: skip = "PD003528"
        elif "Microscope_Test" in parts[3]: skip = "Microscope_Test"
        if skip:
            #log(f"Skipping {skip}: {name}")
            self.skip(skip, name)
            return None
        
        # Parse the name
        #   MELLICELL-LS-01/Plate0152/Day34/P_Plate0152_Day34_B10_01.vsi
        m_dir = self.pat_dir.match(name)
        m_fn = self.pat_fn.match(name)
        def debug():
            log(f"  Dir: {self.pat_dir}")
            log(f"   Fn: {self.pat_fn}")
            def show(m, pat):
                if m:
                    log(f"  Match: {m} <- {pat} <- {name}")
                    log(f"  Groups: {m.groups()}")
                else:
                    log(f"  No match: {pat}")
                    log(f"            {name}")
            show(m_dir, self.pat_dir)
            show(m_fn, self.pat_fn)
        if not m_dir or not m_fn:
            debug()
            raise ValueError(f"Bad name: {name}")
        
        # if m_fn.group('cur'):
        #     log(f"Current: {m_fn.group('cur')} <- {name}")
        #     exit(0)
        if m_fn.group('well')=='current' or m_fn.group('cur').startswith('_current'):
            #log(f"Skipping 'current' well: {name}")
            self.skip("current", name)
            return None
        
        session = m_dir.group('ses') or m_fn.group('ses')
        
        # Sanity check
        for n in ['group', 'plate', 'day', 'ses', 'ptag']:
            if m_dir.group(n) != m_fn.group(n):
                if n == 'day' and int(m_dir.group(n)) == int(m_fn.group(n)): continue
                if n == 'ses' and (m_fn.group(n) is None or m_dir.group(n) is None): continue
                log(f"Field mismatch: {name}")
                log(f"  Dir: {m_dir.groups()}")
                log(f"   Fn: {m_fn.groups()}")
                raise ValueError(f"Field {n} mismatch: {m_dir.group(n)} != {m_fn.group(n)}")
        
        # capture experimental plates that have a prefix
        group = 'main'
        pref = 'Plate'
        if m_fn.group('group'):
            if m_fn.group('group') != 'PD':
                raise ValueError(f"Bad group: {m_fn.group('group')}")
            group = 'PD'
            pref = 'PD'
        plate_name = pref + m_fn.group('plate')
            
        # capture experiment id
        parts = [
            m_fn.group('group'), m_fn.group('typ'), m_fn.group('plate'), m_fn.group('day'),
            m_fn.group('tag0'), m_dir.group('ptag'), m_dir.group('dtag'), 
            session, m_fn.group('cur'), m_fn.group('tag2')
        ]
        image_set_id = "_".join([p.replace("_","") for p in parts if p])
        if m_fn.group('plate') == "0212" and m_fn.group('day') == "13" and m_fn.group('well') == "D2":  
            log(f"  ISET: {image_set_id} <- {name}")
        if image_set_id == "PD_P_0035_3_01":
            log(f"ISET: {image_set_id} <- {name}")
        if "stack" in image_set_id:
            debug()
            raise ValueError(f"Bad image set id: {name} {image_set_id}")
        #log(f"Image set: {image_set_id}")
        
        lightings = {'P': 'BF', 'F': 'FL'}

        # retrieve or create image set 
        key = image_set_id
        if key not in self.image_sets:
            ims = ImageSet(self.repo, group, image_set_id, plate_name)
            # Fill in meta data, as it's not provided in the bucket
            ims.experimentId = image_set_id
            ims.experimentName = image_set_id
            ims.pixelsPerMm = None
            ims.plate_name = plate_name
            ims.day = int(m_fn.group('day'))
            ims.lighting = lightings[m_fn.group('typ')]
            self.image_sets[key] = ims
        else:
            ims = self.image_sets[key]
            if ims.lighting != lightings[m_fn.group('typ')]:
                raise ValueError(f"Lighting mismatch: {name} {ims.lighting} != {lightings[m_fn.group('typ')]}")
            if ims.day != int(m_fn.group('day')):
                raise ValueError(f"Day mismatch: {name} {ims.day} != {int(m_fn.group('day'))}")
        image_set = self.image_sets[key]
        
        # Update the created time range
        if not hasattr(image_set, 'created_min') or image_set.created_min > create_time:
            image_set.created_min = create_time
        if not hasattr(image_set, 'created_max') or image_set.created_max < create_time:
            image_set.created_max = create_time
            
        # Group by well
        wn = m_fn.group('well')
        w = image_set.add_well(wn)
        w.add_image(name, m_fn.group('ext'), 'raw', create_time)            
        
        # Count images and return
        self.image_count += 1
        return image_set

    def parse_olympus_tiff(self, name: str, create_time: datetime):
        # Parse the name and metadata
        #   java/convert/0207/D3/day14/DAPI_01.tif
        pat = re.compile(r'java/convert/(?P<group>PD|)(?P<plate>\d+)/(?P<well>[A-P]\d+)/day(?P<day>\d+)/(?P<channel>\w+)_(?P<index>\d+)\.tif')
        m = pat.match(name)
        def debug():
            log(f"  Pat: {pat}")
            if m:
                log(f"  Match: {m} <- {pat} <- {name}")
                log(f"  Groups: {m.groups()}")
        if not m:
            if name.endswith('.json'):
                return None
            debug()
            raise ValueError(f"Bad name: {name}")
                
        # capture experimental plates that have a prefix
        group = 'main'
        pref = 'Plate'
        if m.group('group'):
            group = 'PD'
            pref = 'PD'
        plate_name = pref + m.group('plate')
            
        # capture experiment id
        lightings = {'DAPI': 'FL', 'FITC': 'FL', 'TRITC': 'FL', 'Cy5': 'FL', 'BF': 'BF'}
        parts = [
            m.group('group'), m.group('plate'), m.group('day'),
            m.group('index'), lightings[m.group('channel')]
        ]
        image_set_id = "_".join([p.replace("_","") for p in parts if p])
        #log(f"Image set: {image_set_id}")
        
        # retrieve or create image set 
        key = image_set_id
        if key not in self.image_sets:
            ims = ImageSet(self.repo, group, image_set_id, plate_name)
            # Fill in meta data, as it's not provided in the bucket
            ims.experimentId = image_set_id
            ims.experimentName = image_set_id
            ims.pixelsPerMm = None
            ims.plate_name = plate_name
            ims.day = int(m.group('day'))
            ims.lighting = 'BF' if m.group('channel') == 'BF' else 'FL'
            self.image_sets[key] = ims
        else:
            ims = self.image_sets[key]
            if m.group('channel') != 'BF' and ims.lighting == 'BF':
                raise ValueError(f"Lighting mismatch: {name} {ims.lighting} != {m.group('channel')}")
            if ims.day != int(m.group('day')):
                raise ValueError(f"Day mismatch: {name} {ims.day} != {int(m.group('day'))}")
        image_set = self.image_sets[key]
        
        # Update the created time range
        if not hasattr(image_set, 'created_min') or image_set.created_min > create_time:
            image_set.created_min = create_time
        if not hasattr(image_set, 'created_max') or image_set.created_max < create_time:
            image_set.created_max = create_time
            
        # Group by well
        wn = m.group('well')
        w = image_set.add_well(wn)
        w.add_image(name, 'tif', m.group('channel'), create_time)            
        
        # Count images and return
        self.image_count += 1
        return image_set

    def parse_olympus_raw(self, name: str, create_time: datetime):
        # Parse the name and metadata
        pat_dir = re.compile(r'windowsService/(?P<group>PD|)(?P<plate>\d+)/day(?P<day>\d+)/(?P<well>[A-Z]\d+)/.*')
        pat_fn = re.compile(r'.*/_?(?P<typ>[PF])_Plate(?P<group>PD|)(?P<plate>\d+)(?P<tag0>_\d+)?_Day(?P<day>\d+)_(Session|)(?P<tag1>\d+_)?(?P<well>[A-Z]\d+)(?P<cur>_[^/]*|)?_(?P<tag2>\d+).*\.(?P<ext>vsi|ets)')
        m_dir = pat_dir.match(name)
        m_fn = pat_fn.match(name)
        def debug():
            log(f"  Dir: {pat_dir}")
            log(f"   Fn: {pat_fn}")
            if m_dir:
                log(f"  Match: {m_dir} <- {pat_dir} <- {name}")
                log(f"  Groups: {m_dir.groups()}")
            if m_fn:
                log(f"  Match: {m_fn} <- {pat_fn} <- {name}")
                log(f"  Groups: {m_fn.groups()}")
        if not m_dir or not m_fn:
            debug()
            raise ValueError(f"Bad name: {name}")
        
        # Sanity check
        for n in ['group', 'plate', 'day', 'well']:
            if m_dir.group(n) != m_fn.group(n):
                if n != 'day' or int(m_dir.group(n)) != int(m_fn.group(n)):
                    log(f"Field mismatch: {name}")
                    log(f"  Dir: {m_dir.groups()}")
                    log(f"   Fn: {m_fn.groups()}")
                    raise ValueError(f"Field {n} mismatch: {m_dir.group(n)} != {m_fn.group(n)}")
        
        # capture experimental plates that have a prefix
        group = 'main'
        pref = 'Plate'
        if m_fn.group('group'):
            group = 'PD'
            pref = 'PD'
        plate_name = pref + m_fn.group('plate')
            
        # capture experiment id
        parts = [
            m_fn.group('group'), m_fn.group('typ'), m_fn.group('plate'), m_fn.group('day'),
            m_fn.group('tag0'), m_fn.group('tag1'), m_fn.group('cur'), m_fn.group('tag2')
        ]
        image_set_id = "_".join([p.replace("_","") for p in parts if p])
        if "stack" in image_set_id:
            debug()
            raise ValueError(f"Bad image set id: {name} {image_set_id}")
        #log(f"Image set: {image_set_id}")
        
        lightings = {'P': 'BF', 'F': 'FL'}

        # retrieve or create image set 
        key = image_set_id
        if key not in self.image_sets:
            ims = ImageSet(self.repo, group, image_set_id, plate_name)
            # Fill in meta data, as it's not provided in the bucket
            ims.experimentId = image_set_id
            ims.experimentName = image_set_id
            ims.pixelsPerMm = None
            ims.plate_name = plate_name
            ims.day = int(m_fn.group('day'))
            ims.lighting = lightings[m_fn.group('typ')]
            self.image_sets[key] = ims
        else:
            ims = self.image_sets[key]
            if ims.lighting != lightings[m_fn.group('typ')]:
                raise ValueError(f"Lighting mismatch: {name} {ims.lighting} != {lightings[m_fn.group('typ')]}")
            if ims.day != int(m_fn.group('day')):
                raise ValueError(f"Day mismatch: {name} {ims.day} != {int(m_fn.group('day'))}")
        image_set = self.image_sets[key]
        
        # Update the created time range
        if not hasattr(image_set, 'created_min') or image_set.created_min > create_time:
            image_set.created_min = create_time
        if not hasattr(image_set, 'created_max') or image_set.created_max < create_time:
            image_set.created_max = create_time
            
        # Group by well
        wn = m_fn.group('well')
        w = image_set.add_well(wn)
        w.add_image(name, m_fn.group('ext'), 'raw', create_time)            
        
        # Count images and return
        self.image_count += 1
        return image_set

    def parse_cytosmart(self, name: str, create_time: datetime, metas: dict):
        # skip info files
        if name.startswith('info/'):
            self.skip("info", name)
            return None
        
        # Parse the name and metadata
        parts = name.split('/')
        
        # capture experimental plates that are in subdirectories
        group = 'main'
        if len(parts) == 5 or ( len(parts) == 4 and parts[3] == 'scan_metadata.json' ):
            group = parts[0]
            parts = parts[1:]
        if len(parts) < 2:
            self.skip("short", name)
            return None
        plate_name = parts[0]
        image_set_id = parts[1]
        
        # retrieve or create image set 
        key = f"{group}/{image_set_id}"
        if key not in self.image_sets:
            ims = ImageSet(self.repo, group, image_set_id, plate_name)
            ims.lighting = 'BF'
            self.image_sets[key] = ims
        image_set = self.image_sets[key]
        
        # Update the created time range
        if not hasattr(image_set, 'created_min') or image_set.created_min > create_time:
            image_set.created_min = create_time
        if not hasattr(image_set, 'created_max') or image_set.created_max < create_time:
            image_set.created_max = create_time
            
        # Sort out wells and metadata
        if len(parts) == 4:
            wn = parts[2]
            wn2 = parts[3].split('_')[0]
            if wn != wn2:
                log(f"Well mismatch: {plate_name} {image_set_id} {wn} {wn2}")
                return None
            # Group by well
            wi = image_set.add_well(wn)
            wi.add_image(name, 'jpg', 'BF', create_time)         
        elif len(parts) == 3 and parts[2] == 'scan_metadata.json':
            if image_set.meta:
                log(f"Duplicate meta: {plate_name} {image_set_id} {name}")
                return None
            else:
                image_set.meta = json.loads(metas[name])
                if set(image_set.meta.keys()) != set(
                    ['experimentId', 'scanNumber', 'experimentName', 'numberOfWells', 'pixelsPerMm']
                ):
                    log(f"Bad meta: {name} {image_set.meta}")
                    return None
        else:
            log(f"Bad name: {name} {len(parts)} parts")
            return None
        
        # Count images and return
        self.image_count += 1
        return image_set
    
    def close(self):
        for image_set in self.image_sets.values():
            image_set.close()
        return

    def query(self, table, cols: str = "*"):
        log(f"Loading {table}")
        df = bigquery().query(
            f"""SELECT {cols} FROM `{table}`;"""
        ).result().to_dataframe()
        df['well'] = df['image'].apply(lambda x: re.search(r'(?P<well>[A-P]\d+)_export.jpg', x).group('well'))
        df['scan'] = df['image'].apply(lambda x: x.split('/')[1])
        df = df.drop(columns=['image'])
        return df
    
    def cluster_df(self):
        return self.query(
          f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_CLUSTER_TABLE}",
          "image, cluster, count_area, mean_area, std_area, min_area, max_area"
        )
    
    def lipid_df(self):
        return self.query(f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_LIPID_TABLE}")
    
    def qcparms(self):
        df = self.lipid_df()
        log(f"Dataframe: loaded {df.shape} rows\n{df}")
        by_scan = df.groupby('scan')
        by_well = df.groupby(['scan', 'well'])
        by_cluster = df.groupby(['scan', 'well', 'cluster'])

        df_well = by_well.agg({'area': ['count', 'mean', 'max'], 'circularity': ['mean', 'min']})
        log(f"Wells:\n{df_well}")
        df_scan = by_scan['well'].nunique()
        log(f"Scan:\n{df_well}")
        df_scan["wells"] = df_scan['scan'].apply(lambda x: self.image_sets[x].numberOfWells)
        df_scan["day"] = df_scan['scan'].apply(lambda x: self.image_sets[x].day)
        
        return df_well, df_scan 

    def all_plates(self):
        plate_names = set([s.plate_name for s in self.image_sets.values() if s.plate_name is not None])
        plates = [PlateImageSets(self, [pn]) for pn in plate_names]
        plates.sort(key=lambda x: x.plate_names)
        return plates
        
    def list_plates(self):
        for p in self.all_plates():
            log(f"Plate {p.plate_names} {p.plate_names[0]} {len(p.image_sets)}: {[ims.day for ims in p.image_sets]}") 
            
    def list_plate(self, plate_name: str):
        pis = PlateImageSets(self, [plate_name])
        for ims in pis.image_sets:
            log(f"  {ims.id} {ims.day} {ims.experimentName}")
        
    def summary(self):
        def sum_line(name, pred):
            sets = [image_set for image_set in self.image_sets.values() if pred(image_set)]
            log(f"{name:16s}: {len(sets)}")

        sum_line('Image Sets', lambda image_set: True)
        log(f"Experiments     : {len(set([s.experiment_id for s in self.image_sets.values()]))}")
        log(f"Plates          : {len(set([s.plate_name for s in self.image_sets.values()]))}")

        groups = set([image_set.group for image_set in self.image_sets.values()])
        for s in groups:
            sum_line(s, lambda image_set: image_set.group == s)

        for k in plate_maps.keys():
            sum_line(f"{k} wells", lambda image_set: image_set.numberOfWells == k)

        ress = set([image_set.pixelsPerMm for image_set in self.image_sets.values()])
        for r in ress:
            sum_line(f"{r} pixels/mm", lambda image_set: image_set.pixelsPerMm == r)

        sum_line('No meta data', lambda image_set: image_set.meta is None or len(image_set.meta) == 0)
        sum_line('No plate', lambda image_set: image_set.plate_name is None)

        lightings = set([image_set.lighting for image_set in self.image_sets.values()])
        for l in lightings:
            sum_line(f"{l} lighting", lambda image_set: image_set.lighting == l)

        def sum_sub(kind, what):
            sets = [image_set for image_set in self.image_sets.values() if what(image_set)]
            log(f"{kind:16s}: {len(sets)}")
            if sets:
                for s in sets[:10]:
                    log(f"  {s.id} {s.plate_name} {len(what(s)):3d}/{s.numberOfWells} {lform(what(s))}")

        sum_sub('Missing wells', lambda image_set: image_set.missing_wells)
        sum_sub('Extra wells', lambda image_set: image_set.extra_wells)

        # Count up the wells
        count = 0
        for s in self.image_sets.values():
            count += len(s.wells)
        log(f"Total files: {count+len(self.image_sets)}/{self.image_count}")

        #log(f"Meta:\n"+str(list(self.image_sets.values())[0].meta))

class PlateImageSets:
    """A set of images for a batch of plates."""
    def __init__(self, imss: ImageSets, plate_names: List[str]):
        if any([not isinstance(pn, str) for pn in plate_names]):
            raise ValueError(f"Some plate names not strings: {plate_names}")
        self.plate_names = plate_names
        ims = [ims for ims in imss.image_sets.values() if ims.day and ims.plate_name in plate_names]
        self.image_sets = sorted(ims, key=lambda x: x.day)
        self.repo = imss.repo
        crl = datetime.min.replace(tzinfo=UTC)
        crf = datetime.max.replace(tzinfo=UTC)
        for im in self.image_sets:
            if im.created_max > crl:
                crl = im.created_max
            if im.created_min < crf:
                crf = im.created_max
        self.last_updated = crl
        self.first_updated = crf
        #for im in self.image_sets:
        #    log(f"  {im.id} {im.day} {im.experimentName}")
            
        # Infer the seed time. 
        #   We want the 'day' parameter to be the timedelta of seed_time and the image_set created time.

    def __repr__(self):
        return f"PlateImageSets {self.repo.name} {','.join(self.plate_names)}  {len(self.image_sets)} {tform(self.last_updated)}"
    
    def infer_seed_time(self):
        # Find most consistent day
        counts = [0 for ims in self.image_sets]
        max_count = 0
        max_i = -1
        log(f"Plates {self.plate_names}, image sets: {len(self.image_sets)}")
        for i, ims in enumerate(self.image_sets):
            t0 = ims.created_min + (ims.created_max - ims.created_min)/2 - timedelta(days=ims.day)
            for ims2 in self.image_sets:
                tdmin = timedelta(days=ims2.day-0.5)
                tdmax = timedelta(days=ims2.day+0.5)
                if ims2.created_min - tdmax < t0 and ims2.created_max - tdmin > t0:
                    counts[i] += 1
            if counts[i] > max_count:
                max_count = counts[i]
                max_i = i
            tm = ims.created_min + (ims.created_max - ims.created_min)/2
            log(f"    Count {counts[i]}  {t0}   {ims.day}   {(tm - t0).days}")
#        if max(counts) < min(3, len(self.image_sets)):
#            raise Exception(f"Can't infer seed time: {max(counts)}/{len(self.image_sets)}")
        
        tmin = datetime.min.replace(tzinfo=UTC)
        tmax = datetime.max.replace(tzinfo=UTC)
        log(f"max_i: {max_i}")
        for i, ims in enumerate(self.image_sets):
            if i == max_i:
                log(f"    Seed time: {ims.day} {ims.created_min} - {ims.created_max}")
                td = timedelta(days=ims.day)
                if ims.created_min - td > tmin:
                    tmin = ims.created_min - td
                if ims.created_max - td < tmax:
                    tmax = ims.created_max - td
                log(f"    Time range: {ims.day:2d}   {ims.created_min} - {ims.created_max}   {ims.created_max-ims.created_min}")
        if (tmax-tmin) > timedelta(days=1):
            for ims in self.image_sets:
                td = timedelta(days=ims.day)
                log(f"  {ims.day:2d} {ims.created_min - td} - {ims.created_max - td}")
            raise Exception(f"Can't infer seed time: {tmin} - {tmax}")
        seed_time = tmin + (tmax - tmin)/2
        self.max_i = max_i
        self.counts = counts

        log(f"Seed time: {seed_time}")
        return seed_time
        
    def assign_seed_time(self, seed_time):
        log(f"Seed time: {seed_time}")
        self.seed_time = seed_time
        # Assign new image times to those that don't fit
        for i, ims in enumerate(self.image_sets):
            created = ims.created_min
            day0 = (created - self.seed_time + timedelta(days=0.5)).days
            day = day0
            # if i != self.max_i and day0 != ims.day:
            #     created = self.seed_time + timedelta(days=ims.day)
            #     day = plate_days(created, self.seed_time)
            log(
                f"  {ims.day:2d} {day0:2d} -> {day:2d}   " +
                f"{ims.created_min.strftime('%b %d, %H:%M')} - {ims.created_max.strftime('%b %d, %H:%M')}"
            )
            if day != ims.day:
                log(f"Error inferring seed time: {ims.day} {day0} {day}")
            ims.created = created

cache = "cache"
encoding = "utf8"

def get_image_sets(repo: ImageRepo):
    log(f"Load {repo} manifest.")
    # We don't reload the cache from the user interface when deployed to avoid long delays
    td = timedelta(hours=16) if not appinfo.deployed else timedelta(days=10000)
    
    # Cache the blobs and metadata locally
    cache = cache_store()
    fn  = repo.name + '-blobs.txt'
    fnm = repo.name + '-meta.txt'
    last = cache.last_modified(fn)
    now = datetime.now(timezone.utc)
    log(f"Exists: {cache.exists(fn)} {cache.exists(fnm)}, modified: {last} {now}")
    if last: 
        log(f"  Time delta: {now-last} > {td}: {now-last > td}")
    if not cache.exists(fn) or not cache.exists(fnm) or not last or now-last > td:
        count = 0
        if cache.exists(fn): 
            with cache.open(fn) as f:
                count = len(f.readlines())
        log(f"Downloading image names from {repo.project}:{repo.bucket} to {cache}{fn}")
        blobs = storage(repo.project).list_blobs(repo.bucket)
        with cache.open(fn, "wt") as f, cache.open(fnm, "wt") as fm:
            i = 0
            latest = datetime.min.replace(tzinfo=UTC)
            with tqdm(blobs, total=count) as pbar:
                for blob in pbar:
                    if i % 1000 == 0: 
                        pbar.set_description(f"{i} {blob.name[-60:]} {tform(blob.time_created)}")
                    # skip image info cache files
                    is_meta = blob.name.endswith('scan_metadata.json')
                    if blob.name.endswith('.json') and not is_meta:
                        continue
                    if blob.time_created > latest:
                        latest = blob.time_created
                    f.write(f"{blob.name}\t{blob.time_created}\n")
                    if is_meta:
                        fm.write(f"{blob.name}\t{blob.download_as_text()}\n")
                    i += 1
            log(f"Downloaded list of {i} images, {i-count} new, latest: {tform(latest)}")
        # f = StringIO()
        # fm = StringIO()
        # i = 0
        # for blob in blobs:
        #     if i % 10000 == 0: log(f"{i} {blob.name} {blob.time_created}")
        #     f.write(f"{blob.name}\t{blob.time_created}\n")
        #     if blob.name.endswith('scan_metadata.json'):
        #         fm.write(f"{blob.name}\t{blob.download_as_text()}\n")
        #     i += 1
        # f.seek(0)
        # cache.save(f.read().encode(encoding), fn)
        # fm.seek(0)
        # cache.save(fm.read().encode(encoding), fnm)
    log(f"Reading cached image names from {cache}{fn}")
    with cache.open(fn) as f:
        blobs = f.readlines()
    metas = dict()
    with cache.open(fnm) as f:
        lines = f.readlines()
        for line in lines:
            name, meta = line.strip().split('\t')
            metas[name] = meta

    # Process the blobs and metadata
    log(f"Images: {len(blobs)}")
    image_sets = ImageSets(repo)
    i = 0
    j = 0
    for line in blobs:
        name, created = line.strip().split('\t')
        i += 1
        p = image_sets.parse(name, created, metas)
        if i % 10000 == 0: log(f"{i} {name}")
        if not p:
            j += 1 
            if j <= 10: log(f"{i} {name}")
    log(f"Skipped: {j}/{i}")
    if image_sets.skipped:
        df = pd.DataFrame(
            [(k,len(xs)) for k, xs in image_sets.skipped.items()], 
            columns=["reason", "count"]
        )
        df = df.sort_values(by='count', ascending=False)
        log(f"\n{df}")
    image_sets.close()
    image_sets.summary()
    return image_sets
