import pandas as pd, numpy as np
import skimage, scipy.ndimage as ndi
from sqlalchemy.orm.session import Session
from imageio.v2 import imread
from base.util import lazy
from base.io import Store, cache_store, File, make
from plates.model import Plate, WellPos, PlateMap, PlateFormat, MapGroup, Dose
from images.model import ImageSet, Image
from base.util import make_logger
log = make_logger(__name__)

def negatives(db: Session, well_names: list[str] = ['A1', 'B1', 'C1', 'D1', 'E1', 'F1', 'G1', 'H1']) -> PlateMap:
    # Build the PlateMap
    format = db.get(PlateFormat, "384-well")
    pm = PlateMap(
        format_name = format.name,
        name = "Negatives",
        description = "Negative image controls",
        type = "image"
    )
    wps = { wp.well_name: wp for wp in format.wells }
    negatives = MapGroup(group_type="negative")
    pm.map_groups = [negatives]
    
    # Add the negatives
    for wn in well_names:
        if wn not in wps:
            raise ValueError(f"Well name {wn} not found in {format} plate format")
        negatives.doses.append(
            Dose(concentration__value=0.0, concentration__unit="uM", well_pos=wps[wn])
        )
    return pm

class Pipeline:
    def __init__(self, typ: type, imgset: ImageSet, controls: PlateMap, cache: Store = cache_store()):
        self.typ = typ
        self.imgset = imgset
        self.controls = controls
        self.store = cache
        wells = {img.wellPos.well_name: img.wellPos for img in imgset.images}
        self.wells = [typ(self, imgset, wn) for wn in wells.keys()]
        log(f"Processing {len(self.wells)} images for {typ.__name__}")
        
    @lazy
    def result(self):
        return self.typ.compute(self.typ, self)

# class Well:
#     def __init__(self, pipeline: Pipeline, pos: WellPos):
#         self.pipeline = pipeline
#         self.pos = pos

