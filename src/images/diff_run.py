import os

default_env = {
        "DBHOST":"localhost",
        "DBPORT":"5432",
        "DBUSER":"app",
        "DBPASS":"pass",
        "DBNAME":"app",
        "DRIVERNAME":"postgresql+pg8000",
        "PORT":"5001",
        "SECRETS":".secrets"
    }
    
for key, value in default_env.items():
    if key not in os.environ:
        os.environ[key] = value

from images.parse import java_vm
from images.repo import Repo
from images.differentiation import Differentiation
from base.util import make_logger
from base.io import diff_store
from images.parse import cleanup_temp_files
import pandas as pd
import json
import sys
import traceback

log = make_logger(__name__)

def run_differentiation(plate_name, day):
    repo = Repo("olympus")
    log(f"Processing plate: {plate_name}, day: {day}")
    store = diff_store()

    plate_dir = f"{plate_name}/Day{day}"

    # Checking for state.json
    state_file = f"state.json"
    state_path = f"{plate_dir}/{state_file}"
    processed_wells = set()
    if store.exists(state_path):
        try:
            with store.open(state_path, "r") as f:
                state_data = json.load(f)
                processed_wells = {item.get("well") for item in state_data.get("results", [])}
                log(f"Resuming from state file. Already processed: {processed_wells}")
        except json.JSONDecodeError as e:
            log(f"Error loading state file (corrupted JSON): {e}")
            log(f"Deleting corrupted file and starting fresh")
            # Delete the corrupted file
            store.delete(state_path)
            processed_wells = set()
            results = []
        except Exception as e:
            log(f"Error loading state file: {e}")
            processed_wells = set()
            results = []

    try:
        with java_vm():
            results = []
            
            # Load existing results from state file
            if store.exists(state_path):
                with store.open(state_path, "r") as f:
                    state_data = json.load(f)
                    results = state_data.get("results", [])
            
            ims = repo.image_set(plate_name, day)
            if not ims:
                log(f"No image sets found for plate {plate_name}, day {day}")
                return None
            log(f"Found image set: {ims.plate_name} day {ims.day} with {len(ims.wells)} wells")

            for well_name in ims.wells.keys():
                if well_name in processed_wells:
                    log(f"Skipping already processed well {well_name}")
                    continue
                
                try:
                    diff = Differentiation(ims, plate_name, well_name, day)
                    result = diff.measure()

                    result_dict = result.iloc[0].to_dict()
                    results.append(result_dict)
                    log(f"Well {well_name}: {result['positive']} positive, {result['negative']} negative")
                    
                    # Update state after each well
                    with store.open(state_path, "w") as f:
                        state_data = {"results": results}
                        json.dump(state_data, f, indent=4)
                        
                except Exception as e:
                    log(f"Error processing well {well_name}: {e}")
                    traceback.print_exc()
    except Exception as e:
        log(f"Error during processing: {e}")
        traceback.print_exc()
    finally:
        # Save final state
        if results:
            with store.open(state_path, "w") as f:
                state_data = {"results": results}
                json.dump(state_data, f, indent=4)
        cleanup_temp_files()

    if results:
        df = pd.DataFrame(results)
        output_file = f"{plate_name}_day{day}_differentiation.csv"
        output_path = f"{plate_dir}/{output_file}"
        with store.open(output_path, "w") as f:
            df.to_csv(f)
        log(f"Results saved to {output_file}")
        return df
    else:
        log("No results generated")
        return None

def main():
    """Entry point for command-line usage"""

    if len(sys.argv) > 2:
        plate_name = "Plate"+sys.argv[1]
        culture_day = int(sys.argv[2])
    else:
        plate_input = input("Please enter a plate number (format: #### or PD####): ")
        plate_name = plate_input if plate_input.startswith("Plate") or plate_input.startswith("PD") else "Plate"+plate_input
        culture_day = int(input("Please enter culture day: "))
    
    run_differentiation(plate_name, culture_day)

if __name__ == "__main__":
    main()