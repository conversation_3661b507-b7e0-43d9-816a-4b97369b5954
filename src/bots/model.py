from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import func
from datetime import datetime

from base.database import Base, UtcDateTime

class Worker(Base):
    """Keep track of bot workers. Each running or finished worker is a row in this table.
    """
    __tablename__ = "worker"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(nullable=False)
    started: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())
    last: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())
    finished: Mapped[datetime] = mapped_column(UtcDateTime, nullable=True)
