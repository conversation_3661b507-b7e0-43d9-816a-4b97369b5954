from typing import List
from datetime import timezone, timedelta
import io
import asyncio
import PIL as pil
from sqlalchemy.orm import Session, aliased
from sqlalchemy.sql.expression import exists
from plates.model import Plate, WellPos
from images.model import ImageSet, Image
from base.clients import storage
from base.database import get_db, flush, transact
from qc.data import parse_plate_no, plate_days
from workflow.orm.rar import Request, Action, Result, ResultReason
from workflow.orm.steps import ImageIn, SeedIn, UploadIn, UploadOut
from workflow.flow import ActionState
from workflow.culture import UploadStep
from workflow.workflow import Flow
import images.repo as repo
from images.repo import PlateImageSets, CytosmartRepo, Repo
from images.match import ImageMatcher
from bots.bot import Bot
from workflow.culture import NewImageSetResult
from base.util import make_logger
log = make_logger(__name__)


#
# Bot to operate culture.upload step
#
class UploadBot(Bot):
    def __init__(self, db: Session):
        super().__init__(db, Flow(UploadStep))

    def backfill_requests(self, db: Session):
        # Find all imaging requests that are done and passed but not queued for upload
        subq = ~db.query(UploadIn).filter(Request.id == UploadIn.image_req).exists()
        q = db.query(ImageIn, Request, Result).\
            filter(ImageIn.device == 'cytosmart').\
            filter(ImageIn.request == Request.id).\
            filter(Request.step == 'image').\
            filter(Request.action == Result.id).\
            filter(Result.result == 'pass').\
            filter(subq)
        #log(f"Query: {q.statement}")
        reqs = q.all()
        log(f"Found {len(reqs)} requests to backfill.")
        # Backfill the requests
        for _, req, _ in reqs:
            with transact(db):
                ureq = self.step.new_request(db, "upload")
                upload_in = UploadIn(request = ureq.id, image_req = req.id)
                db.add(upload_in)
        log(f"Backfilled {len(reqs)} requests.")


    def process_olympus(self, state, plate, ires):
        image_sets = (
            self.db.query(ImageSet)
            .filter(ImageSet.plate_id == plate.id)
            .filter(ImageSet.device == 'olympus')
            .filter(ImageSet.created > (ires.ended - timedelta(days=1)))
            .filter(ImageSet.created < (ires.ended + timedelta(days=1)))
            .filter(~ exists().where(UploadOut.image_set == ImageSet.id))
            .all()
        )
       
         
        if len(image_sets) < 1:
           log(f"Could not find image set that match date")
           raise ValueError("No olympus image set within 1 day of request")
        
        min_ = None
        for ims in image_sets:
            if not min_:
                closest_ims = ims
                min_ = abs(ims.created - ires.ended)
            if min_ > abs(ims.created - ires.ended):
                closest_ims = ims
                min_ = abs(ims.created - ires.ended)
                
        reason = self.db.get(ResultReason, "pass")
        return NewImageSetResult(state.action.id, "image set linked", reason, closest_ims)
        
    def process(self, state: ActionState) -> NewImageSetResult:
        req, inp, *_ = state.requests.items[0]
        
        # Set up the plate ImageMatcher
        imgin, plate = self.db.query(ImageIn, Plate).\
            filter(ImageIn.request == inp.image_req).\
            filter(ImageIn.plate == Plate.id).\
            one()
        m = ImageMatcher(self.db, plate, repos=[Repo("cytosmart"), Repo("olympus")])

        for i, (iin, ireq, iact, ires) in enumerate(m.iacts):
            if ireq.id == inp.image_req:
                day = plate_days(ires.ended, m.seed_time)
                device = iin.device
                if device == "cytosmart" and iact.station != "cytosmart":
                    device = iact.station
                s = f" (was {iin.device} at {iact.station})" if iin.device != device else ""
                log(f"Working on {plate.name}, day {day}, device={device}{s}.")      

                ims = m.find_match(device, iact, ires)
                db_ims, reason, msg = m.insert_image_set(ims)
                break
        reason = self.db.get(ResultReason, reason)
        return NewImageSetResult(state.action.id, msg, reason, db_ims)

if __name__ == '__main__':
    bot = UploadBot(get_db())
    #bot.backfill_requests(get_db())
    #asyncio.run(bot.run(once=False))

