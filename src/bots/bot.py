import sys
from time import sleep
from datetime import datetime, timezone
import traceback
from sqlalchemy.orm.session import Session
from sqlalchemy.exc import ProgrammingError

from app.auth import make_user
from base.io import logs_store
from workflow.orm.rar import Action, Request
from workflow.flow import FlowStep, Requests, BusyActions, ActionState
from base.util import make_logger, redirect

log = make_logger(__name__)
logs = logs_store()

#
# Bot base class
#
class Bot:
    def __init__(self, db: Session, step: FlowStep, user: str = None, station: str = None):
        self.db = db
        self.step = step
        # Make sure user and station are present
        self.user = user if user else step.name+"-bot"
        make_user(db, self.user, role="bot")
        self.station = station if station else step.name+"-station"
        self.tried = set()
        

    def pick_request(self, wait = True, complete = True) -> ActionState:
        """Pick one request and start an action to perform on it."""
        
        if complete:
            # See if an action is already in progress. 
            #   This means either multiple bots running on the same station (avoid!) 
            #   or an unfinished action due to an abnormally terminated bot.
            #   We ensure (hope?) that bot code is idempotent and proceed with the found action.
            busy = BusyActions(self.step, self.db)
            acts = [a for _, _, a, _ in busy.items 
                if a.operator == self.user and a.station == self.station
                and a.id not in self.tried
            ]
            # acts = self.db.query(Action).filter(
            #     Action.step == self.step.name, 
            #     Action.operator == self.user, 
            #     Action.station == self.station
            # ).all()
            if len(acts) > 0:
                log(f"Action in progress: {len(acts)} {acts[0]}")
                return ActionState(self.step, self.db, acts[0])
            log(f"{len(acts)} actions in progress with {self.user} at {self.station} for {self.step}")
        
        # Pick a request for a new action to perform
        state = None
        while not state:
            reqs = Requests(self.step, self.db)
            if reqs.n<1:
                if not wait: 
                    log(f"No requests to process.")
                    return None
                dt = 60
                log(f"No requests to process. Waiting {dt} seconds...")
                sleep(dt)
            else:
                req, out, *_ = reqs.items[0]
                log(f"Pick 1/{reqs.n} requests: {req} {out}")
                try:
                    a = self.step.new_action(self.db, self.user, self.station, [req])
                    return ActionState(self.step, self.db, a)
                except ProgrammingError as e:
                    dt = 1
                    log(f"Error: {type(e)}")
                    log(f"Error: {e}")
                    # if not wait: 
                    #     log(f"Assuming Race condition.")
                    #     return None
                    log(f"Assuming Race condition. Waiting {dt} seconds...")
                    sleep(dt)


    async def sim_one(self, action: Action | int):
        """Perform one action."""
        if isinstance(action, int):
            id = action
            action = self.db.query(Action).get(id)
            if not action:
                log("No action, try request instead:")
                req = self.db.query(Request).get(id)
                action = self.step.new_action(self.db, self.user, self.station, [req])
        state = ActionState(self.step, self.db, action)
        if state.result is not None:
            log(f"Action already complete: {state.result}. Still checking...")
        log(f"Starting action: {state}")
        res = self.process(state)
        log(f"Finished action: {res.result} {res.comment}")
        if state.result is None:
            state.update_tag("status", res.comment)
            await self.step.finish_action(self.db, state, res)


    async def run(self, once = False, wait = None, on_action = None, complete = True):
        """Run the bot."""
        if wait is None: wait = not once
        while True:
            log(f"Picking request...")
            state = self.pick_request(wait=wait, complete=complete)
            if not state: 
                log("No requests to process.")
                return
            if state.requests.n>1: 
                raise ValueError(f"Multiple requests: {state.requests.n}")
            if not state: return
            tsm = datetime.now(timezone.utc).strftime("%Y%m")
            tsd = datetime.now(timezone.utc).strftime("%d-%H%M%S")
            fn = f"{tsm}/{self.step.name}/{tsd}-{state.action.id}.log"
            with redirect(log, logs.open(fn, "wt"), f"{logs.tag}{fn}") as red:
                log(f"Starting action {state.action.id}, log to {red.fn}")
                try:
                    res = self.process(state)
                except Exception as e:
                    log(f"Error: {type(e)} {e}")
                    traceback.print_exception(e)
                    state.update_tag("status", str(e))
                    self.tried.add(state.action.id)
                    log(f"Not finished: {state.action.id} {type(e)} {e}")
                    if once: return
                    continue    # Leave this undone and try again next time we start a new bot
                log(f"Finished action: {state.requests.n} requests, {res.result} {res.comment}")
                if on_action:
                    on_action()
            state.update_tag("status", res.comment)
            await self.step.finish_action(self.db, state, res)
            if once: return