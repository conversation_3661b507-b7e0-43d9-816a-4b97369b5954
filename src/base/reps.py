#
#  Combinators for managing object representations.
#    Used for forms, table views, and data parsing
#
import types
from typing import List, Callable
from datetime import datetime
import pytz
from enum import Enum
from dataclasses import dataclass
from fastapi import Form
from base.database import Base, UtcDateTime
from base.util import tform
from base.io import File
from app.html import node
from base.util import make_logger
log = make_logger(__name__)

nils = [None, "", "NA", "ND"]

def obj_name(o):
    if o is None: return "None"
    if isinstance(o, str): return o
    if hasattr(o, "name"): return o.name
    if hasattr(o, "__name__"): return o.__name__
    return f"{o}"

class Rep:
    """Base class for the Object Rep Combinators"""
    @staticmethod
    def join(*reps): return JointRep.from_reps(*reps)
    
    def repr(self, value): return str(value)
    def __repr__(self): return self.name
    def to_string(self, value): return str(value)
    def html(self, value): 
        if value is None: return None
        return str(value)
    def input(self, **attrs): raise NotImplementedError()
    def parse(self, value): return value
    
    def select_input(self, options, **attrs):
        if hasattr(self, "_options"):
            log(f"Use _options: {self._options}")
            options = self._options
        opts = []
        if hasattr(self, "_default") and self._default is None:
            opts.append(node("option", value="", selected="selected")("Select..."))
        for o in options:
            n = node("option", value=obj_name(o))(obj_name(o))
            log(f"  Option: {o} {obj_name(o)}")
            if hasattr(self, "_default") and self._default is not None:
                if obj_name(o)==obj_name(self._default): n = n(selected = "selected")
            opts.append(n)
        return node("select", **attrs)(*opts)
    
class ValRep(Rep):
    pass

class IntRep(ValRep):
    name: str = "int"
    typ: type = int
    def input(self, **attrs): return node("input", type="text", **attrs)()
    def parse(self, value):
        if value in nils: return None 
        return int(value)
    
class StrRep(ValRep):
    name: str = "str"
    typ: type = str
    def input(self, **attrs):
        if "name" in attrs and "password" in attrs["name"]:
            attrs['autocomplete'] = "current-password"
            return node("input", type="password", **attrs)()
        if "name" in attrs and attrs["name"]=="comment":
            return node("textarea", **attrs)()
        return node("input", type="text", **attrs)()
    def parse(self, value): 
        if value in nils: return None
        return value
    
class FloatRep(ValRep):
    name: str = "float"
    typ: type = float
    def input(self, **attrs): return node("input", type="text", **attrs)()
    def parse(self, v): 
        if v in nils: return None
        return float(v)
    
class BoolRep(ValRep):
    name: str = "bool"
    typ: type = bool
    def input(self, **attrs):
        # need to add a hidden field to ensure the value is sent. Hidden field must be first.
        return \
            node("input", type="hidden", id=attrs["id"]+".def", name=attrs["name"], value="off")() +\
            node("input", type="checkbox", **attrs)()
    def parse(self, v):
        if v in nils: return None
        if v in ["on", "true", "True", "T", "1"]: return True
        if v in ["off", "false", "False", "F", "0"]: return False
        raise ValueError(f"Invalid boolean value: {v}")
    
class DateTimeRep(ValRep):
    name: str = "datetime"
    typ: type = datetime
    def to_string(self, value): return str(value)
    def html(self, value):
        return tform(value)
    def input(self, **attrs): return node("input", type="datetime-local", **attrs)()

class FileRep(ValRep):
    name: str = "file"
    typ: type = File
    def input(self, **attrs): return node("input", type="file", **attrs)()
    
def enum_value(opts, aliases, v):
    """Check for a valid enum value with aliases"""
    def isval(o):
        """Is v a valid value for enum option o?"""
        on = obj_name(o)
        if on==v: return True
        if v in aliases: return on==aliases[v]
        return False
    # get all matching options
    xs = [o for o in opts if isval(o)]
    # Need it to be present and unique
    if len(xs)==0: raise ValueError(f"Invalid enum value: {v}")
    if len(xs)>1: raise ValueError(f"Ambiguous enum value: {v}")
    return xs[0]

class EnumRep(ValRep):
    name: str = "enum"
    def __init__(self, t):
        self.typ = t
        self.name = t.__name__
    def input(self, **attrs):
        return self.select_input([e.name for e in self.typ], **attrs)
    def parse(self, v):
        if v in nils: return None
        return enum_value([e.name for e in self.typ], self._aliases, v)

def rep(t: type) -> Rep:
    #log(f"Rep: {t}")
    match t:
        case t if isinstance(t, dict): return Rep.join(t)
        case t if t == int: return IntRep()
        case t if t == str: return StrRep()
        case t if t == float: return FloatRep()
        case t if t == bool: return BoolRep()
        case t if t == datetime: return DateTimeRep()
        case t if t == File: return FileRep()
        #case t if hasattr(t, "__enum__"): return EnumRep(t)
        case t if issubclass(t, Enum): return EnumRep(t)
        case t if t == dict: return ObjRep.from_dict("dict", {})
        case t if issubclass(t, Base): return ObjRep.from_model(t)
        case t: return ObjRep.from_class(t)

def col_type(c):
        fks = c.foreign_keys
        rcl = None
        for fk in fks:
            tn = fk.column.table.name
            #log(f"  fk: {fk} {fk.column} {tn}")
            for mapper in Base.registry.mappers:
                cl = mapper.class_
                cn = cl.__name__
                if not cn.startswith('_'):
                    tblname = cl.__tablename__
                    if tblname == tn:
                        rcl = cl
                        #log(f"  Found class: {cl}")
        #log(f"  Found class: {c}: {c.type} ({type(c.type)}) => {rcl}")
        if rcl is None:
            if isinstance(c.type, UtcDateTime): 
                #log(f"    Found class: {c}: {c.type} ({type(c.type)}) => {datetime}")
                rcl = datetime
            else: 
                rcl = c.type.python_type
        return rcl

class ObjRep(Rep):
    def __init__(self, name, fields: dict):
        self.name = name
        for r in fields.values():
            if not isinstance(r, Rep):
                raise ValueError(f"Invalid field representation: {r}")
        self.fields: dict = fields
    
    @staticmethod
    def from_model(m): 
        #log(f"From model: {m} {m.__name__}")
        r = ObjRep(m.__name__,
            {c.name: rep(col_type(c)) for c in m.__table__.columns}
        )
        r.typ = m
        #log(f"From model: {r}")
        return r
    
    @staticmethod
    def from_class(cl):
        def dct(cl):
            if not cl or not hasattr(cl, "__annotations__"): return {}
            return dct(cl.__base__) | {k: rep(v) for k, v in cl.__annotations__.items()}
        orep = ObjRep(cl.__name__, dct(cl))
        #orep = ObjRep(cl.__name__, {k: rep(v) for k, v in cl.__annotations__.items()})
        orep.typ = cl
        # Set any default values
        def sdf(c):
            if not c or not hasattr(c, "__annotations__"): return
            for k, r in orep.fields.items():
                if hasattr(c, k):
                    v = c.__dict__[k]
                    if isinstance(v, r.typ):
                        r._default = v
            sdf(c.__base__)
        sdf(cl)
        return orep
        
    @staticmethod
    def from_dict(name: str, **annots: type):
        r = ObjRep(name, {k: rep(v) for k, v in annots.items()})
        r.typ = type(name, (), annots)
        r.typ.__module__ = Base.__module__
        return r
        
    @staticmethod
    def pass_through(name: str, values: dict, hide: bool = False):
        annots = {k: type(v) for k, v in values.items()}
        r = ObjRep.from_dict(name, **annots)
        for f in r.fields:
            if hide:
                r.fields[f]._hidden = values[f]
            else:
                r.fields[f]._default = values[f]
        return r
        
    @staticmethod
    def joint(*reps):
        return ObjRep("-".join([r.name for r in reps]), {r.name+"."+k: v for r in reps for k, v in r.fields.items()})
    
    def __add__(self, other):
        """Combine two object representations into a joint representation."""
        return JointRep.from_reps(self, other)
    
    def __or__(self, other: Rep):
        """Add additional fields."""
        self.fields.update(other.fields)
        return self
    
    def __sub__(self, other: str):
        """Remove fields."""
        self.fields.pop(other)
        return self
    
    def __lt__(self, value: any):
        """Set the defaults from an instance or parse it from a dictionary."""
        if isinstance(value, dict):
            for k, v in value.items():
                if k in self.fields:
                    r = self.fields[k]
                    r._default = r.parse(v)
            return self
        for n, f in self.fields.items():
            f._default = self.get(n, value)
        return self
    
    def __call__(self, kmap: dict):
        """Return a new object representation with only the specified fields."""
        return MappedRep(self, kmap)
    
    def options(self, field, xs: List):
        """Provide a custom list of options for a field."""
        #if len(xs)>0 and isinstance(xs[0], str):
        #    raise ValueError(f"Invalid options: {xs}")
        self.fields[field]._options = xs
        #log(f"Set options for {field}: {xs} {hasattr(self.fields[field], 'options')}")
        #log(f"  {self}")
        return self
    
    def hidden(self, field, value):
        """Hide a field in forms."""
        self.fields[field]._hidden = value
        return self  
    
    def omit(self, field):
        """Omit a field from forms."""
        self.fields[field]._omit = True
        return self    
    
    def default(self, field, value):
        """Set a default value for a field."""
        self.fields[field]._default = value
        return self    
        
    def html(self, value):
        if value is None: return None
        elif isinstance(value, str): return value
        elif isinstance(value, int): return str(value)
        elif isinstance(value, Base):
            if hasattr(self.typ, "name"): return value.name
            elif hasattr(self.typ, "code"): return value.code
            else: return value.id
        else: raise ValueError(f"Invalid model value: {value}")

    def __repr__(self):
        def rstr(r):
            s = ""
            if hasattr(r, "_options"): s += "o"
            if hasattr(r, "_hidden"): s += "h"
            if hasattr(r, "_omit"): s += "x"
            if hasattr(r, "_default"): s += "d"
            if s=="": return r.name 
            elif hasattr(r, "_default"):
                return f"{r.name}[{s}]={obj_name(r._default)}"
            elif hasattr(r, "_hidden"):
                return f"{r.name}[{s}]={r._hidden}"
            else: return f"{r.name}[{s}]"
        n = self.name
        if hasattr(self, "fields"):
            n += "("+", ".join([f"{k}: {rstr(r)}" for k, r in self.fields.items()])+")"
        return n
    
    def __getitem__(self, field: str):
        return self.fields[field]
    
    def get(self, field: str, value):
        if value is None: return None
        #log(f"Get: {self.name}.{field} {value} {self.fields[field]}")
        return getattr(value, field)
            
    def repr(self, value):
        # we allow database models to be represented as their ids
        if issubclass(self.typ, Base):
            if isinstance(value, int): return self.typ.__name__+"["+str(value)+"]"
            if isinstance(value, str): return self.typ.__name__+"["+value+"]"
        # log(f"Repr: {self} {value}")
        # for k, r in self.fields.items():
        #     log(f"Field: {k} {r}")
        #     log(f"  Value: {self.get(k, value)}")
        #     log(f"  Repr: {r.repr(self.get(k, value))}")
        return self.name+"("+",".join([f"{k}: {r.repr(self.get(k, value))}" for k, r in self.fields.items()])+")"

    def htf(self, field, value):
        """Return an HTML representation of a field value."""
        #log(f"HTF: ({self.name}).{field} = {value}")
        v = self.get(field, value)
        #log(f"HTF: ({self.name}).{field} = {value} -> {v}: {type(v)}")
        #log(f"  Field: {self.fields[field]}")
        return self.fields[field].html(v)

    def input(self, **attrs):
        """Return an HTML input field for the object representation. Just a text input for now."""
        return node("input", type="text", **attrs)() 
       
    def parse(self, v):
        if v in nils: return None
        if not hasattr(self.typ, "__enum__"):
            #  pass through id(s) as string:
            if isinstance(v, str): return v
            if isinstance(v, int): return str(v)
            raise ValueError(f"Cannot parse objects: {self.typ} {v}")
        als = self._aliases if hasattr(self, "_aliases") else {}
        log(f"Parse: {self.typ} {v} {hasattr(self, '_options')}")
        return enum_value([o for o in self._options], als, v)
    
    def form_fields(self, ogen: Callable[[Base], List] = None, default = None):
        """Return the form fields for creating an HTML form."""
        fs = []
        for n, r in self.fields.items():
            if n=="id": continue
            fn = f"{self.name.lower()}.{n}"
            f = None
            if hasattr(r, "_omit"): continue
            if hasattr(r, "_hidden"): 
                v = r._hidden
                if isinstance(v, types.FunctionType): v = v()
                f = node("input", type="hidden", name=n, value=v)()
            elif issubclass(r.typ, Base) and hasattr(r.typ, "__enum__"):
                f = r.select_input(ogen(r.typ), id=fn, name=n, autocomplete=fn)
            else:
                log(f"Form field: {n} {r.typ} {r} {hasattr(r, 'options')}")
                if hasattr(r, "_options"):
                    log(f"Use _options: {r._options}")
                    f = r.select_input(r._options, id=fn, name=n, autocomplete=fn)
                else:
                    f = r.input(id=fn, name=n, autocomplete=fn)
            if hasattr(r, "_default"):
                f = f(value=r._default)
            if default is not None:
                f = f(value=self.get(n, default))
            fs.append((n, f))
        return fs

    def form_parser(self):
        """Return a dataclass suitable for parsing data from an HTML post request."""
        use = {k: r for k, r in self.fields.items() if not hasattr(r, "_omit")}
        cl = type(self.name+"Form", (), {k:Form(...) for k, r in use.items()})
        cl.__module__ = Base.__module__
        annots = {k: r.typ for k, r in use.items() if k!="id"}
        for k, v in annots.items(): 
            if issubclass(v, Base):
                annots[k] = v.__table__.columns[0].type.python_type
            #if issubclass(v, File): annots[k] = str
            #log(f"  prop {k}: {v} -> {annots[k]}")
        cl.__annotations__ = annots
        return dataclass(cl)
    
    def from_parser(self, fp):
        """Return an object representation from a form parser."""
        x = self.typ()
        for k, r in self.fields.items():
            setattr(x, k, getattr(fp, k))
        return x

    def dataclass(self):
        """Return a regular dataclass"""
        cl = type(self.name, (), {})
        cl.__module__ = Base.__module__
        cl.__annotations__ = {k: r.typ for k, r in self.fields.items()}
        return dataclass(cl)
    
    def instance(self, **kwargs):
        """Return an instance of the object representation."""
        return self.typ(**{k: r.parse(kwargs[k]) for k, r in self.fields.items()})

class JointRep(ObjRep):
    """A joint representation of multiple object representations. Combines all fields."""
    def __init__(self, reps: dict):
        names = []
        fields = dict()
        for rn, r in reps.items():
            #log(f"JointRep: {rn} {r}: {type(r)}")
            if isinstance(r, ObjRep):
                names.append(rn)
                fields.update({rn+"."+k: v for k, v in r.fields.items()})
            else:
                names.append(f"{rn}: {r.name}")
                fields.update({rn: r})
        #for k, r in reps.items(): log(f"  Rep: {k}: {r} {type(r)}")
        super().__init__("-".join(names), fields)
        self.reps = reps
        self.name = "-".join([r.name for r in reps.values()])

    @staticmethod
    def from_reps(*reps):
        rs = dict()
        for r in reps:
            if isinstance(r, tuple): rs[r[0]] = ObjRep.from_model(r[1])
            elif isinstance(r, Rep): rs[r.name] = r
            elif isinstance(r, dict): rs.update({k: rep(t) for k, t in r.items()})
            elif issubclass(r, Base): rs[r.__name__] = ObjRep.from_model(r)
            else:
                log(f"Invalid representation type: {r}: {type(r)} Base? {isinstance(r, Base)}")
                raise ValueError(f"Invalid representation type: {r}")
        return JointRep(rs)

    @staticmethod
    def from_class(cl):
        def dct(cl):
            if not cl or not hasattr(cl, "__annotations__"): return {}
            return dct(cl.__base__) | {k: rep(v) for k, v in cl.__annotations__.items()}
        return JointRep(dct(cl))
    
    def get(self, item: str, value):
        ns = item.split(".")
        i = list(self.reps.keys()).index(ns[0])
        if i==-1: raise ValueError(f"Invalid field: {item} not in {self}")
        #log(f"JointGet: {item} {value} -> {i} {value[i]}   ns: {ns}")
        try:
            if len(ns)==1: return value[i]
            elif len(ns)==2: return self.reps[ns[0]].get(ns[1], value[i])
            else: raise ValueError(f"Invalid field {item}: too many parts")
        except Exception as e:
            log(f"Exception: {item}[{i}, {ns[0]} in {list(self.reps.keys())}] {value}[{i}]")
            raise e
    
    def repr(self, value):
        rs = [k+"="+self.get(k, value) for k in self.fields.keys()]
        return self.name+"("+",".join(rs)+")"
        
class MappedRep(ObjRep):
    """A mapped representation. Selects and renames fields from another rep"""
    def __init__(self, org: ObjRep, kmap: dict):
        self.org = org
        self.kmap = kmap
        self.name = org.name + "*"
        #log(f"MappedRep org: {org.name} - {org.fields}")
        try:
            fs = {k: self.org.fields[k0] for k, k0 in kmap.items()}
        except KeyError as e:
            log(f"Items:  {kmap}")
            log(f"Fields: {org.fields}")
            raise e
        #log(f"MappedRep new: {org.name} - {fs}")
        super().__init__(org.name + '*', fs)
    
    def get(self, item: str, value):
        #log(f"Get: {item} -> {self.kmap[item]} {value}")
        return self.org.get(self.kmap[item], value)


if __name__ == "__main__":
    @dataclass
    class A:
        id: int
        name: str
        value: float
        active: bool
        created: datetime
        status: Enum
    log(rep(A))
    
    @dataclass
    class B:
        id: int
        name: str
        value: float
        active: bool
        created: datetime
        status: Enum
        other: A
    log(rep(B))
    
    repAB = rep(A) + rep(B)
    log(repAB)
    repABs = repAB({"id":"A.id", "name":"A.name", "bname":"B.name"})
    log(repABs)
    
    a = A(id=1, name="A", value=1.0, active=True, created=datetime.now(), status=1)
    b = B(id=2, name="B", value=2.0, active=False, created=datetime.now(), status=2, other=a)
    log(rep(A).repr(a))
    log(rep(B).repr(b))
    log(repAB.repr((a, b)))
    log(repABs.repr((a, b)))

