import os
import subprocess
from datetime import datetime

_vals = {}

def _pypom() -> dict:
    global _vals
    if _vals: return _vals
    dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    fn = os.path.join(dir, "pyproject.toml")
    pars = ["name", "version"]
    _vals = {}
    with open(fn, "r") as f:
        for line in f:
            for p in pars:
                if line.startswith(p+" = "):
                    _vals[p] = line.split("=")[1].strip().strip('"')
    return _vals

class AppInfo:
    """Contains information about the app."""
    def __init__(self):
        try:
            from base._appinfo import _appinfo
            _appinfo(self)
        except ImportError:
            # We are not deployed, so we can get poml and git info
            self.name = "mellitos"
            self.version = "unknown"
            self.deployed = True
            self.hash = "unknown"
            self.wip = True
            self.date = datetime.min
            try:
                root_path = os.path.dirname(os.path.dirname(__file__))
                self.name = _pypom()["name"]
                self.version = _pypom()["version"]
                self.deployed = False
                
                self.hash = subprocess.check_output(
                    ['git', 'rev-parse', 'HEAD'],
                    cwd = root_path
                ).decode('ascii').strip()
                
                ds = subprocess.check_output(
                    ['git', 'show', '-s', '--format=%ci', 'HEAD'], 
                    cwd = root_path
                ).decode('ascii').strip()
                ds = ds.replace(" ", "T", 1).replace(" ", "")
                ds = ds[:-2] + ":" + ds[-2:]
                self.date = datetime.fromisoformat(ds)
                
                self.wip = subprocess.check_output(
                    ['git', 'status', '--porcelain', '-uno'], 
                    cwd = root_path
                ).decode('ascii').strip() != ""
            except Exception as e:
                # print(f"Error getting app info: {e}")
                pass
            
    def code_id(self):
        return f"{self.hash}{'-WIP' if self.wip else ''}"
    
    def __str__(self):
        date = self.date.strftime("%Y-%m-%d %H:%M")
        return f"{self.name} {self.version} ({date}) [{self.code_id()}]{' deployed' if self.deployed else ''}"

    def generate_python(self):
        """Generate a python file with the app info to be used during deployment."""  
        return f"""from datetime import datetime
def _appinfo(app):
    app.name = "{self.name}"
    app.version = "{self.version}"
    app.date = datetime.fromisoformat("{self.date.isoformat()}")
    app.hash = "{self.hash}"
    app.wip = {self.wip}
    app.deployed = True
"""

appinfo = AppInfo()

if __name__ == "__main__":
    print(appinfo.generate_python())