import os
import re
import threading
from typing import Iterable, IO
from datetime import datetime
import pytz
import psutil
import logging
from base.appinfo import appinfo

#
#   Logging
#
#   Usage:
#     from base.util import make_logger
#     log = make_logger()
#
#   Then use log.debug(), log.info(), log.warning(), log.error(), log.critical()
#
class LogFormatter(logging.Formatter):
    
    def __init__(self, color: bool = True):
        super().__init__()
        self._last_time = None
        
        # Do colors
        self.grey = "\x1b[37;20m" if color else ""
        self.dark = "\x1b[38;20m" if color else ""
        self.yellow = "\x1b[33;20m" if color else ""
        self.red = "\x1b[31;20m" if color else ""
        self.bold_red = "\x1b[31;1m" if color else ""
        self.reset = "\x1b[0m" if color else ""
        self.cols = {
            logging.DEBUG: self.grey,
            logging.INFO: self.dark,
            logging.WARNING: self.yellow,
            logging.ERROR: self.red,
            logging.CRITICAL: self.bold_red
        }
 
    def format(self, record):
        if record.levelno == logging.INFO:
            msg = record.getMessage()
        else:
            msg = self.cols[record.levelno]+record.getMessage()+self.reset
       
        # Do process id...
        pid = str(threading.get_native_id()%100)
        
        # Do time...
        if self._last_time is None:
            time = "    "
        else:
            t = record.created - self._last_time
            if t < 10: time = f"{1000*t:4.0f}"
            elif t < 1000: time = f"{t:3.0f}s"
            elif t < 60000: time = f"{t/60:3.0f}m"
            elif t < 3600000: time = f"{t/3600:3.0f}h"
            else: time = f"{t/86400:3.0f}d"
        self._last_time = record.created
        
        #  Do memory...
        mem = psutil.Process().memory_info().rss
        def to_string(mem):
            for u in ['B', 'K', 'M', 'G', 'T']:
                if mem < 10: return f"{mem:3.1f}{u}"
                if mem < 1000: return f"{mem:3.0f}{u}"
                mem /= 1024.0
        mem = to_string(mem)

        #  Do origin...
        width = 8
        lno = f"{record.lineno}"
        w = width - len(lno) - 1
        mod = record.module[:w]
        mod += " "*(w-len(mod)) if len(mod) < w else ""
        ori = f"{mod}:{lno}"

        return self.grey+f"{pid}{ori} {mem}{time}: "+self.reset+msg
_pids = {}

def make_logger(name):
    log = logging.getLogger(name)
    log.setLevel(logging.DEBUG)
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    formatter = LogFormatter()
    ch.setFormatter(formatter)
    log.addHandler(ch)
    # monkey patch the logger to add the shortcut for info
    def info(self, msg, *args, **kwds):
        return self._log(logging.INFO, msg, args, stacklevel=2, **kwds)
    log.__class__.__call__ = info
    def intro():
        log.info(f"{appinfo}")
        log.info(f"  Starting log at {datetime.now()}")
    log.intro = intro
    return log

class redirect:
    def __init__(self, log: logging.Logger, fd: IO, fn: str = None):
        self.log = log
        self.fn = fn or f"{fd}"
        self.fd = fd
        self.handler = logging.StreamHandler(fd)
        self.handler.setLevel(logging.DEBUG)
        self.handler.setFormatter(LogFormatter(color=False))
    
    def __enter__(self):
        self.log.parent.addHandler(self.handler)
        self.log.info(f"{datetime.now()} Logging output to {self.fn}")
        self.log.intro()
        return self
    
    def __exit__(self, type, value, traceback):
        self.log.info(f"{datetime.now()} End of log to {self.fn}.")
        self.log.parent.removeHandler(self.handler)
        
#
#   Convenience formatting
#
def lform(l: Iterable, n=10, of_interest: str = None, sep: str = ", "):
    xs0 = l
    if of_interest is not None:
        xs0 = [x for x in l if of_interest in x]
    xs = []
    i = 0
    for i, x in enumerate(xs0):
        if i > n: break
        xs.append(str(x))
    ret = sep.join(xs)
    if i>n: ret += sep+"..."
    return f"({len(l)})[" + ret + "]"

def sform(s: str, n=75):
    if s is None: return "None"
    if len(s) > n: return s[:n-3]+"..."
    return s

def oform(o: any):
    if o is None: return "None"
    if isinstance(o, str): return f"'{o}'"
    if isinstance(o, datetime): return tform(o)
    if isinstance(o, Iterable): return lform(o)
    return f"{type(o)}({o})'"

#
#   Time formatting
#
def tform(t):
    if t is None: return None
    if t==datetime.min.replace(tzinfo=pytz.UTC) or t==datetime.min: return "anytime"
    if t==datetime.max.replace(tzinfo=pytz.UTC) or t==datetime.max: return "never"
    t = t.astimezone()
    now = datetime.now()
    if t.year == now.year:
        if t.month == now.month:
            if t.day == now.day: return t.strftime("Today %H:%M")
        return t.strftime("%b%d %H:%M")
    return t.strftime("%Y %b %d")

#
#   Some grammar related functions
#
def word_s(s: str, n: int = 0):
    """Convert word to plural."""
    if n==1: return s
    if s.endswith("um"): return s[:-2] + "a"
    if s.endswith("y"): return s[:-1] + "ies"
    if s.endswith("s") or s.endswith("ch") or s.endswith("sh"): return s + "es"
    return s + "s"

def word_ed(s: str):
    if s == "feed": return "fed"
    if s.endswith("e"):
        return s + "d"
    return s + "ed"

def word_ing(s: str):
    if re.match(r".*.[^aeiou]e$", s): 
        return s[:-1] + "ing"
    if re.match(r".*[^aeiou][aeiou][^aeiouy]$", s): 
        return s + s[-1] + "ing"
    return s + "ing"

def word_list(xs: list[str], item: str = None):
    xs = [str(x) for x in xs if x]
    wlist = xs[0]
    if len(xs) > 1: wlist = ', '.join(xs[:-1]) + " and " + xs[-1]
    if item is None: return wlist
    log(f"Word list: {wlist} {item}")
    return word_s(item, len(xs)) + ' ' + wlist

log = make_logger(__name__)

#
#  Some useful conversion functions
#
def camel2snake(s: str):
    return ''.join(['_' + c.lower() if c.isupper() else c for c in s]).lstrip('_')

#
#  Some useful decorators
#
class lazy(object):
    """A decorator for lazy properties."""
    def __init__(self, func, name=None, doc=None):
        self.__name__ = name or func.__name__
        self.__module__ = func.__module__
        self.__doc__ = doc or func.__doc__
        self.func = func

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            x = obj.__dict__[self.__name__]
        else:
            x = self.func(obj)
            obj.__dict__[self.__name__] = x
        return x

class Singleton(object):
    def __new__(cls, *args, **kwds):
        it = cls.__dict__.get("__it__")
        if it is not None:
            return it
        cls.__it__ = it = object.__new__(cls)
        it.init(*args, **kwds)
        return it
    def init(self, *args, **kwds):
        pass


#
#  Some representation functions for debugging
#
def show_model(x, m = None):
    if m is None: m = x.__class__
    log(f"{m.__name__}: {m} {m.__table__}")
    for c in m.__table__.columns:
        log(f"  {c.name} = {getattr(x, c.name)}")
    
        
def show_class(c):
    log(f"{c.__name__}: {c} {c.__annotations__}")
    for k, v in c.__annotations__.items():
        dcf = ""
        if hasattr(c, "__dataclass_fields__"):
            f = c.__dataclass_fields__[k]
            dcf = f"-- Field({f.name}: {f.type})"
        ct = v.__name__ not in ["int", "str", "enum"]
        log(f"  {k} {v} {ct} {dcf}")


