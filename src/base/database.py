import datetime
from typing import Literal
from sqlalchemy import create_engine, URL, DateTime, TypeDecorator
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from sqlalchemy.orm.session import Session
from app.config import settings
from base.util import lazy, make_logger
log = make_logger(__name__)


SQLALCHEMY_DATABASE_URL = URL.create(
    settings.DRIVERNAME,
    username=settings.DBUSER,
    password=settings.DBPASS,
    host=settings.DBHOST,
    port=settings.DBPORT,
    database=settings.DBNAME,
)

class DbConn:
    def __init__(self, loc: Literal["local", "dev"]):
        self.loc = loc

    @lazy
    def engine(self):
        log(f"ENGINE: {SQLALCHEMY_DATABASE_URL}")
        return create_engine(SQLALCHEMY_DATABASE_URL, echo=False)
    
    @lazy
    def session(self):
        return sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
db_conn = DbConn("local")

#SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class Base(DeclarativeBase):
    pass

class UtcDateTime(TypeDecorator):
    """Wrapper for timezone aware datetimes."""
    impl = DateTime
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            if not value.tzinfo or value.tzinfo.utcoffset(value) is None:
                raise TypeError("tzinfo is required")
            value = value.astimezone(datetime.timezone.utc).replace(tzinfo=None)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            value = value.replace(tzinfo=datetime.timezone.utc)
        return value

# Database singleton, mostly for scripts
global_db = None
def get_db():
    global global_db
    if global_db is None:
        global_db = db_conn.session()
    return global_db

def transact(db: Session):
    """Return a transaction context manager."""
    db.rollback()   # this is necessary to avoid a mysterious "existing transaction" error
    return db.begin()

def flush(db, o: Base):
    """Add an object to the database and refresh it to get at the id."""
    db.add(o)
    # get the new id...
    db.flush()
    db.refresh(o)
    
def populate_enums(db: Session):
    """If there are no options for an enum model, add them."""
    updated = 0
    enums = 0
    total = 0
    for mapper in Base.registry.mappers:
        cl = mapper.class_
        #log(f"Checking {cl}")
        total += 1
        if hasattr(cl, "__enum__"):
            #log(f"Checking {cl.__name__}")
            enums += 1
            xs = db.query(cl).all()
            present = set([x.name for x in xs])
            ns = []
            for v in cl.__enum__:
                if not isinstance(v, tuple): v = (v,)
                if v[0] not in present: ns.append(v[0])
            if len(ns)>0:
                log(f"Populating {cl.__name__}")
                try:
                    with transact(db):
                        for v in cl.__enum__:
                            if not isinstance(v, tuple): v = (v,)
                            if v[0] in present: continue
                            args = {c.name:v for c, v in zip(cl.__table__.columns, v)}
                            log(f"Adding {cl} {v}, {args}")
                            db.add(cl(**args))
                    xs = db.query(cl).all()
                    if len(xs) == 0:
                        raise ValueError(f"Failed to populate {cl.__name__}")
                    updated += 1
                except Exception as e:
                    log(f"Error populating {cl.__name__}: {e}")
                    db.rollback()
    log(f"Populated {updated}/{enums}/{total} enum models.")

#
#  Database utilities
#
def name_col(m): 
    if hasattr(m, "name"): return m.__name__+".name"
    elif hasattr(m, "code"): return m.__name__+".code"
    else: return m.__name__+".id"
    
def name_val(x):
    if hasattr(x.__class__, "name"): return x.name
    elif hasattr(x.__class__, "code"): return x.code
    else: return x.id

def key_col(m):
    """Get the primary key for a model."""
    if hasattr(m, "id"): return m.id
    else: return m.name
    
def key_val(x):
    """Get the primary key for a model instance."""
    if hasattr(x.__class__, "id"): return x.id
    else: return x.name

