from typing import IO
import os, io, pandas as pd
from datetime import datetime, timedelta, timezone
from urllib.request import urlopen
import zipfile
from google.cloud import storage as gcs
import imageio
from base.clients import storage
from app.config import settings
from base.util import make_logger
log = make_logger(__name__)

base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
#
#   Singletons for file storage
#
def cache_store():
    return file_store("cache", local="static")

def logs_store():
    return file_store("logs", local="static")

def pandas_delimiter(fn: str) -> str:
    """Set the delimiter for a pandas read_csv operation."""
    return "\t" if fn.endswith(".tsv") or fn.endswith(".tsv.gz") else ","

def diff_store():
    project_root = os.getcwd()
    current_dir = project_root

    while current_dir != os.path.dirname(current_dir):
        if os.path.exists(os.path.join(current_dir, "pyproject.toml")):
            project_root = current_dir
            break
        current_dir = os.path.dirname(current_dir)
    
    static_path = os.path.join(project_root, "static")
    return file_store("differentiation", local=static_path)


_stores = {}
def file_store(name: str, local: str = None, remote: str = None):
    global _stores
    if name not in _stores:
        if settings.DBHOST == "0.0.0.0" or settings.DBHOST == "localhost":
            if remote:
                _stores[name] = GcsStore("mellitos-"+remote)
            elif local:
                _stores[name] = FileStore(local+"/"+name)
            else:
                _stores[name] = FileStore(name)
        else:
            _stores[name] = GcsStore("mellitos-"+name)
    return _stores[name]


class File:
    """Convenience class to represent a file in a store"""
    def __init__(self, store, path):
        self.path = path
        self.store = store
    
    def __repr__(self):
        return f"{self.store.tag}{self.path}"
    
    def exists(self):
        return self.store.exists(self.path)
    
    def last_modified(self):
        return self.store.last_modified(self.path)
    
    def open(self, mode="r"):
        return self.store.open(self.path, mode)       
           
    def stream(self):
        return self.store.stream(self.path)       
           
    def delete(self):
        return self.store.delete(self.path)


class Store:
    def __repr__(self):
        return f"{self.tag}"

    def download(self, url: str, fn: str, mode="r") -> IO:
        """Download a file from a URL if it doesn't exist locally."""
        remote_file = os.path.join(url, fn)
        if not self.exists(fn):
            log(f"Downloading {url+fn} to {self.tag}{fn}")
            with urlopen(remote_file) as r:
                with self.open(fn, "wb") as f:
                    f.write(r.read())
        log(f"Opening {self.tag}{fn}")
        
        # unzip if necessary
        if fn.endswith(".zip"):
            fd = self.open(fn, "rb")
            archive = zipfile.ZipFile(fd, 'r')
            for tn in archive.namelist():
                log(f"  {tn}")
            fd = archive.open(tn, mode)
        else:
            fd = self.open(fn, mode)
        return fd
    
    def cached(self, fn: str, make, write, read, mode):
        """Return a cached object, computing it if necessary."""
        def dt(d: timedelta): return f"{d.microseconds/1000:.3f} ms"
        t0 = datetime.now()
        r = None
        if not self.exists(fn):
            t1 = datetime.now()
            x = make()
            t2 = datetime.now()

            # Write the data to the file
            write(self.open(fn, "w"+mode), x)
            t = datetime.now()
            r = f"Computed in {dt(t-t0)} (exists: {dt(t1-t0)}, compute: {dt(t2-t1)}, save: {dt(t-t2)}, load: {'{}'}) : {fn}"
        t3 = datetime.now()
        log(f"Loading {self.tag}{fn}")
        x = read(self.open(fn, "r"+mode))
        if not r: 
            r = f"Retrieved in {dt(datetime.now()-t0)} (exists: {dt(t3-t0)}, load: {'{}'}) : {self.tag}{fn}"
        log(r.format(dt(datetime.now()-t3)))
        return x
    
    def cached_df(self, fn: str, make_df):
        """Special case for dataframes"""
        return self.cached(fn, make_df, 
            lambda fd, df: df.to_csv(fd, index=False, sep=pandas_delimiter(fn)), 
            lambda fd: pd.read_csv(fd, sep=pandas_delimiter(fn)),
            ""
        )

            
#
#   GCS storage class
#
class GcsStore(Store):
    """A class for caching data in Google Cloud Storage."""
    def __init__(self, bucket, project="development", path: str = ""):
        if isinstance(bucket, str):
            bucket = storage(project).bucket(bucket)
        self.bucket = bucket
        self.project = project
        if path:
            path = path + "/"
        self.path = path
        self.name = self.bucket.name.replace("mellitos-", "")
        self.tag = f"gs://{self.bucket.name}/{path}"
    
    def __truediv__(self, sub: str):
        return GcsStore(self.bucket, self.project, self.path + sub)

    def cp_local(self, src: str, dest: str, fake: bool = False):
        log(f"Copy from {self.tag} to {dest}:") 
        if self.exists(src):
            n = 1
            fn = os.path.join(dest, os.path.basename(src))
            os.makedirs(os.path.dirname(fn), exist_ok=True)
            log(f"  {src} -> {fn}")
            with open(fn, "wb") as fd:
                self.bucket.blob(self.path+src).download_to_file(fd)
        else:
            blobs = self.bucket.list_blobs(prefix=self.path+src)  # Get list of files
            log(f"  {src} -> {dest}")
            n = 0
            for blob in blobs:
                n += 1
                fn = blob.name.replace(src, "")[1:]
                dn = os.path.join(dest, fn)
                log(f"    {n}: {fn} -> {dn}")
                os.makedirs(os.path.dirname(dn), exist_ok=True)
                if fake:
                    with open(dn, "w") as fd:
                        fd.write("Fake file")
                else:
                    blob.download_to_filename(dn)
        log(f"Copied {n} files.") 
                  
    def stream(self, fn: str) -> io.BytesIO:
        s = io.BytesIO()
        self.bucket.blob(self.path+fn).download_to_file(s)
        s.seek(0)
        return s
    
    def signed_url(self, fn: str) -> str:
        url = self.bucket.blob(self.path+fn).generate_signed_url(datetime.max)
        log(f"URL: {url}")
        return url
        
    def exists(self, fn: str) -> bool:
        #log(f"Checking {self.tag}{path}")
        return self.bucket.blob(self.path+fn).exists()
    
    def size(self, fn: str) -> int:
        size = self.bucket.get_blob(self.path + fn).size
        return size
    
    def last_modified(self, fn: str) -> datetime:
        if not self.exists(fn): return None
        blob = self.bucket.blob(self.path+fn)
        blob.reload()
        return blob.updated or blob.time_created
    
    def open(self, fn: str, mode="r"):
        if "w" in mode and "b" in mode:
            # Avoid gcloud.io error on flush
            return self.bucket.blob(self.path+fn).open(mode=mode, ignore_flush=True)
        else:  
            return self.bucket.blob(self.path+fn).open(mode=mode)
           
    def delete(self, fn: str):
        self.bucket.blob(self.path+fn).delete()
        
    def rename(self, src: str, dst: str):
        self.bucket.blob(self.path+src).rename(self.path+dst)

#
#   File storage class
#
class FileStore(Store):
    """A class for cahcing data on local disk."""
    def __init__(self, root = "", path = ""):
        if root and not root.endswith("/"): 
            root = root + "/"
        self.root = root
        if path and not path.endswith("/"): 
            path = path + "/"
        self.path = path
        self.name = root.split("/")[-2]
        self.tag = f"file://{root}{path}"
    
    def __truediv__(self, sub: str) -> "FileStore":
        return FileStore(self.root, self.path + sub)

    def _path(self, fn: str) -> str:
        p = self.root + self.path + fn
        if not p.startswith("/"):
            p = base_path + "/" + p
        return p
    
    def exists(self, fn: str) -> bool:
        return os.path.exists(self._path(fn))
        
    def size(self, fn: str) -> int:
        return os.path.getsize(self._path(fn))
        
    def last_modified(self, fn: str) -> datetime:
        if not self.exists(fn): return None
        return datetime.fromtimestamp(os.path.getmtime(self._path(fn)), tz=timezone.utc)
    
    def open(self, fn: str, mode="r") -> IO:
        if "w" in mode:
            os.makedirs(os.path.dirname(self._path(fn)), exist_ok=True)
        return open(self._path(fn), mode)
    
    def delete(self, fn: str):
        os.remove(self._path(fn))
        
    def rename(self, src: str, dst: str):
        os.rename(self._path(src), self._path(dst))

#
#  A file dependency decorator
#
def make(trg: str, *srcs: str):
    def decorator(func):
        return make_decorator(func, trg, srcs)
    return decorator

class make_decorator(object):
    """A decorator for cached data frames."""
    def __init__(self, func, trg: str, srcs: list[str]):
        self.__name__ = func.__name__
        self.__module__ = func.__module__
        self.__doc__ = func.__doc__
        self.func = func
        self.trg = trg
        self.srcs = srcs

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            return obj.__dict__[self.__name__]
        trg = File(obj.store, self.trg)
        srcs = [File(obj.store, src) for src in self.srcs]
        # Remove target if out of date
        if any([trg.last_modified() < src.last_modified() for src in srcs]):
            trg.delete()
        # Make target if necessary
        if not obj.store.exists(trg.path):
            self.func(obj, trg, *srcs)
        obj.__dict__[self.__name__] = trg
        return trg

#
#  A decorator for cached objects
#
class cached_decorator(object):
    """A decorator for cached data frames."""
    def __init__(self, func, fn: str, ext: str = None, cls = None):
        self.__name__ = func.__name__
        self.__module__ = func.__module__
        self.__doc__ = func.__doc__
        self.func = func
        self.fn = fn
        if not ext:
            ext = fn.split(".")[-1]
            if ext in ["Z", "gz"]:
                ext = fn.split(".")[-2]
        if cls is not None:
            self.writer = cls.writer
            self.reader = cls.reader
            self.mode = cls.mode
        elif ext.lower() in ["csv", "tsv"]:
            self.writer = lambda fd, df: df.to_csv(fd, index=False, sep=pandas_delimiter(fn))
            self.reader = lambda fd, **_: pd.read_csv(fd, sep=pandas_delimiter(fn))
            self.mode = ""
        elif ext.lower() in ["tif", "tiff"]:
            self.writer = lambda fd, img: imageio.v2.imwrite(fd, img, format="tiff")
            self.reader = lambda fd, **_: imageio.v2.imread(fd, format="tiff")
            self.mode = "b"

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            return obj.__dict__[self.__name__]
        fn = self.fn
        if "{}" in fn: 
            fn = self.fn.format(obj.file_name)
        store = cache_store() if not hasattr(obj, "store") else obj.store
        # pass environment to the reader for more flexible construction
        def reader(fd):
            return self.reader(fd, deco=self, owner=obj)
        x = store.cached(fn, lambda: self.func(obj), self.writer, reader, self.mode)
        obj.__dict__[self.__name__] = x
        return x

def cached(fn: str, cls = None):
    def decorator(func):
        return cached_decorator(func, fn, cls=cls)
    return decorator

#
#  A decorator for cached dataframes (special case of `cached`, for back compatibility)
#
def cached_df(fn: str):
    def decorator(func):
        return cached_decorator(func, fn, "csv")
    return decorator


