from typing import NamedTuple
from datetime import datetime
import json

#
#   Read and write JSON files from NamedTuples
#
def json_serial(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")    
    
def encode(t: NamedTuple) -> str:
    return json.dumps(t._asdict(), default = json_serial)

def decode(cls: type, s: str) -> NamedTuple:
    return cls(**json.loads(s))