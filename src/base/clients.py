#
#  A bunch of client singletons for the Google Cloud services
#
from google.cloud import storage as _storage, bigquery as _bigquery, secretmanager as _secretmanager
from google.auth import load_credentials_from_file
from google.auth.exceptions import DefaultCredentialsError

from app.config import get_secret
from base.util import make_logger
log = make_logger(__name__)

_storage_clients = {}

def storage(name="development"):
    global _storage_clients
    sc = _storage_clients.get(name)
    if sc is None:
        fn = get_secret(f"{name}.json")
        log(f"Loading credentials from {fn}")
        #cred = load_credentials_from_file(fn)
        #log(f"Credentials: {cred}")
        #sc = _storage.Client(credentials=cred)
        sc = _storage.Client.from_service_account_json(fn)

        _storage_clients[name] = sc
    return sc

_bigquery_client = None

def bigquery():
    global _bigquery_client
    if _bigquery_client is None:
        fn = get_secret(f"development.json")
        log(f"Loading credentials from {fn}")
        _bigquery_client = _bigquery.Client.from_service_account_json(fn)
        #_bigquery_client = _bigquery.Client()
    return _bigquery_client

_secretmanager_client = None

def secretmanager():
    global _secretmanager_client
    if _secretmanager_client is None:
        _secretmanager_client = _secretmanager.SecretManagerServiceClient()
    return _secretmanager_client

