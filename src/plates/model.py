import string
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey, func
from typing import List, Optional
from datetime import datetime
import enum
from base.database import Base, UtcDateTime

def get_well_name(row: int, col: int):
    return string.ascii_uppercase[row] + str(col + 1)

#
#  Plates
#
class PlateType(Base):
    __enum__ = ["culture", "supernatant", "dilution", "assay"]
    __tablename__ = "plate_type"
    name: Mapped[str] = mapped_column(primary_key=True)

class PlateFormat(Base):
    __enum__ = [("384-well", 384, 16, 24), ("96-well", 96, 8, 12), ("12-well", 12, 3, 4), ("6-well", 6, 2, 3)]
    __tablename__ = "plate_format"
    name: Mapped[str] = mapped_column(primary_key=True)
    well_count: Mapped[int] = mapped_column(nullable=False)
    row_count: Mapped[int]
    col_count: Mapped[int]

    wells: Mapped[List["WellPos"]] = relationship("WellPos")

class WellPos(Base):
    __tablename__ = "well_pos"
    id: Mapped[int] = mapped_column(primary_key=True)
    well_name: Mapped[str] = mapped_column(nullable=False)
    plate_format: Mapped[str] = mapped_column(ForeignKey("plate_format.name"), nullable=False)
    row: Mapped[int] = mapped_column(nullable=False)
    col: Mapped[int] = mapped_column(nullable=False)

    #plate_format: Mapped["PlateFormat"] = relationship("PlateFormat", back_populates="wells")

class Quadrant(Base):
    __tablename__ = "quadrant"
    __enum__ = ["A1", "A2", "B1", "B2", "All"]
    name: Mapped[str] = mapped_column(primary_key=True)
    
class Vendor(Base):
    __enum__ = [
        ("PromoCell", "PromoCell GmbH, Heidelberg, Germany", "donor"), 
        ("OB", "OB", "donor"), 
        ("ZB", "ZB", "donor"),
        ("4DCell", "4DCell, Montreuil, France", "plate"),
        ("Corning", "Corning Inc., Corning, NY", "plate"),
    ]
    __tablename__ = "vendor"
    name: Mapped[str] = mapped_column(unique=True, primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)
    type: Mapped[str] = mapped_column(nullable=False)

class Plate(Base):
    """Base table for all microtiter plates."""
    __tablename__ = "plate"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(unique=True, nullable=False)
    format: Mapped[str] = mapped_column(ForeignKey("plate_format.name"), nullable=False)
    type: Mapped[str] = mapped_column(ForeignKey("plate_type.name"), nullable=False)
    vendor: Mapped[str] = mapped_column(ForeignKey("vendor.name"), nullable=False)
    barcode: Mapped[int] = mapped_column(unique=True, nullable=True)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())

    plate_format: Mapped[PlateFormat] = relationship(PlateFormat)
    
    def __repr__(self):
        return f"Plate({self.id}, {self.name}, {self.format}, {self.type}, {self.vendor}, {self.barcode})"
    
class Supernatant(Base):
    """Supernatant from a culture plate. (type='supernatant')"""
    __tablename__ = "supernatant"
    id: Mapped[int] = mapped_column(ForeignKey("plate.id"), primary_key=True)
    source: Mapped[int] = mapped_column(ForeignKey("plate.id"))

class Dilution(Base):
    """Dilution from a supernatant plate. (type='dilution')"""
    __tablename__ = "dilution"
    id: Mapped[int] = mapped_column(ForeignKey("plate.id"), primary_key=True)
    factor: Mapped[float] = mapped_column(nullable=False)
    source: Mapped[int] = mapped_column(ForeignKey("plate.id"))

class Assay(Base):
    __tablename__ = "assay"
    __enum__ = [("viability", "Assess the viability of cells"),
                ("lipolysis", "Assess the lipolysis of cells"),
                ("mitotracker", "Assess the mitochndria of cells"),
                ("glucose uptake", "Assess the glucose uptake of cells"),
                ]
    name: Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())
    
class Medium(Base):
    __tablename__ = "medium"
    __enum__ = [("standard", "Standard culture medium")]
    name: Mapped[str] = mapped_column(unique=True, primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)

class Gender(Base):
    __tablename__ = "gender"
    __enum__ = ["male", "female"]
    name: Mapped[str] = mapped_column(primary_key=True)

class Race(Base):
    __tablename__ = "race"
    __enum__ = ["African", "Asian", "Caucasian"]
    name: Mapped[str] = mapped_column(primary_key=True)
    
class Donor(Base):
    __tablename__ = "donor"
    id: Mapped[int] = mapped_column(primary_key=True)
    code: Mapped[str] = mapped_column(unique=True)
    description: Mapped[str] = mapped_column(nullable=False)
    biopsy_site: Mapped[str] = mapped_column(nullable=True)
    documentation: Mapped[Optional[str]]
    vendor: Mapped[str] = mapped_column(ForeignKey("vendor.name"), nullable=False)
    catno: Mapped[str] = mapped_column(nullable=True)
    lotno: Mapped[str] = mapped_column(nullable=False)
    gender: Mapped[str] = mapped_column(ForeignKey("gender.name"))
    age: Mapped[int] = mapped_column(nullable=False)
    race: Mapped[str] = mapped_column(ForeignKey("race.name"), nullable=False)
    bmi: Mapped[float] = mapped_column(nullable=True)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())


class TreatmentType(Base):
    __tablename__ = "treatment_type"
    __enum__ = ["small_molecule", "sirna"]
    name: Mapped[str] = mapped_column(primary_key=True)

class Treatment(Base):
    __tablename__ = "treatment"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(unique=True)
    mcl_id: Mapped[Optional[str]] = mapped_column(unique=True)
    type: Mapped[str] = mapped_column(ForeignKey("treatment_type.name"))


class GroupType(Base):
    __tablename__ = "group_type"
    __enum__ = ["positive", "negative", "experimental", "standard"]
    name: Mapped[str] = mapped_column(primary_key=True)


class ConcentrationUnit(Base):
    __tablename__ = "concentration_unit"
    __enum__ = ["uM", "nM"]
    name: Mapped[str] = mapped_column(primary_key=True)

class Dose(Base):
    __tablename__ = "dose"
    id: Mapped[int] = mapped_column(primary_key=True)
    well_pos_id: Mapped[int] = mapped_column(ForeignKey("well_pos.id"), nullable=False)
    map_group_id: Mapped[int] = mapped_column(ForeignKey("map_group.id"), nullable=False)
    concentration__value: Mapped[float]
    concentration__unit: Mapped[str] = mapped_column(ForeignKey("concentration_unit.name"), nullable=False)

    well_pos: Mapped[WellPos] = relationship(WellPos, uselist=False)

class MapGroup(Base):
    __tablename__ = "map_group"
    id: Mapped[int] = mapped_column(primary_key=True)
    plate_map_id: Mapped[int] = mapped_column(ForeignKey("plate_map.id"), nullable=False)
    group_type: Mapped[str] = mapped_column(ForeignKey("group_type.name"), nullable=False)

    doses: Mapped[List[Dose]] = relationship(Dose)

class PlateMap(Base):
    __tablename__ = "plate_map"
    id: Mapped[int] = mapped_column(primary_key=True)
    format_name: Mapped[str] = mapped_column(ForeignKey("plate_format.name"), nullable=False)
    
    name: Mapped[str] = mapped_column(unique=True, nullable=True)
    description: Mapped[str] = mapped_column(nullable=True)
    type: Mapped[str] = mapped_column(ForeignKey("plate_map_type.name"), nullable=True)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False, default=func.now())
    
    plate_format: Mapped[PlateFormat] = relationship(PlateFormat)
    map_groups: Mapped[List[MapGroup]] = relationship(MapGroup)
    
    def __repr__(self):
        return f"PlateMap({self.id}, {self.name}, {self.description}, {self.format_name}, {self.type})"

class PlateMapType(Base):
    __tablename__ = "plate_map_type"
    # Types should be gerunds so 'plate map for x' makes sense
    __enum__ = ["dosing", "assay", "screening", "seeding"]
    name: Mapped[str] = mapped_column(primary_key=True)

class GroupAssignment(Base):
    __tablename__ = "plate_map_assignment"
    id: Mapped[int] = mapped_column(primary_key=True)
    map_group_id: Mapped[int] = mapped_column(ForeignKey("map_group.id"), nullable=False)
    treatment_id: Mapped[int] = mapped_column(ForeignKey("treatment.id"), nullable=False)
    treatment_regiment_id: Mapped[int] = mapped_column(ForeignKey("treatment_regiment.id"))

    treatment: Mapped[Treatment] = relationship(Treatment)
    map_group: Mapped[MapGroup] = relationship(MapGroup)

class TreatmentBatch(Base):
    __tablename__ = "treatment_regiment"
    id: Mapped[int] = mapped_column(primary_key=True)
    plate_id: Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    plate_map_id: Mapped[int] = mapped_column(ForeignKey("plate_map.id"), nullable=False)

    treatment_assignments: Mapped[List[GroupAssignment]]= relationship(GroupAssignment)
    plate: Mapped[Plate] = relationship(Plate)
    plate_map: Mapped[PlateMap] = relationship(PlateMap)
