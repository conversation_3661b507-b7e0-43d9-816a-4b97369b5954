from app.cruds import crud
from app.layouts import Context
from app import html
from app.table import table
from app.auth import context
from fastapi import APIRouter, Depends
from plates.model import Donor, PlateFormat, Quadrant, Vendor, Plate, Medium, TreatmentBatch, PlateMap #, SupernatentPlate
from fastapi import APIRouter, Form, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse, Response
from fastapi.requests import Request
import plates.commands as commands
from app import html
from plates import handlers as dosing
from plates import render
from app.auth import context
from app.layouts import Context
from plates import model as models
from functools import partial
from sqlalchemy.orm import Session
from typing import Annotated, List, Dict, Any
from datetime import datetime, timezone
from base.util import make_logger
log = make_logger(__name__)

router = APIRouter(prefix="/plates")
ROOT_URL = "/plates"

# Set up crud routes
cruds = [
    crud("donors", Donor, router),
    crud("vendors", Vendor, router),
    crud("plates", Plate, router),
    crud("platemaps", PlateMap, router),
    crud("plateformats", PlateFormat, router),
    crud("media", Medium, router),
    crud("quadrant", Quadrant, router),
]


@router.get("/treatment_batches/", response_class=HTMLResponse)
def list_treatment_batches(ht: Context = Depends(context)):
    treatment_batches = ht.db.query(models.TreatmentBatch).all()
    list_items = []
    for tb in treatment_batches:
        link = html.node("a", href=f"{ROOT_URL}/treatment_batches/{tb.id}")("excel sheet")
        li = html.node("li")(
            html.node("div")(
                html.node("p")(f"{tb.plate.name}: {link.pretty()}")
            )
        )
        list_items.append(li)
    
    body = html.node("body")(
        html.node("h1")("list of treatment dose assignments"),
        html.node("ul")(
            *list_items
        )
    )  
    return ht.page("plate treatments", body).pretty()

@router.get("/treatment_batches/{treatment_batch_id}")
def download_excel(treatment_batch_id: int, ht: Context = Depends(context)):
    treatment_batch = ht.db.query(models.TreatmentBatch).filter_by(id=treatment_batch_id).first()
    excel_file = render.render_treatment_map_xlsx(treatment_batch)
    plate_name = treatment_batch.plate.name
    headers = {
        # By adding this, browsers can download this file.
        'Content-Disposition': f'attachment; filename=plate{plate_name}_treatment.xlsx',
        # Needed by our client readers, for CORS (cross origin resource sharing).
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
        "Access-Control_Allow-Methods": "POST, GET, OPTIONS",
    }
    media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    return Response(
        content=excel_file,
        headers=headers,
        media_type=media_type
    )

@router.get("/assign/{plate_map_id}")
def assign_plate_map_render(plate_map_id: int, ht: Context = Depends(context)):
    plate_map = ht.db.query(models.PlateMap).filter_by(id=plate_map_id).first()
    fields = []
    fields.append(
        html.node("div")(
            html.node("label", **{"for":"plate"})("plate"),
            html.node("input", type="text", name="plate", id="plate")()
        )
    )
    for i, group in enumerate(plate_map.map_groups):
        fields.append(
            html.node("div")(
                html.node("label", **{"for":f"group-{group.id}"})(f"group {i} - {group.group_type} treatment"),
                html.node("input", type="text", name=f"group-{group.id}", id=f"group-{group.id}", **{"class":"treatment-select"})()
            )
        )
    
    fields.append(html.node("input", type="submit", value="submit")("submit"))
    form = html.node("form", method="post")(*fields)
    
    body = html.node("body")(
        html.node("h1")("assign treatments to experiment groups"),
        form,
        html.node("script", src="/static/js/searchSelectTreatments.js")()
    )
    return HTMLResponse(
        ht.page("assign treatment", body).pretty()
    )

        
@router.post("/assign/{plate_map_id}")
async def assign_plate_map_post(plate_map_id: int, request: Request, ht: Context = Depends(context)):
    log(plate_map_id)
    plate_map = ht.db.query(models.PlateMap).filter_by(id=plate_map_id).first()
    form_data = await request.form()
    experimental_treatments = []
    for field in form_data.items():
        if field[0] == 'plate':
            plate = ht.db.query(models.Plate).filter_by(name=field[1]).first()
        elif "group" in field[0]:
            group_id = int(field[0].replace('group-', ''))
            group = ht.db.query(models.MapGroup).filter_by(id=group_id).first()
            log(group.group_type)
            treatment = ht.db.query(models.Treatment).filter_by(name=str(field[1])).first()
            if group.group_type == 'negative':
                negative_treatment = treatment
            elif group.group_type == 'positive':
                positive_treatment = treatment
            else:
                experimental_treatments.append(treatment)

                
    command = commands.AssignMap(
        plate_map=plate_map,
        experimental_treatments=experimental_treatments,
        negative_treatment=negative_treatment,
        positive_treatment=positive_treatment,
        plate=plate
    )
    plate_map_assignment = dosing.create_treatment_batch(command)
    ht.db.add(plate_map_assignment)
    ht.db.commit()
    return RedirectResponse(f'{ROOT_URL}/treatment_batches/', status_code=status.HTTP_302_FOUND)
    

@router.get("/", response_class=HTMLResponse)
def list_plate_maps(ht: Context = Depends(context)):
    plate_maps = ht.db.query(models.PlateMap).all()

    body = html.node("body")(
        html.node("h1")("map layouts"),
        html.node("h3")("click to assign to a culture plate"),
        html.node("div", **{"class":"plate-collection"})(
            *[
                html.node('div', 
                    onclick=f"location.href='{ROOT_URL}/assign/{pm.id}';", 
                    **{"class":"plate-container"}
                )(
                    render.render_map2(pm)
                ).pretty() 
            for pm in plate_maps ]
        ),
        html.node("a", href=f"{ROOT_URL}/new")("create new dosing template")
    )

    return ht.page("plate templates", body).pretty()


@router.get('/new', response_class=HTMLResponse)
def new_plate_map_render(ht: Context = Depends(context)):
    new_plate_map_form = html.node("form", method="post")(
        html.node("div")(
            html.node("label", **{"for":"format-select"})("plate format"),
            html.node("select", name="format", id="format-select")(
                html.node("option", value="384-well")("384-well"),
                html.node("option", value="96-well")("96-well"),
                html.node("option", value="12-well")("12-well"),
                html.node("option", value="6-well")("6-well"),
            )
        ),
        html.node("div")(
            html.node("label", **{"for":"group-count"})("experimental group count"),
            html.node("input", name="groups", id="group-count", type="number", min="1", step="1")()
        ),
        html.node("div")(
            html.node("label", **{"for":"replicate-count"})("replicate count"),
            html.node("input", name="replicates", id="replicate-count", type="number", min="1", step="1")()
        ),
        html.node("div")(
            html.node("label", **{"for":"unit-concentration"})("unit of concentration"),
            html.node("select", name="unit", id="unit-concentration")(
                html.node("option", value="uM")("uM"),
                html.node("option", value="nM")("nM")
            )
        ),
        html.node("div")(
            html.node("label", **{"for":"value-concentration"})("concentration values"),
            html.node("input", name="concentrations", type="text", placeholder="10, 3, 1, 0.5, 0.3...")()
        ),
        html.node("div")(
            html.node("label", **{"for":"plate-orientation"})("dosing orientation"),
            html.node("select", name="orientation", id="plate-orientation")(
                html.node("option", value="H")("horizontal"),
                html.node("option", value="V")("vertical")
            )
        ),
        html.node("div")(
            html.node("input", type="submit", value="submit")()
        )
    )
    
    body = html.node("body")(
        new_plate_map_form
    )
    return ht.page("new template", body).pretty()


import starlette.status as status
@router.post("/new")
def new_plate_map_post(
    format: Annotated[str, Form()],
    groups: Annotated[int, Form()],
    replicates: Annotated[int, Form()],
    unit: Annotated[str, Form()],
    concentrations: Annotated[str, Form()],
    orientation: Annotated[str, Form()],
    ht: Context = Depends(context)
):
    concentration_values = [float(c.strip()) for c in concentrations.split(",")]
    plate_format = ht.db.query(models.PlateFormat).filter_by(name=format).first()
    command = commands.CreateMap(
        plate_format=plate_format,
        num_experimental_groups=groups,
        num_replicates=replicates,
        orientation=orientation,
        concentration_unit=unit,
        concentration_list=concentration_values
    )
    plate_map = dosing.create_dose_response_map(command)
    ht.db.add(plate_map)
    ht.db.commit()
    return RedirectResponse(f"{ROOT_URL}/", status_code=status.HTTP_302_FOUND)


# @router.get("/transfers/", response_class=HTMLResponse)
# def list_transfer_plates(ht: Context = Depends(context)):
#     supernatent_plates = ht.db.query(SupernatentPlate).all()
#     return ht.page("supernatent plates", table(supernatent_plates)).pretty()
    

# @router.get("/transfers/new", response_class=HTMLResponse)
# def transfer_plate_form(ht: Context = Depends(context)):
#     ht.db.query(Plate).all()
#     body = html.node("body")(
#         html.node("h2")("select plate to extract supernatent"),
#         html.node("form", **{"method":"POST"})(
#             html.node("input", **{"id":"plate", "name":"plate", "type":"text"})(),
#             html.node("input", **{"type":"submit", "value":"Submit"})()
#         ),
#         html.node("script", **{"src":"/static/js/searchSelectPlate.js"})
#     )
#     return ht.page("supernatent collection", body).pretty()
    
# """class SupernatentPlate(Base):
#     __tablename__ = "supernatent_plate"
#     id: Mapped[int] = mapped_column(primary_key=True)
#     name: Mapped[str] = mapped_column(unique=True)
#     barcode: Mapped[int] = mapped_column(unique=True, nullable=True)
#     created: Mapped[datetime]
#     source_plate: Mapped[int] = mapped_column(ForeignKey("plate.id"))"""
    
# @router.post("/transfers/new")
# async def create_supernatent_plate(request: Request, ht: Context = Depends(context)):
#     async with request.form() as form:
#         plate_name = form["plate"]
        
#     plate = ht.db.query(Plate).filter_by(name=plate_name).first()
#     supernatent_plates = ht.db.query(SupernatentPlate).filter_by(source_plate=plate.id).all()
#     count = len(supernatent_plates)
#     supernatent_plate = SupernatentPlate()
#     supernatent_plate.name = plate.name + f"_S{count+1}"
#     supernatent_plate.created = datetime.now(tz=timezone.utc)
#     supernatent_plate.source_plate = plate.id
#     ht.db.add(supernatent_plate)
#     ht.db.commit()

#     return RedirectResponse("/plates/transfers/", status_code=302)
    

    
