from typing import Callable
from app.layouts import Context
from plates.model import Dose, MapGroup, PlateMap, WellPos, TreatmentBatch, PlateFormat
from app import html
from app.html import node, Html
import string
import random
from io import BytesIO
from typing import Tuple
from base.util import make_logger
log = make_logger(__name__)


import xlsxwriter

def rbg_to_hex(colortuple: Tuple[int, int, int]):
    return '#' + ''.join(f'{i:02X}' for i in colortuple)


def render_treatment_map_xlsx(treatment_batch: TreatmentBatch):
    fs = BytesIO()
    wb = xlsxwriter.Workbook(fs)
    sheet = wb.add_worksheet("Sheet1")

    literal_map = []
    treatment_group_map = {}
    format = treatment_batch.plate_map.plate_format
    for assignment in treatment_batch.treatment_assignments:
        treatment = assignment.treatment
        group = assignment.map_group
        treatment_group_map[group.id] = (treatment.name, group.group_type)
        for dose in group.doses:
            well_pos = dose.well_pos
            literal_map.append(
                {
                    "row":well_pos.row,
                    "col":well_pos.col,
                    "unit":dose.concentration__unit,
                    "concentration":dose.concentration__value,
                    "treatment":treatment.name,
                    "group_id":group.id,
                    "control":group.group_type
                }
            )

    color_treatment_map = {}
    for row in range(format.row_count):
        for col in range(format.col_count):

            try:
                well = next(w for w in literal_map if w['row'] == row and w['col'] == col)
            except:
                well = {
                    "row":row,
                    "col":col,
                    "unit":"uM",
                    "concentration":0,
                    "treatment":"",
                    "group_id":0,
                    "control":None
                }
            if well["group_id"] not in color_treatment_map:

                style = wb.add_format()
                color = tuple(random.choices(range(256), k=3)) if well["group_id"] > 0 else (255, 255, 255)
                style.set_bg_color(rbg_to_hex(color))
                color_treatment_map[well["group_id"]] = style
                
            try:
                sheet.write(0, col + 1, str(col + 1))
            except:
                pass
            try:
                sheet.write(row + 1, 0, string.ascii_uppercase[row] if format.well_count <= 384 else string.ascii_uppercase[row//26] + string.ascii_uppercase[row%26])
            except:
                pass
            try:
                value = f'{well["concentration"]:.3f}{well["unit"]}'
                sheet.write(row + 1, col + 1, value, color_treatment_map[well['group_id']])
                #sheet.col(col + 1).width = 256 * 10
                #sheet.row(row + 1).height = 256 * 2
            except:
                log(f"error {row} {col}")
    
    row += 4
    sheet.write(row, 0, "treatments:")
    for group in treatment_group_map:
        row += 1
        treatment, group_type = treatment_group_map[group]
        sheet.write(row, 0, group_type, color_treatment_map[group])
        sheet.write(row, 1, treatment, color_treatment_map[group])

    wb.close()
    return fs.getvalue()
            

# Maps without labels (deprecate!)
class_dict = {
    "384-well":"wells384",
    "96-well":"wells96",
    "12-well":"wells12",
    "6-well":"wells6"
}

# Maps with labels (see style.css)
class_dict2 = {
    "384-well":"wells-384",
    "96-well":"wells-96",
    "12-well":"wells-12",
    "6-well":"wells-6"
}

def render_map2(plate_map: PlateMap):
    
    literal_map = []
    group_count = 0
    for group in plate_map.map_groups:
        group_count += 1
        for dose in group.doses:
            well = dose.well_pos
            literal_map.append(
                {
                    "well_name":well.well_name,
                    "row":well.row,
                    "col":well.col, 
                    "group":group_count, 
                    "group_type":group.group_type,
                    "concentration":dose.concentration__value
                } 
            )
            
            
    row_count = None
    col_count = None
    if plate_map.format_name == '384-well':
        row_count = 16
        col_count = 24
    elif plate_map.format_name == '96-well':
        row_count = 8
        col_count = 12
    elif plate_map.format_name == '12-well':
        row_count = 3
        col_count = 4
    elif plate_map.format_name == '6-well':
        row_count = 2
        col_count = 3
    
    rows = []    
    for row in range(row_count):
        wells_in_row = [w for w in literal_map if w['row'] == row]
        if wells_in_row:
            wells_in_row.sort(key=lambda x: x['col'])
            
            completed_row = []
            c = 0
            for w in wells_in_row:
                while c < w['col']:
                    completed_row.append(
                        {
                            "well_name":"NA",
                            "row":row,
                            "col":c, 
                            "group":0, 
                            "group_type":"ex",
                            "concentration":0.00
                        }
                    )
                    c += 1
                completed_row.append(w)
                c += 1
            while c > w['col'] and c < col_count:
                completed_row.append(
                    {
                        "well_name":"NA",
                        "row":row,
                        "col":c, 
                        "group":0, 
                        "group_type":"ex",
                        "concentration":0.00
                    }
                )
                c += 1
                            
            
            
            row = html.node("div", **{"class":f"well-row-{plate_map.format_name}"})(
                *[html.node("div", **{"class":f"well group{well['group']}", "title":f"{well['concentration']}uM"})(html.node("span", **{"class":"concentration-text"})(f"{well['concentration']:.2f}uM")) for well in completed_row]
            )
        else:
            row = html.node("div", **{"class":f"well-row-{plate_map.format_name}"})(
                *[html.node("div", **{"class":f"well group0", "title":"0.00uM"})(html.node("span", **{"class":"concentration-text"})(f"0.00uM")) for _ in range(col_count)]
            )
        rows.append(row)
    
    return html.node("div", **{"class":f'plate {class_dict[plate_map.format_name]}'})(
        *rows
    )
            
        

def render_map(plate_map: PlateMap):
    
    literal_map = []
    group_count = 0
    for group in plate_map.map_groups:
        group_count += 1
        for dose in group.doses:
            well = dose.well_pos
            literal_map.append(
                {
                    "well_name":well.well_name,
                    "row":well.row,
                    "col":well.col, 
                    "group":group_count, 
                    "group_type":group.group_type,
                    "concentration":dose.concentration__value
                } 
            )
    plate_map_literal = []        
    for row in range(plate_map.plate_format.row_count):
        for col in range(plate_map.plate_format.col_count):
            try:
                well = next(w for w in literal_map if w['row'] == row and w['col'] == col)
            except:
                well = {
                    "well_name":
                        string.ascii_uppercase[row] + str(col+1) 
                            if plate_map.plate_format.well_count <= 384 
                            else string.ascii_uppercase[row//26] + string.ascii_uppercase[row%26] + str(col+1).zfill(2),
                    "row":row,
                    "col":col,
                    "group":"0",
                    "group_type":"none",
                    "concentration":0
                }
            plate_map_literal.append(well)
        

    
    return html.node("div", **{"class":f'plate {class_dict[plate_map.format_name]}'})(
        *[
            html.node("div", **{"class":f"well group{well['group']}"})(
                html.node("p", **{"class":"concentration-text"})(
                    f"{well['concentration']:.2f}uM"
            )) 
        for well in plate_map_literal ]
    )
    
letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"


def plate_map(ht: Context, plate_format: str, well: Callable[[WellPos], Html]):
    """Render a plate map for a given plate format"""
    
    # Get the plate format and the well positions
    pf = ht.db.query(PlateFormat).get(plate_format)
    wps = ht.db.query(WellPos).filter(WellPos.plate_format==pf.name).all()
    
    # Enumerate the well positions in row by row order (don't rely on query order)
    wp = [None] * (pf.row_count * pf.col_count)
    for w in wps:
        j = w.row * pf.col_count + w.col
        wp[j] = w
        
    # How to render the row and column labels
    def label(x):
        return ht.div(_class="well2 label")(x)
    
    # column labels
    wells = [label("")] + [label(f"{i+1}") for i in range(pf.col_count)]
    i = 0
    for row in range(pf.row_count):
        # row label
        rn = letters[row] if pf.row_count <= 26 else letters[row//26] + letters[row%26]
        wells.append(label(rn))
        # Now render the actual wells
        for col in range(pf.col_count):
            wells.append(well(wp[i])(_class="well2"))
            i += 1
    # Wrap it in the plate div
    return ht.div(_class=f"plate2 {class_dict2[pf.name]}")(*wells)

control_symbol = {
    "positive": "+",
    "negative": "-",
    "experimental": "X",
    "standard": "S"
}

def plate_view(ht: Context, pm: PlateMap):    
    ds = ht.db.query(WellPos, Dose, MapGroup).\
        filter(MapGroup.plate_map_id==pm.id).\
        filter(Dose.map_group_id==MapGroup.id).\
        filter(Dose.well_pos_id==WellPos.id).\
    all()
    dss = {wp.well_name: (d, g) for wp, d, g in ds}
    egs = [g for g in pm.map_groups if g.group_type == "experimental"]
    gix = {g.id: i+1 for i, g in enumerate(egs)}
    
    def well(wp: WellPos):
        d, g = dss.get(wp.well_name, (None, None))
        t = f"{wp.well_name} ({wp.row} {wp.col}): "
        if d is None: 
            return ht.div(title=t+"Unmapped")(" ")
        t += f"Group {gix[g.id]}"
        gt = control_symbol[g.group_type]
        if g.group_type == "experimental":
            gt = gix[g.id]
        return ht.div(title=t)(gt) 
    
    t = pm.name
    if pm.name is None: t = "unnamed"
    if pm.description is not None: t = t + " - "+pm.description

    return \
        ht.h3(f"Plate map {pm.id} ({pm.format_name}): {t}") + \
        ht.p("Hover over a well to see more information.") + \
        plate_map(ht, pm.format_name, well)


    
   
                 
    
