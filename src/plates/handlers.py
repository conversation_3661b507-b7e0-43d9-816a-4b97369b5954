from plates.model import Treatment, Plate, GroupAssignment, MapGroup, TreatmentBatch, PlateMap, Dose, PlateFormat #, SupernatentPlate
from typing import List
from plates.commands import AssignMap, CreateMap, GenerateConcentrations, ExtractSupernatent
from base.util import make_logger
log = make_logger(__name__)


def create_treatment_batch(
        assign_map_command: AssignMap
    ):
    plate_map = assign_map_command.plate_map
    experimental_treatments = assign_map_command.experimental_treatments
    negative_treatment = assign_map_command.negative_treatment
    positive_treatment = assign_map_command.positive_treatment
    plate = assign_map_command.plate

    if plate_map.format_name != plate.format:
        raise Exception("culture plate and plate map need to be the same format")

    regiment = TreatmentBatch() 
    regiment.plate = plate

    if len([g for g in plate_map.map_groups]) != len(experimental_treatments) + 2:
        raise Exception(f"{len(plate_map.map_groups)} number of treatments and {len(experimental_treatments) + 2} treatment groups do not match")
    
    negative_group = next(g for g in plate_map.map_groups if g.group_type == "negative")
    positive_group = next(g for g in plate_map.map_groups if g.group_type == "positive")
    negative_assign = GroupAssignment()
    negative_assign.map_group = negative_group
    negative_assign.treatment = negative_treatment
    regiment.treatment_assignments.append(negative_assign)
    positive_assign = GroupAssignment()
    positive_assign.treatment = positive_treatment
    positive_assign.map_group = positive_group
    regiment.treatment_assignments.append(positive_assign)
    for treatment, group in zip(experimental_treatments, [g for g in plate_map.map_groups if g.group_type == "experimental"]):
        experimental_assign = GroupAssignment()
        experimental_assign.treatment = treatment
        experimental_assign.map_group=group
        regiment.treatment_assignments.append(
            experimental_assign
        )
    regiment.plate_map = plate_map
    return regiment

def create_dose_response_map(
        create_map_command: CreateMap
):
    plate_format = create_map_command.plate_format
    num_experimental_groups = create_map_command.num_experimental_groups
    num_replicates = create_map_command.num_replicates
    concentration_unit = create_map_command.concentration_unit
    concentration_list = create_map_command.concentration_list
    orientation = create_map_command.orientation

    group_size = len(concentration_list) * num_replicates
    num_groups = num_experimental_groups + 2 # always 2 control groups
    well_requirement = group_size * num_groups
    
    if well_requirement > plate_format.well_count:
        raise Exception(f"paremeters require {well_requirement} wells but there are only {plate_format.well_count}")
    elif num_groups > (plate_format.row_count if orientation == 'H' else plate_format.col_count):
        raise Exception(f'{plate_format.row_count if orientation == "H" else plate_format.col_count} {"rows" if orientation == "H" else "columns"} cannot accomidate {num_groups} groups.')
    elif len(concentration_list) > (plate_format.row_count if orientation == 'V' else plate_format.col_count):
        raise Exception(f"{ plate_format.row_count if orientation == 'V' else plate_format.col_count} {'columns' if orientation == 'V' else 'rows'} cannot accomidate {len(concentration_list)} concentrations.")
    
    extra_rows = (plate_format.row_count if orientation == 'H' else plate_format.col_count) - num_groups
    top_empty_group_idx = extra_rows // 2
    bottom_empty_group_idx = (plate_format.row_count if orientation == 'H' else plate_format.col_count) - (extra_rows - top_empty_group_idx)

    extra_cols = (plate_format.col_count if orientation == 'H' else plate_format.row_count) - (len(concentration_list) * num_replicates)
    left_empty_cols_idx = extra_cols // 2
    right_empty_cols_idx = (plate_format.col_count if orientation == 'H' else plate_format.row_count) - (extra_cols - left_empty_cols_idx)

    group_order = [(MapGroup(group_type="positive"))]
    for i in range(num_experimental_groups):
        group_order.append(MapGroup(group_type="experimental"))
    group_order.append(MapGroup(group_type="negative"))

    plate_map = PlateMap()
    plate_map.plate_format = plate_format
    group_idx = 0
    for row in range(plate_format.row_count if orientation == 'H' else plate_format.col_count):
        
        if row < top_empty_group_idx or row >= bottom_empty_group_idx:
            log('skip row')
            continue
        group = group_order[group_idx]
        concentration_step = 0
        concentration_idx = 0
        concentration = concentration_list[concentration_idx]
        for col in range(plate_format.col_count if orientation == 'H' else plate_format.row_count):
            if col < left_empty_cols_idx or col >= right_empty_cols_idx:
                log(f'skip col {col} < {left_empty_cols_idx} or {col} >= {right_empty_cols_idx}')
                continue
            if concentration_step % num_replicates == 0 and concentration_step != 0:
                concentration_idx += 1
                concentration = concentration_list[concentration_idx]
            well = next(w for w in plate_format.wells if w.row == (row if orientation == 'H' else col) and w.col == (col if orientation == 'H' else row))
            dose = Dose()
            dose.concentration__value=concentration
            dose.concentration__unit=concentration_unit
            dose.well_pos = well
            group.doses.append(
                dose
            )
            concentration_step += 1
        plate_map.map_groups.append(group)
        group_idx += 1
    
    return plate_map

def calculate_fold_changes(generate_command: GenerateConcentrations):
    max_concentration = generate_command.max_concentration
    num_fold_changes = generate_command.num_fold_changes
    fold_change = generate_command.fold_change
    
    concentration_list = []
    concentration = max_concentration
    for i in range(num_fold_changes):
        concentration_list.append(concentration)
        concentration = concentration / fold_change
    return concentration_list
    
# def extract_supernatent(command: ExtractSupernatent):
#     source_plate = command.source_plate
#     name = command.name
#     created = command.extracted
    
#     supernatent_plate = SupernatentPlate()
#     supernatent_plate.source_plate = source_plate.id
#     supernatent_plate.name = name
#     supernatent_plate.create = created
    
#     return supernatent_plate


        
