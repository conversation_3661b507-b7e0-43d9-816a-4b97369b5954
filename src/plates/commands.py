from dataclasses import dataclass
from plates.model import PlateMap, Treatment, Plate, PlateFormat, Vendor, WellPos
from typing import List, Optional, Dict
from datetime import datetime

@dataclass
class AssignMap:
    plate_map: PlateMap
    experimental_treatments: List[Treatment]
    negative_treatment: Treatment
    positive_treatment: Treatment
    plate: Plate

@dataclass
class CreateMap:
    plate_format: PlateFormat
    num_experimental_groups: int
    num_replicates: int = 3
    concentration_unit: str = "uM"
    concentration_list: List[float] = None
    orientation: str = 'H'


@dataclass
class GenerateConcentrations:
    max_concentration: float = 10.0
    fold_change: float = 3.0
    num_fold_changes: int = 8
    
@dataclass
class ExtractSupernatent:
    name: str
    source_plate: Plate
    extracted: datetime
    
