from typing import Callable, List
from fastapi import Depends, APIRouter
from fastapi.responses import HTMLResponse

from base.util import word_s, word_list
from app.auth import context
from app.layouts import Context
from app.html import node
from base.reps import rep
from app.table import reptable
from images.router import ims_repo, img_cache
from plates.model import Plate, WellPos
from images.model import ImageSet
from plates.render import plate_map
from qc.data import ImageSetData, ready_plates, parse_plate_no
from qc.plots import GrowthCurve, SizeDistributions, WellDistributions, cache
from qc.measures import LipidArea, LipidCount, LipidVolume, QcTable, QcExplanation, Percentiles, QcPlateMap, WellItemCount, QcMeasure
from qc.data import PlateImageData
from base.util import make_logger
log = make_logger(__name__)

router = APIRouter(prefix="/qc")
plates_rep = rep({"plates": str})

well_qc = {m.name: m for m in [WellItemCount("droplet"), WellItemCount("cluster")]}

class PlateMapInput:
    plates: str
    measure: str
    threshold: float = 30.0
    
pmi_rep = rep(PlateMapInput)
pmi_rep.options("measure", well_qc.keys())

# QC home page
@router.get("/", response_class=HTMLResponse)
async def qc(ht: Context = Depends(context)):
    plates = ready_plates(ht.db)
    ready = set([parse_plate_no(p.name) for p in plates])
    ht.db.query(Plate, ImageSet).filter(ImageSet.plate_id == Plate.id)
    
    log(f"REPS: {plates_rep}, {pmi_rep}")
    
    ret = ht.page("QC Home",
        ht.h2("Generate a QC report"),
        ht.h3("Pick one or more plates from this list of plates with images:"),
        reptable(rep(Plate), plates)(id="plates", href=ht.url("qc/plate")),
        ht.h3("Full QC report:"),
        ht.rform(f"qc/plate", plates_rep, tag="submit-plates"),
        ht.h3("Custom QC plate maps:"),
        ht.rform(f"qc/maps", pmi_rep, tag="submit-plates"),
    )
    return ret.pretty()

class Figures:
    def __init__(self):
        self.fig_no = 0
        
    def __call__(self, graph):
        self.fig_no += 1
        graph.plot()
        return node("div")(
            node("img", src=f"/{cache}/{graph.fn}", style="width: 100%;", alt=graph.title),
            node("p")(f"Figure {self.fig_no}: "+graph.caption)
        )

class Tables:
    def __init__(self):
        self.tab_no = 0
        
    def __call__(self, ht, table):
        self.tab_no += 1
        return node("div")(
            table.html(ht),
            node("p")(f"Table {self.tab_no}: "+table.caption)
        )

def downloads(ht: Context, plates: list[PlateImageData]):
    def series(p: PlateImageData, item: str, df: Callable):
        ns = []
        for day, ims in p.image_sets.items():
            # make sure the data is loaded and link valid           
            if df(ims).empty: ns.append(ht.span(f"{day}"))
            else: ns.append(
                ht.a(href=f"/store/cache/download/{ims.store.path}{item}.csv")(f"{day}")
            )
        return ht.span(f", {item}:", *ns)
        
    return ht.div(
        ht.h3("Downloads"),
        *[
            ht.div(
                f"Plate {p.plate_no}: ",
                ht.a( 
                     href=f"/store/cache/download/{p.store.path}lipid-size.csv"
                )("lipid size"),
                series(p, "clusters", lambda ims: ims.cluster_df),
#                series(p, "lipids", lambda ims: ims.lipid_df),
            )
            for p in plates
        ]
    )

ref_plate_nos = [174, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185]

def qc_plate_nos(plate_nos: List[int], ht: Context):
    plates = [PlateImageData(ht.db, no) for no in plate_nos]
    reference = [PlateImageData(ht.db, no) for no in ref_plate_nos]
    fig = Figures()
    tab = Tables()
    ret = ht.page("Plate QC",
        ht.h2(f"QC report for {word_list(plate_nos, 'plate')}:"),
        #ht.h3(f"QC measures summary:"),
        tab(ht, QcTable(plates)),
        tab(ht, Percentiles(plates, reference)),
        tab(ht, QcExplanation()),
        downloads(ht, plates),
        ht.h3(f"QC well maps:"), ht.p("Click on a well to see the images."),
        *[tab(ht, QcPlateMap(p, WellItemCount("cluster"), 30)) for p in plates],
        ht.h3(f"QC data plots:"),
        *[fig(WellDistributions(p)) for p in plates],
        *[fig(SizeDistributions(p)) for p in plates],
        fig(GrowthCurve(plates, 'mean')),
        fig(GrowthCurve(plates, 'count'))
    )
    return ret.pretty()

class PlateSel(plates_rep.form_parser()): pass

def qc_report(ht: Context, plate_ids: str):
    log(f"Selected plates: {plate_ids}")
    ids = [int(id) for id in plate_ids.split(',')]
    plates = ht.db.query(Plate).filter(Plate.id.in_(ids)).all()
    plate_nos = [parse_plate_no(p.name) for p in plates]
    log(f"Selected plate nos: {plate_nos}")
    return qc_plate_nos(plate_nos, ht)
    
@router.get("/plate/{plate_ids}", response_class=HTMLResponse)
async def qc_plate_get(plate_ids: str, ht: Context = Depends(context)):
    return qc_report(ht, plate_ids)

@router.post("/plate/", response_class=HTMLResponse)
async def qc_plate_post(input: PlateSel = Depends(), ht: Context = Depends(context)):
    return qc_report(ht, input.plates)

class MapsSel(pmi_rep.form_parser()): pass

@router.post("/maps/", response_class=HTMLResponse)
async def qc_maps_post(input: MapsSel = Depends(), ht: Context = Depends(context)):
    inp: PlateMapInput = pmi_rep.from_parser(input)
    log(f"Selected plates: {input} -> {inp}")
    ids = [int(id) for id in input.plates.split(',')]
    plates = ht.db.query(Plate).filter(Plate.id.in_(ids)).all()
    plate_nos = [parse_plate_no(p.name) for p in plates]
    plates = [PlateImageData(ht.db, no) for no in plate_nos]
    log(f"Selected plate nos: {plate_nos}")
    tab = Tables()
    ret = ht.page("QC Plate Maps",
        ht.h3(f"QC well maps:"),
        ht.rform(f"qc/maps", pmi_rep, default=inp),
        *[tab(ht, QcPlateMap(p, well_qc[inp.measure], input.threshold)) for p in plates],
    )
    return ret.pretty()

@router.get("/images/{plate_no}/{well_name}", response_class=HTMLResponse)
async def qc_plate_images(plate_no: str, well_name: str, ht: Context = Depends(context)):
    data = PlateImageData(ht.db, int(plate_no))
    imgs = []
    for day, imsd in data.image_sets.items():
        repo = ims_repo(imsd.image_set)
        rwell = repo.get_well(imsd.image_set.bt_key, well_name)
        if rwell is not None:
            rim = rwell.default_image()
            rim.encode(img_cache)
            img = ht.img(src=f"/store/cache/bin/{rim.cfn}-thumb.jpg", style="width: 3in;")()
        else:
            log(f"Not found well {day}/{well_name} in {repo}.")
            well_map = {im.well_pos: im for im in imsd.image_set.images}
            def well(wp: WellPos):
                """Function that creates the html for a single well."""
                if wp.id in well_map: return ht.div()("X")
                else: return ht.div()()
            img = ht.div(
                ht.span("This well not imaged. See imaging map:"),
                plate_map(ht, imsd.image_set.plate.format, well)(style="zoom: 40%;")
            )
        imgs.append(ht.tr(
            ht.td(ht.h3(f"Day {day}")),
            ht.td(img)
        ))
    t = f"Images for well {well_name} on Plate {plate_no}"
    return ht.page(t, ht.h3(t), ht.table(*imgs)).pretty()

@router.get("/iset/{iset_id}", response_class=HTMLResponse)
async def iset_qc(iset_id: int, ht: Context = Depends(context)):
    ims = ht.db.get(ImageSet, iset_id)
    
    # Get the cluster data
    data = ImageSetData(ims, parse_plate_no(ims.plate.name))
    title = f"QC data for {data}"
    
    ret = ht.page(title, ht.h3(title),
        *[ht.div(
            ht.h3(f"Combined {m.brief()} per well:"),
            QcPlateMap(data, m, 30).html(ht)
        ) for m in [LipidVolume(), LipidArea(), LipidCount()] ],
    )
    return ret.pretty()
