import os
import json
import dotenv
from base.clients import secretmanager
from urllib import parse
from datetime import timedelta
dotenv.load_dotenv()

basedir = os.path.abspath(os.path.dirname(__name__))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    def __init__(self, local=False):
        # connection_secrets = json.loads(
        #     secretmanager().access_secret_version(
        #         request={"name": "projects/1049074702976/secrets/mellitos-dev-db-secret/versions/3"}
        #     ).payload.data.decode("UTF-8")
        # )

        self.DEBUG = True
        # if local:
        #     self.SQLALCHEMY_DATABASE_URI = 'sqlite:////tmp/test.db'
        # else:
        #     self.SQLALCHEMY_DATABASE_URI = f'postgresql+pg8000://{connection_secrets["DB_USER"]}:{parse.quote(connection_secrets["DB_PASS"])}@/{connection_secrets["DB_NAME"]}?unix_sock={os.environ["INSTANCE_UNIX_SOCKET"]}/.s.PGSQL.5432'
        self.SECRET_KEY = "dev"
        self.SESSION_COOKIE_SECURE = True
        self.PERMANENT_SESSION_LIFETIME = timedelta(days=3)
        self.SESSION_COOKIE_HTTPONLY = True
        self.ASSAYS_BUCKET = "mellicell_development_bucket" # uploads to dev bucket not to mess with production
        self.ASSAY_TOPIC_PATH = "dev_assay_created" # create topic for development purposes
        self.PROJECT_ID = "development-311316"
        self.CYTOSMART_BUCKET = "cytosmart_brightfield_images_mellicell"
        self.CYTOSMART_MASK_BUCKET = "cytosmart_masks_mellicell"
        self.REQUEST_MASK_TOPIC = "dev_mask_requested" # create topic for development purposes
        self.VERSION_2_DATASET = "mellicell_image_data_v2"
        self.VERSION_1_DATASET = "mellicell_image_data"
        self.OLYMPUS_LIPID_TABLE = "cellpose-measurments"
        self.OLYMPUS_CLUSTER_TABLE = "cluster-statistics"
        self.CYTOSMART_LIPID_TABLE = "cytosmart_lipid_measurments"
        self.CYTOSMART_CLUSTER_TABLE = "cytosmart_measurements"
        self.RESIZE_IMAGE_SERVICE_URL = "https://olympus-resize-service-gkjofhbn5a-ue.a.run.app"
        self.OLYMPUS_IMAGE_BUCKET = "mellicell_development_bucket" # uploads to dev bucket not to mess with production

class ProductionConfig(Config):

    def __init__(self):
        """ secret_key = secretmanager().access_secret_version(
            request={"name": "projects/1049074702976/secrets/mellitos-secret-key/versions/1"}
        ).payload.data.decode("UTF-8") """
        secret_key="ce7281f36d32021632910ab09f5252fd0696aa6bc1f926aac30a816eedb7ac00"
        
        connection_secrets = json.loads(
            secretmanager().access_secret_version(
                request={"name": "projects/1049074702976/secrets/mellitos-db-secret/versions/3"}
            ).payload.data.decode("UTF-8")
        )
        self.DEBUG = False
        self.SECRET_KEY = secret_key
        # self.SQLALCHEMY_DATABASE_URI = f'postgresql+pg8000://{connection_secrets["DB_USER"]}:{parse.quote(connection_secrets["DB_PASS"])}@/{connection_secrets["DB_NAME"]}?unix_sock={os.environ["INSTANCE_UNIX_SOCKET"]}/.s.PGSQL.5432'
        self.SESSION_COOKIE_NAME = "7e23b"
        self.SESSION_COOKIE_SECURE = True
        self.PERMANENT_SESSION_LIFETIME = timedelta(days=3)
        self.SESSION_COOKIE_HTTPONLY = True
        self.ASSAYS_BUCKET = "mellicell_lab_bucket"
        self.ASSAY_TOPIC_PATH = "assay_created"
        self.PROJECT_ID = "development-311316"
        self.CYTOSMART_BUCKET = "cytosmart_brightfield_images_mellicell"
        self.CYTOSMART_MASK_BUCKET = "cytosmart_masks_mellicell"
        self.REQUEST_MASK_TOPIC = "mask_requested"
        self.VERSION_2_DATASET = "mellicell_image_data_v2"
        self.VERSION_1_DATASET = "mellicell_image_data"
        self.OLYMPUS_LIPID_TABLE = "cellpose-measurments"
        self.OLYMPUS_CLUSTER_TABLE = "cluster-statistics"
        self.CYTOSMART_LIPID_TABLE = "cytosmart_lipid_measurments"
        self.CYTOSMART_CLUSTER_TABLE = "cytosmart_measurements"
        self.RESIZE_IMAGE_SERVICE_URL = "https://olympus-resize-service-gkjofhbn5a-ue.a.run.app"
        self.OLYMPUS_IMAGE_BUCKET = "mellicell_raw_images"
