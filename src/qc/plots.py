import os, numpy as np
import matplotlib
import matplotlib.pyplot as plt

from base.util import lform, word_s, word_list
from qc.data import PlateImageData, excl_clause, ready_plate_nos
from base.util import make_logger
log = make_logger(__name__)
#
# A variety of QC plots
#
cache = "static/img/cache/qc"

def set_styles(fig, ax):
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)
    fig.set_size_inches(10, 4)

class WellDistributions:
    def __init__(self, plate: PlateImageData, sum: str = 'max'):
        self.plate = plate
        self.sum = sum
        plist = plate.plate_no
        self.fn = f"well_dist_{sum}_{plist}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        self.title = f"Distributions for mean(well) {sum}(cluster) lipid droplet area, Plate {plist}"
        self.caption = f"""
            Distribution of mean {sum} lipid droplet size per well on plate {plate.plate_no}.
            The distribution is shown for each of {len(plate.image_sets)} imaging days, 
            showing the evolution over time. Shown on the Y-axis is the mean (per well) of the 
            {sum} (per cluster) of lipid droplet size.
            {excl_clause(plate.excluded_wells)} 
        """
        
    def plot(self):
        """Plot size distribution curves for a plate"""
        max_day = max(self.plate.image_sets.keys())

        miny, maxy = (-200, 4000)
        width = 1
        fig, ax = plt.subplots()
        y = []
        x = []
        days = []      
        for day, ims in self.plate.image_sets.items():
            df = self.plate.cluster_df(ims)
            by_well = df.groupby('well')
            y0 = by_well[self.sum+'_area'].mean()
            if len(y0) > 0:
                x.append(day + beeswarm(y0, width=width))
                y.append(y0)
                days.append(day)
        for i, d in enumerate(x):
            ax.annotate(f"{len(d):d}", (days[i], miny), ha='center', va='bottom')
        log(f"Y: {lform(y)}")
        log(f"Days: {lform(days)}")
        if len(y) == 0:
            ax.set_title("No data")
            return fig
        ax.boxplot(y, positions=days, widths=3*width, showfliers=False, showcaps=True)
        x = np.concatenate(x)
        y = np.concatenate(y)
        nout = len(y[y>maxy])
        ax.annotate(f"{nout} outliers > {maxy} not visible", (0, maxy), va='top')
        ax.set_ylim(miny, maxy)
        ax.plot(x, y, '.', alpha=0.3)
        ax.set_ylabel('Mean '+self.sum + ' droplet area')
        ax.set_xlabel('Culture day')
        ax.set_title(self.title)
        set_styles(fig, ax)
        plt.savefig(cache + "/" + self.fn)
        return fig


class SizeDistributions:
    def __init__(self, plate: PlateImageData, sum: str = 'max'):
        self.plate = plate
        self.sum = sum
        plist = plate.plate_no
        self.fn = f"size_dist_{sum}_{plist}.png"
        self.title = f"Distributions for {sum} lipid droplet area per cluster, Plate {plist}"
        self.caption = f"""
            Distribution of {sum} lipid droplet area for all clusters on plate {plate.plate_no}.
            {excl_clause(plate.excluded_wells)} The 
            distribution is shown for each of {len(plate.image_sets)} imaging days, 
            showing the evolution over time.
        """
        
    def plot(self):
        """Plot size distribution curves for a plate"""
        max_day = max(self.plate.image_sets.keys())

        fig, ax = plt.subplots()        
        for day, ims in self.plate.image_sets.items():
            df = self.plate.cluster_df(ims)
            nwells = len(df['well'].unique())
            ys, edges = np.histogram(df[self.sum+'_area'], bins=25, range=(0, 5000))
            #log(f"Day {day} edges {edges} ys {ys}")
            log(f"Day {day} max_day {max_day}, all: {self.plate.image_sets.keys()}")
            col = f"{(0.95-0.95*day/max_day):.2f}"
            ax.plot(edges[:-1], ys, label=f"Day {day} ({nwells} wells)", color=col)
        ax.set_ylabel('Count')
        ax.set_xlabel(self.sum.capitalize() + ' droplet area')
        ax.set_title(self.title)
        ax.legend(fontsize="9")
        set_styles(fig, ax)
        plt.savefig(cache + "/" + self.fn)
        return fig


class GrowthCurve:
    def __init__(self, plates: list[PlateImageData], sum: str = 'mean'):
        # Make sure exclusions are consistent across plates
        excluded_wells = plates[0].excluded_wells
        for plate in plates[1:]:
            if plate.excluded_wells != excluded_wells:
                excluded_wells = None
                break
            
        self.plates = plates
        self.sum = sum
        
        # Set caching filename
        plate_names = [str(p.plate_no) for p in plates]
        self.fn = f"growth_curve_{sum}_{','.join(plate_names)}.png"
        
        # Title and caption information
        self.what = "lipid droplet"
        self.what = f"{self.what} count" if sum=='count' else f"{sum} {self.what} area"
        wlist = word_list(plate_names, 'plate')
        self.title = f"Growth curve for {self.what}, {wlist}"
        self.caption = f"""
            Average {sum} lipid droplet area for all clusters on {wlist}. 
            Plotted over time for all available imaging days to show growth over time.
            {excl_clause(excluded_wells)}
            The standard deviation of the distribution is indicated by the vertical lines.
        """
        
    def plot(self):
        """Plot growth curves for one or more plates"""
        fig, ax = plt.subplots()
        for plate in self.plates:
            df = plate.lipid_size
            if hasattr(df, self.sum+'-std'):
                ax.errorbar(df['day'], df[self.sum], yerr=df[self.sum+'-std'], fmt='o-', label='Plate '+str(plate.plate_no))
            else:
                ax.plot(df['day'], df[self.sum], 'o-', label='Plate '+str(plate.plate_no))
        ax.set_xlabel('Day')
        ax.set_ylabel(self.what.capitalize())
        ax.set_title(self.title)
        ax.legend()
        set_styles(fig, ax)
        plt.savefig(cache + "/" + self.fn)
        return fig

def beeswarm(y, nbins=None, width=1.):
    """
    Returns x coordinates for the points in ``y``, so that plotting ``x`` and
    ``y`` results in a bee swarm plot.
    from: https://stackoverflow.com/questions/36153410/how-to-create-a-swarm-plot-with-matplotlib
    """
    y = np.asarray(y)
    if nbins is None:
        # nbins = len(y) // 6
        nbins = np.ceil(len(y) / 6).astype(int)

    # Get upper bounds of bins
    x = np.zeros(len(y))

    nn, ybins = np.histogram(y, bins=nbins)
    nmax = nn.max()

    #Divide indices into bins
    ibs = []#np.nonzero((y>=ybins[0])*(y<=ybins[1]))[0]]
    for ymin, ymax in zip(ybins[:-1], ybins[1:]):
        i = np.nonzero((y>ymin)*(y<=ymax))[0]
        ibs.append(i)

    # Assign x indices
    dx = width / (nmax // 2)
    for i in ibs:
        yy = y[i]
        if len(i) > 1:
            j = len(i) % 2
            i = i[np.argsort(yy)]
            a = i[j::2]
            b = i[j+1::2]
            x[a] = (0.5 + j / 3 + np.arange(len(b))) * dx
            x[b] = (0.5 + j / 3 + np.arange(len(b))) * -dx

    return x

if __name__ == "__main__":
    from base.database import get_db
    db = get_db()

    # log(f"Ready plates: {ready_plate_nos(db)}")
    # exit(0)
    data = [PlateImageData(db, p) for p in [166, 167]]
    sd = WellDistributions(data[0])
    sd.plot()
    # sd = SizeDistributions(data[0])
    # sd.plot()
    # GrowthCurve(data).plot()
    # gcs = [GrowthCurve(data, sum) for sum in ['mean', 'max']]
    # for gc in gcs: gc.plot()
    plt.show()

