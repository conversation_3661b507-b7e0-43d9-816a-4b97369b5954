from typing import Tuple, List
import math
import numpy as np
import pandas as pd
from scipy.stats import rankdata

from base.database import Session
from app.layouts import Context
from app.table import table
from qc.data import ImageSetData, PlateImageData, excl_clause
from plates.render import plate_map
from plates.model import PlateFormat, WellPos
from base.util import make_logger
log = make_logger(__name__)

class QcMeasure:
    def __init__(self, tag: str, name: str, unit: str, description: str):
        self.tag = tag
        self.name = name
        self.unit = unit
        self.description = description

    def __str__(self):
        return f"QcMeasure {self.name} [{self.unit}]: {self.description}"

    def __repr__(self):
        return self.__str__()

class WellMeasure:
    """A measure that can be computed for each well."""
    def __init__(self, tag, name, unit, description):
        self.tag = tag
        self.name = name
        self.unit = unit
        self.description = description

    def __str__(self):
        return f"WellMeasure {self.name} [{self.unit}]: {self.description}"

    def __repr__(self):
        return self.__str__()
    
    def brief(self):
        u = f" [{self.unit}]" if self.unit != "" else ""
        return f"{self.name}{u}"

    def compute(self, plate: PlateImageData, wp: WellPos):
        raise NotImplementedError("Subclasses must implement this method")
    
class LipidArea(WellMeasure):
    def __init__(self):
        super().__init__("lipid_area", "lipid droplet area", '100 um^2', 
            'Combined area of lipid droplets in a well')

    def narrative(self):
        return """
        Lipid droplet area is an important measure of development of adipocytes during culture.
        It is calculated as the sum of the areas of all lipid droplets in a well.
        """
        
    def compute(self, ims: ImageSetData, wp: WellPos):
        df = ims.lipid_df
        df = df[df['well']==wp.well_name]
        return df['area'].sum() / 100

class LipidVolume(WellMeasure):
    def __init__(self):
        super().__init__("lipid_volume", 'lipid droplet volume', 'pL', 
            'Combined volume of lipid droplets in a well')

    def narrative(self):
        return """
        Lipid droplet volume is an important measure of development of adipocytes during culture.
        It is calculated as the sum of the volumes of all lipid droplets in a well. Since only
        area is directly measured, volume is calculated as (4/3)pi*r^3, where r is (area/pi)^0.5.
        """
        
    def compute(self, ims: ImageSetData, wp: WellPos):
        df = ims.lipid_df
        df = df[df['well']==wp.well_name]
        volume = np.pi * (df['area'] / np.pi)**1.5 * 4 / 3000
        return volume.sum()

class LipidCount(WellMeasure):
    def __init__(self):
        super().__init__("lipid_count", 'lipid droplet count', '', 
            'Number of lipid droplets in a well')

    def narrative(self):
        return """
        Lipid droplet count is an important measure of development of adipocytes during culture.
        It is calculated as the number of all lipid droplets in a well.
        """
        
    def compute(self, ims: ImageSetData, wp: WellPos):
        df = ims.lipid_df
        df = df[df['well']==wp.well_name]
        return df.shape[0]

class GrowthRate(QcMeasure):
    def __init__(self, days: Tuple = (5, 25), sum: str = 'max'):
        if days[1] <= 30: time = 'early '
        elif days[0] >= 20: time = 'late '
        else: time = ''
        super().__init__(f"{time.replace(' ','_')}growth", f"{time}growth rate".capitalize(), "%/day", 
            f'Growth rate between days {days[0]} and {days[1]}') 
        self.days = days
        self.sum = sum
    
    def narrative(self, excluded_wells = None):
        return f"""
            Growth rate is the percent increase of lipid drop area per day. 
            It is calculated as 100 times the slope of the log2 of the 
            mean (well) {self.sum} (cluster) lipid droplet area 
            per well across days {self.days[0]} and {self.days[1]}. 
            The slope is calculated using linear regression.
            {excl_clause(excluded_wells)}
        """
        
    def compute(self, plate: PlateImageData):
        # if plate.excluded_wells!=excluded_wells:
        #     raise Exception(f"Excluded wells inconsistent for plate {plate.plate_no}")
        x, y = [], []
        log(f"Plate {plate.plate_no}, {len(plate.image_sets)} image sets")
        for day, ims in plate.image_sets.items():
            if day < self.days[0] or day > self.days[1]:
                log(f"  Day {day} skipped: outside range {self.days}")
                continue
            df = plate.cluster_df(ims)
            #log(f"  Day {day} {df.shape}")
            by_well = df.groupby('well')
            y0 = by_well[self.sum+'_area'].mean()
            if len(y0) > 0:
                x0 = np.array([day] * len(y0))
                x.append(x0)
                y.append(y0)
        x = np.concatenate(x)
        y = np.concatenate(y)
        #log(f"GROWTH: x {x.shape} {x}")
        #log(f"GROWTH: y {y.shape} {y}")
        #log(f"GROWTH: y {y.shape} {y.dtype}")
        m, b = np.polyfit(x, np.log2(y), 1)
        return m * np.log2(np.e) * 100


class WellItemCount(QcMeasure):
    def __init__(self, item: str, days: Tuple = (25, 60)):
        if item == 'droplet': name = 'lipid droplet'
        elif item == 'cluster': name = 'adipocyte'
        else: raise ValueError(f"Unknown item {item}")
        super().__init__(f"{item}_count", f'{name.capitalize()} count', 'count', 
            f'Average lipid {item} count across wells between days {days[0]} and {days[1]}') 
        self.days = days
        self.item = item

    def narrative(self, excluded_wells = None):
        return f"""
            {self.name} is an important measure of development of adipocytes during culture.
            It is calculated as the number of lipid {self.item} per well, averaged over all 
            imaging days between days {self.days[0]} and {self.days[1]}, inclusive.
        """
    
    def compute(self, plate: PlateImageData, wp: WellPos):  
        # if plate.excluded_wells!=excluded_wells:
        #     raise Exception(f"Excluded wells inconsistent for plate {plate.plate_no}")
        y = []
        for day, ims in plate.image_sets.items():
            if day < self.days[0] or day > self.days[1]:
                continue
            df = ims.cluster_df
            dfw = df[df['well']==wp.well_name]
            if self.item == 'droplet':
                y0 = dfw['count_area'].sum()
            else:
                y0 = dfw['count_area'].count()
            y.append(y0)
        y = np.array(y)
        return np.mean(y)


class LipidCountAvg(QcMeasure):
    def __init__(self, days: Tuple = (25, 60)):
        super().__init__("lipid_count", "Lipid droplet count", "count", 
            f'Average lipid droplet count across wells between days {days[0]} and {days[1]}') 
        self.days = days

    def narrative(self, excluded_wells = None):
        return f"""
            Lipid droplet count is an important measure of development of adipocytes during culture.
            It is calculated as the number of lipid droplets per well, averaged over all wells 
            and all imaging days between days {self.days[0]} and {self.days[1]}, inclusive.
            {excl_clause(excluded_wells)}
        """
    
    def compute(self, plate: PlateImageData):
        # if plate.excluded_wells!=excluded_wells:
        #     raise Exception(f"Excluded wells inconsistent for plate {plate.plate_no}")
        y = []
        for day, ims in plate.image_sets.items():
            if day < self.days[0] or day > self.days[1]:
                continue
            df = plate.cluster_df(ims)
            by_well = df.groupby('well')
            y0 = by_well['count_area'].sum()
            y.append(y0)
        log(f"Lipid count days: {len(y)}/{len(plate.image_sets)}")
        y = np.concatenate(y)
        return np.mean(y)

class LipidSizeAvg(QcMeasure):
    def __init__(self, days: Tuple = (25, 60), sum: str = 'max'):
        super().__init__("lipid_size", "Lipid size average", "area", 
            f'Average mean {sum} lipid droplet area across wells between days {days[0]} and {days[1]}') 
        self.days = days
        self.sum = sum

    def narrative(self, excluded_wells = None):
        return f"""
            Lipid size is an important measure of development of adipocytes during culture.
            It is calculated as the  
            mean (well) {self.sum} (cluster) lipid droplet size per well, averaged over all wells 
            and all imaging days between days {self.days[0]} and {self.days[1]}, inclusive.
            {excl_clause(excluded_wells)}
        """
    
    def compute(self, plate: PlateImageData):
        # if plate.excluded_wells!=excluded_wells:
        #     raise Exception(f"Excluded wells inconsistent for plate {plate.plate_no}")
        y = []
        for day, ims in plate.image_sets.items():
            if day < self.days[0] or day > self.days[1]:
                continue
            df = plate.cluster_df(ims)
            by_well = df.groupby('well')
            y0 = by_well[self.sum+'_area'].mean()
            y.append(y0)
        y = np.concatenate(y)
        return np.mean(y)


class LipidSizeCV(QcMeasure):
    def __init__(self, days: Tuple = (25, 60), sum: str = 'max'):
        super().__init__("lipid_cv", "Lipid size variation", "%CV", 
            f'Variation of mean {sum} lipid droplets across wells between days {days[0]} and {days[1]}') 
        self.days = days
        self.sum = sum
    def narrative(self, excluded_wells = None):
        return f"""
            Lipid size variation is a measure of the variability of lipid droplet size across wells.
            It is indicative of the sensitivity of later measurements of lipid droplet size response 
            to treatment.
            It is calculated as the coefficient of variation (CV) of the 
            mean (well) {self.sum} (cluster) lipid droplet size across wells. 
            CVs are computed across all imaging days between day {self.days[0]} and day {self.days[1]} (inclusive).
            {excl_clause(excluded_wells)}
        """
    
    def compute(self, plate: PlateImageData):
        # if plate.excluded_wells!=excluded_wells:
        #     raise Exception(f"Excluded wells inconsistent for plate {plate.plate_no}")
        y = []
        dy = []
        for day, ims in plate.image_sets.items():
            if day < self.days[0] or day > self.days[1]:
                continue
            df = plate.cluster_df(ims)
            by_well = df.groupby('well')
            y0 = by_well[self.sum+'_area'].mean()
            y.append(y0)
            dy.append(y0 - np.mean(y0))
        y = np.concatenate(y)
        dy = np.concatenate(dy)
        if len(y) == 0: 
            return np.nan
        return np.std(dy) / np.mean(y) * 100

class QcPlateMap:
    def __init__(self, 
        data: PlateImageData | ImageSetData, 
        measure: QcMeasure, 
        threshold: float, 
        above: bool = True
    ):
        self.data = data
        self.measure = measure
        self.threshold = threshold
        self.above = above
        dir = 'above' if above else 'below'
        self.caption = \
          f"Map of wells on plate {data.plate_no} with {measure.name} {dir} {threshold} {measure.unit}. " +\
          measure.narrative()

    def html(self, ht: Context):
        if isinstance(self.data, PlateImageData):
            def link(wp: WellPos):
                return ht.a(href=f"/qc/images/{self.data.plate_no}/{wp.well_name}")
        else:
            imap = {im.wellPos.well_name: im for im in self.data.image_set.images}
            log(f"IMAP: {len(imap)} wells.")
            def link(wp: WellPos):
                if wp.well_name not in imap: return ht.span(dummy="x")
                return ht.a(href=f"/imaging/image/{imap[wp.well_name].id}")
            
        def well(wp: WellPos):
            v = self.measure.compute(self.data, wp)
            t = f"{wp.well_name} ({wp.row} {wp.col}): {v:.3f} {self.measure.unit}"
            qc = 'good'
            if math.isnan(v): qc = 'bad'
            elif self.above and v < self.threshold: qc = 'bad'
            elif not self.above and v > self.threshold: qc = 'bad'
            return link(wp)(f"{v:.0f}")(qc=qc, title=t, style="font-size: 0.7vw;")
        
        fn = self.measure.tag+".tsv"
        self.data.store.cached_df(fn, lambda: self.df(ht.db))
        
        more = ht.span()
        if isinstance(self.data, PlateImageData):
            ns = []
            for day, ims in self.data.image_sets.items():
                if ims.lipid_df.empty: ns.append(ht.span(f"{day}"))
                else: ns.append(
                    ht.a(href=f"/qc/iset/{ims.image_set.id}")(f" {day}") 
                )
            more = ht.div("Individual image sets: Days ", *ns)
        return ht.div(
            plate_map(ht, self.data.plate_format, well),
            ht.span(f"Threshold: {self.threshold} {self.measure.unit}. "),
            ht.span(f"Click to see images. "),
            ht.a(href=f"/store/cache/download/{self.data.store.path}{fn}")("Export this data"),
            more
        )
    
    def df(self, db: Session):
        pf = db.get(PlateFormat, self.data.plate_format)
        wps = db.query(WellPos).filter(WellPos.plate_format==pf.name).all()
        
        # Enumerate the well positions in row by row order (don't rely on query order)
        wp = [None] * (pf.row_count * pf.col_count)
        for w in wps:
            j = w.row * pf.col_count + w.col
            wp[j] = w

        data = []
        for w in wp:
            v = self.measure.compute(self.data, w)
            data.append({
                "well": w.well_name,
                self.measure.tag: v
            })
        return pd.DataFrame(data)
    

measures = [LipidCountAvg(), LipidSizeAvg(), LipidSizeCV(), GrowthRate(days=(5, 25)), GrowthRate(days=(25, 60))]

class QcTable:
    def __init__(self, plates: List[PlateImageData]):
        self.plates = plates
        self.caption = "Summary of QC measures for selected plates"

    def html(self, ht: Context):
        def cell(p, m):
            try: 
                return f"{m.compute(p):.2f}"
            except ValueError as e: 
                log(f"Error computing {m.name} for plate {p.plate_no}: {e}")
                return "NA"
        data = [
            { "Plate": str(p.plate_no), **{f"{m.name} [{m.unit}]": cell(p, m) for m in measures}}
            for p in self.plates
        ]
        return table(data)

class Percentiles:
    def __init__(self, plates: List[PlateImageData], reference: List[PlateImageData]):
        self.plates = plates
        self.reference = reference
        refnos = [p.plate_no for p in reference]
        self.caption = \
            f"Percentiles of QC measures for selected plates compared to reference plates {refnos}"

    def html(self, ht: Context):
        pnosd = np.array([p.plate_no for p in self.plates])
        pnosr = np.array([p.plate_no for p in self.reference])
        pnoss = np.array(list(set(pnosd) | set(pnosr)))  # all plates
        sorter = np.argsort(pnoss)
        ixd = sorter[np.searchsorted(pnoss, pnosd, sorter=sorter)]
        ixr = sorter[np.searchsorted(pnoss, pnosr, sorter=sorter)]
        ps = [None]*len(pnoss)
        for k, i in enumerate(ixr): ps[i] = self.reference[k]
        for k, i in enumerate(ixd): ps[i] = self.plates[k]
        log(f"Selected plates: {pnoss} {pnosd} -> {ixd}")
        data = {}
        for m in measures:
            try:
                xs = [m.compute(p) for p in ps]
                ranks = rankdata(xs)*100/len(xs)
                data[m.name] = ranks[ixd]
            except ValueError as e:
                log(f"Error computing {m.name} percentiles: {e}")
                data[m.name] = [np.nan]*len(ixd)
        data = [
            { "Plate": str(pnoss[i]), **{f"{m.name}": f"{data[m.name][k]:.0f}" for m in measures}}
            for k, i in enumerate(ixd)
        ]
        return table(data)

class QcExplanation:
    def __init__(self):
        self.caption = "List of QC measures with explanations"
        
    def html(self, ht: Context):
        data = [
            { "QC Measure": m.name, "Unit": m.unit, "Description": m.description, "Explanation": m.narrative()}
            for m in measures
        ]
        return table(data)

        
        