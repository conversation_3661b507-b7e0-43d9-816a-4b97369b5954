#
#  HTML Combinator library
#

from typing import Any, Callable, List, Tuple
from enum import Enum
from datetime import datetime
from dataclasses import Field, MISSING
from fastapi import Form
from base.util import make_logger
log = make_logger(__name__)

class Html:
    pass

class HtmlGen:
    pass

class HtmlNodeGen(HtmlGen):
    def __init__(self, tag, **attrs):
        self.tag = tag
        self.attrs = node_attrs(attrs)

    def __call__(self, *args: Html):
        #log(f"Call: '{args}'")
        return HtmlNode(self, list(args))
    
class HtmlNode(Html):
    def __init__(self, gen, children):
        def asnode(x):
            if x is None: return None
            if isinstance(x, str): return HtmlString(x)
            if isinstance(x, int): return HtmlString(str(x))
            if isinstance(x, HtmlGen): return asnode(x())
            if not isinstance(x, Html):
                raise Exception(f"Expected Html, got {x}")
            if isinstance(x, HtmlNode) and x.is_nil(): return None
            return x
        self.gen = gen
        self.children = [asnode(c) for c in children if asnode(c) is not None]

    def is_nil(self):
        return self.gen.tag=="" and len(self.children)==0
    
    def __call__(self, **attrs: str):
        "Add attributes for this node."
        self.gen.attrs.update(node_attrs(attrs))
        return self
    
    def __str__(self):
        chs = ''.join(str(c) for c in self.children)
        if self.gen.tag=="": return chs
        attrs = ''.join(f' {k}="{v}"' for k, v in self.gen.attrs.items())
        return f"<{self.gen.tag}{attrs}>{chs}</{self.gen.tag}>"
    
    def pretty(self, indent=0):
        attrs = ''.join(f' {k}="{v}"' for k, v in self.gen.attrs.items())
        if len(self.children)==0: return f"<{self.gen.tag}{attrs}></{self.gen.tag}>"
        ni = indent + 1 if self.gen.tag!="" else indent
        def sep(i): return '\n' + '    ' * i
        def brace(i): return sep(i) if len(self.children)>1 else ""
        chs = sep(ni).join(c.pretty(ni) for c in self.children)
        if self.gen.tag=="": return chs
        return f"<{self.gen.tag}{attrs}>{brace(ni)}{chs}{brace(indent)}</{self.gen.tag}>"
    
    def __add__(self, other):
        xs = [other]
        if isinstance(other, list): xs = other
        if isinstance(other, HtmlNode) and other.gen.tag=="": xs = other.children
        if self.gen.tag=="": return HtmlNode(self.gen, self.children + xs)
        return HtmlNode(HtmlNodeGen(""), [self] + xs)


class HtmlString(Html):
    def __init__(self, s):
        self.s = s

    def __str__(self):
        return self.s
    
    def pretty(self, indent=0):
        return self.s
    
    def __add__(self, other):
        xs = [other]
        if isinstance(other, list): xs = other
        return HtmlNode(HtmlNodeGen(""), [self] + xs)

def flatten(xs):
    ret = []
    for x in xs:
        if isinstance(x, list): ret.extend(flatten(x))
        elif isinstance(x, HtmlNode) and x.gen.tag=="": ret.extend(flatten(x.children))
        else: ret.append(x)
    return ret

def html(x):
    return HtmlString(x)

def node(tag, **attrs):
    return HtmlNodeGen(tag, **attrs)
                            
def div(*args: Html):
    return node('div')(*args)

def ulist(**args: Html):
    return lambda *xs: node('ul', **args)(*[node('li')(x) for x in flatten(xs)])

def when(cond):
    return lambda *xs: node("")(*xs) if cond else None

def tablerow(*args: Html):
    return node("tr")(*[node("td", **{'class': f"label{i}"})(a) for i, a in enumerate(args)])

def submitrow(names: List[str] = ["Submit"]):
    return node("tr")(
        node("td", colspan=2, **{'class': "sub"})(
            *[node("input", type="submit", value=sn)() for sn in names]
    ))

def node_attrs(attrs):
    def key(k): 
        if k=="_class": return "class"
        if k=="_for": return "for"
        return k
    return {key(k): v for k, v in attrs.items()}

def tag(func):
    """Decorator for tag functions that allows for both tag and tag() usage."""
    def wrapper(ht, *args: Html, **kwargs: str):
        if len(args)>0: return func(ht, *args, **kwargs)
        return lambda *xs: func(ht, *xs, **kwargs)
    return wrapper

# All legal tag names (add missing ones as needed)
html_tags = set([
    "div", "span", "a", "p", "h1", "h2", "h3", "ul", "li", "table", 
    "tr", "td", "th", "input", "form", "button", "label", "select", "option", "textarea", 
    "img", "br", "hr", "pre", "code", "style", "script", "link", "meta", "head", 
    "body", "html", "title", "nav", "header", "footer", "section", "details", "summary",
    "thead", "tbody"
])

class HtmlSource:
    """The API for generating HTML."""
    # Generate a node list
    def __call__(self, *args: Html):
        return HtmlNodeGen("")(*args)
    
    # Intercept getattr to allow arbitry tag names
    def __getattr__(self, name):
        if not name in html_tags: 
            raise Exception(f"Unknown HTML tag: '{name}'")
        def func(*args: Html, **kwargs: str):   # Permit both tag() and tag usage
            if len(args)>0: return HtmlNodeGen(name, **kwargs)(*args)
            return HtmlNodeGen(name, **kwargs)
        return lambda *args, **kwargs: func(*args, **kwargs)
    
    def when(ht, cond):
        """Conditional html."""
        return lambda *xs: HtmlNodeGen("")(*xs) if cond else None
    
    def tablerow(ht, *args: Html):
        return ht.tr(*[ht.td(_class=f"label{i}")(a) for i, a in enumerate(args)])

    def submitrow(ht, names: List[str] = ["Submit"]):
        return ht.tr(
            ht.td(colspan=2, _class="sub")(
                *[ht.input(type="submit", value=sn)() for sn in names]
        ))

    @tag
    def ulist(ht, *args, **kwargs: str):
        return ht.ul(**kwargs)(*[ht.li(x) for x in flatten(args)])
    
    @tag
    def tablerow(ht, *args, **kwargs: str):
        return ht.tr(**kwargs)(*[ht.td(_class=f"label{i}")(a) for i, a in enumerate(args)])

    def submitrow(ht, names: List[str] = ["Submit"]):
        return ht.tr(
            ht.td(colspan=2, _class="sub")(
                *[ht.input(type="submit", value=sn)() for sn in names]
        ))

ht = HtmlSource()

if __name__ == "__main__":
    ns = ht.div(_class="top")(
        ht.ulist(id="list1")(
            "One",
            "Two",
            "Three"
        ),
        ht.ulist(
            "Four",
            "Five",
            "Six",
            id="list2"
        ),
        ht.ulist(
            "Seven",
            "Eight",
            "Nine",
        )(id="list3"),
        ht.tablerow("A", "B", "C", _class="row"),
        ht.submitrow(["Save", "Cancel"])
    )
    log(ns.pretty())