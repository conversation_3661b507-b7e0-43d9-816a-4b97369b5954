from fastapi import Form
from dataclasses import dataclass
from app.model import UserRole

#
# Routes for user authentication management
#
@dataclass
class Login:
    username: str = Form(...)
    password: str = Form(...)

@dataclass
class Reset:
    username: str = Form(...)
    new_password: str = Form(...)
    repeat_password: str = Form(...)

@dataclass
class Change:
    old_password: str = Form(...)
    new_password: str = Form(...)
    repeat_password: str = Form(...)

@dataclass
class Signup:
    username: str = Form(...)
    email: str = Form(...)
    password: str = Form(...)
    role: UserRole = Form(...)

