from dataclasses import dataclass, fields
from typing import Any, Callable, List, Tuple

from base.appinfo import appinfo
from base.reps import Rep, ObjRep
from app.html import Html, HtmlString, HtmlSource, node, ulist, div, when, tablerow, submitrow
from app.table import table
from base.database import Base, transact, flush, Session, populate_enums as db_populate_enums
from app.model import User, UserRole
from plates.model import WellPos, PlateFormat
import string
from base.util import make_logger
log = make_logger(__name__)

resources = node("")(
    node("link", rel="shortcut icon", type="image/x-icon", href="/static/img/favicon.ico"),
    node("link", rel="stylesheet", type="text/css", href="/static/css/datatables.min.css"),
    node("link", rel="stylesheet", type="text/css", href="/static/css/select.dataTables.css"),
    # These are our own styles
    node("link", rel="stylesheet", type="text/css", href="/static/css/style.css"),

    node("script", type="text/javascript", src="/static/js/jquery-3.7.1.min.js"),
    node("script", type="text/javascript", src="/static/js/datatables.js"),
    node("script", type="text/javascript", src="/static/js/dataTables.select.js"),
    node("script", type="text/javascript", src="/static/js/select.dataTables.js"),
    # These are our own scripts
    node("script", type="text/javascript", src="/static/js/scripts.js")()
)

# Set this to a function that generates a list of links in the main app module (rar.py)
navgen: Callable[["Context"], Html] = lambda ht: []
def navset(f: Callable[["Context"], Html]):
    global navgen
    navgen = f

def navbar(ht):
    global navgen
    uname = f" ({ht.user.name})" if ht.authenticated else ""
    links = { f"User{uname}": [
        ht.link("")("Home"),
        when(ht.admin)(ht.link("auth/users")("Users")),
        when(ht.admin)(ht.link("auth/reset")("Reset Password")),
        ht.link("auth/change")("Change Password"),
        ht.link("auth/logout")("Logout"),
    ]} | {k: v for k, v in navgen(ht).items() if len(v) > 0}
    bar = node("ul")(
        *[node("details")(node("summary")(k), *[node("li")(x) for x in xs]) for k, xs in links.items()]
    )
    logo = node("img", src="/static/img/logo.png", alt="logo", width="30", **{'class': "logo"})
    navh = node("table", style="font-size: 1.5em; text-align:center;")(node("tr")(
            node("td")(logo), node("td")("MellitOS"), 
    ))
    appi = node("table", style="text-align:left;")(
        node("tr")(node("td")(f"{appinfo.name} {appinfo.version} {'' if appinfo.deployed else 'dev'}")),
        node("tr")(node("td")(f"{appinfo.date}")),
        node("tr", style="font-size: 0.7em;")(node("td", )(appinfo.code_id()))
    )
        
    return node("div", **{'class': "navbar"})(
        navh,
        when(ht.authenticated)(bar),
        when(not ht.authenticated)("Please log in!"),
        when(ht.admin)(appi)
    )

class Context(HtmlSource):
    def __init__(self, db: Session, user: User, messages: dict, **kwargs):
        self.db = db
        self.user = user
        self.admin = user is not None and user.role == "administrator"
        self.messages = messages
        self.__dict__.update(kwargs)

        self.authenticated = user is not None

        # convenience members
        self.br = node("br")()
    
    def url(self, route: str):
        """Make a correctly routed URL."""
        if route=="": return "/"
        parts = route.split("?")
        body = parts[0]
        pars = "?"+parts[1] if len(parts)>1 else ""
        return f"/{body}/{pars}"
        # if route=="": return f"{self.root}/"
        # return f"{self.root}/{route}/"
    
    # # shortcuts for nodes with no attributes
    # def head(ht, *xs: Html): return node("head")(*xs)
    # def title(ht, *xs: Html): return node("title")(*xs)
    # def body(ht, *xs: Html): return node("body")(*xs)
    # def div(ht, *xs: Html): return node("div")(*xs)
    # def label(ht, *xs: Html): return node("label")(*xs)
    # def h1(ht, *xs: Html): return node("h1")(*xs)
    # def h2(ht, *xs: Html): return node("h2")(*xs)
    # def h3(ht, *xs: Html): return node("h3")(*xs)
    # def ul(ht, *xs: Html): return node("ul")(*xs)
    # def li(ht, *xs: Html): return node("li")(*xs)
    # def p(ht, *xs: Html): return node("p")(*xs)

    # make a correctly routed link
    def link(ht, route: str): return node("a", href=ht.url(route)) 

    def header(ht, title: str):
        return HtmlString('<!DOCTYPE html>\n<html lang="en">') + \
        ht.head(
            '<meta charset="UTF-8">',
            '<meta http-equiv="X-UA-Compatible" content="IE=edge">',
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
            resources,
            ht.title(title)
        )

    
    def page(ht, title: str, *args: Html):
        #msgs = when(len(ht.messages) > 0)(
        #    ulist(*[HtmlString(m) for m in ht.messages])
        #)
        return ht.header(title) + ht.body(navbar(ht), node("div", **{'class': "main"})(*args))
    
    def error(ht, *args: Html):
        return ht.page("Error", ht.h1("Error: "), *args).pretty()
    
    def success(ht, *args: Html):
        return ht.page("Success", ht.h1("Success: "), *args).pretty()
    
    #
    #  Generic GUI forms. Should work for any ORM model.
    #    
    def rform(ht, route: str, rep: Rep, 
        default = None,
        submits: List[str] = ["Submit"], 
        tag: str = None,
        hidden: dict = None,
        method: str = "post"
    ):
        """Create a form for a model class. The form will be a table of labeled input fields."""
        log(f"Form2: {rep}")
        ffs = rep.form_fields(lambda cl: ht.get_items(cl), default=default)
        fs = []
        for name, input in ffs:
            if hasattr(rep[name], "_hidden"):
                fs.append(input)
            else:                                         
                fs.append(tablerow(node("label", **{'for': name})(name+":"), input))
        fn = node("form", method=method, action=ht.url(route))(
            node("table")(*fs, submitrow(names=submits))
        )
        if tag is not None:
            fn = fn(**{"class": tag})
        return fn

    def get_items(ht, model):
        """Get select options from the database for an enum model. 
           If there are no options for an enum model, add them."""
        xs = ht.db.query(model).all()
        if len(xs)==0 and hasattr(model, "__enum__"):
            raise ValueError(f"No items in enum {model.__name__}")
        return xs

    # transaction utilities...
    def transact(ht): return transact(ht.db)
    def flush(ht, o: Base): return flush(ht.db, o)
    
    def populate_well_pos(ht):
        wells = ht.db.query(WellPos).all()
        if len(wells) != 0:
            return
        for plate_format in ht.db.query(PlateFormat).all():
            for row in range(plate_format.row_count):
                for col in range(plate_format.col_count):
                    well_name = string.ascii_uppercase[row] + str(col +  1)
                    well_pos = WellPos(well_name=well_name, row=row, col=col, plate_format=plate_format.name)
                    ht.db.add(well_pos)
        ht.db.commit()

    # Populate the enum models if needed
    def populate_enums(ht):
        db_populate_enums(ht.db)
        ht.populate_well_pos()
        log(f"Done populating enum models.")
