from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Foreign<PERSON>ey
from base.database import Base

class UserRole(Base):
    __tablename__ = "user_role"
    __enum__ = [("administrator", True), ("member", False), ("bot", False)]
    name: Mapped[str] = mapped_column(primary_key=True)
    admin: Mapped[bool]

class User(Base):
    __tablename__ = 'user'
    name:     Mapped[str] = mapped_column(primary_key=True)
    email:    Mapped[str] = mapped_column(nullable=False)
    role:     Mapped[str] = mapped_column(ForeignKey("user_role.name"), nullable=False)
    password: Mapped[str] = mapped_column(nullable=False)
