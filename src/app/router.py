from fastapi import APIRouter, Depends, status
from fastapi.responses import HTMLResponse, RedirectResponse, Response
from base.database import transact
from base.util import make_logger
from app.layouts import Context
from base.reps import rep
from app.table import table
from app import html
from app.model import User, UserRole
from app.auth import (
    context, context_optional, query_user, hash_password, make_admin, pwd_context, manager, touch_users
)
from app.commands import Login, Reset, Signup, Change

log = make_logger(__name__)

router = APIRouter(prefix="/auth")

@router.get("/login/", response_class=HTMLResponse)
def auth_login(ht: Context = Depends(context_optional)):
    ht.populate_enums()
    return ht.page("login form", 
        ht.h2("Please Login:"), 
        ht.rform("auth/login", rep(Login))
    ).pretty()


@router.get("/change", response_class=HTMLResponse)
def auth_reset(ht: Context = Depends(context)):
    return ht.page("Change Password",
        ht.h2(f"Change Password for {ht.user.name}"),
        ht.rform("auth/change", rep(Change)),
    ).pretty()

@router.post("/change", response_class=HTMLResponse)
def auth_reset_post(form: Change = Depends(), ht: Context = Depends(context)):
    if not pwd_context.verify(form.old_password, ht.user.password):
        return ht.error("Old password is incorrect.", ht.link("auth/change")("Try again"))
    elif form.new_password != form.repeat_password:
        return ht.error("Repeat password doesn't match.", ht.link("auth/change")("Try again"))
    with transact(ht.db):
        dbu = ht.db.query(User).filter(User.name == ht.user.name).one()
        dbu.password = pwd_context.hash(form.new_password)
    touch_users()
    return ht.success(f"Password for {ht.user.name} changed successfully.")


@router.post("/login/")
def auth_login_post(login: Login = Depends(), ht: Context = Depends(context_optional)):
    log(f"Login: {login}")
    user = query_user(login.username)
    log(f"Given:  {login.password} {hash_password(login.password)}")
    if user: log(f"Stored: {user.password}")
    if not user or not pwd_context.verify(login.password, user.password):
        make_admin(ht.db)
        response = RedirectResponse(url="/auth/login",status_code=status.HTTP_302_FOUND)
    else:
        token = manager.create_access_token(data={'sub': login.username})
        response = RedirectResponse(url="/",status_code=status.HTTP_302_FOUND)
        manager.set_cookie(response, token)
    return response

@router.get("/logout/")
def logout(response : Response, ctx: Context = Depends(context)):
  response = RedirectResponse('/auth/login', status_code= 302)
  manager.set_cookie(response, "")
  return response

@router.get("/reset/", response_class=HTMLResponse)
def reset_password(ht: Context = Depends(context_optional)):
  return ht.page("login form", ht.rform("auth/reset", rep(Reset))).pretty()

@router.post("/reset/", response_class=HTMLResponse)
def reset_password_post(form: Reset = Depends(), ht: Context = Depends(context_optional)):
    log(f"Reset: {form}")
    user = ht.db.query(User).filter(User.name == form.username).one_or_none()
    if not user:
        return ht.error("User '{form.username}' not found.", ht.link("auth/reset")("Try again"))
    if form.new_password != form.repeat_password:
        return ht.error("Repeat password doesn't match.", ht.link("auth/reset")("Try again"))
    with transact(ht.db):
        user.password = hash_password(form.new_password)
    touch_users()
    return ht.success(f"Password for {user.name} reset successfully!", ht.link("")("Home"))

@router.get("/users/", response_class=HTMLResponse)
def auth_users(ht: Context = Depends(context)):
    users = ht.db.query(User).all()
    roles = ht.db.query(UserRole).all()
    form = ht.h2("Add a User:") + ht.rform("auth/users", rep(Signup).default("role", "member"))
    ret = ht.page(
        "List of Users", ht.h2("List of Users:"), table(users), ht.hr(), 
        "List of Roles", ht.h2("List of Roles:"), table(roles), ht.hr(), 
        form
    )
    return ret.pretty()

class SignupIn(rep(Signup).form_parser()): pass

@router.post("/users/")
def auth_users_post(signup: SignupIn = Depends(), ht: Context = Depends(context_optional)):
    log(f"Change: {signup}")
    user = query_user(signup.username)
    if user is None:
        with transact(ht.db):
            ht.db.add(User(
                name=signup.username,
                email=signup.email,
                password=hash_password(signup.password),
                role = signup.role
            ))
        touch_users()
        log(f"New:  {signup.password} -> {hash_password(signup.password)}")
    return RedirectResponse(url="/auth/users",status_code=status.HTTP_302_FOUND)

