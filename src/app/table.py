from typing import Callable
from dataclasses import dataclass, is_dataclass
from sqlalchemy.engine.row import Row
from typing import List, Any
from dataclasses import fields
from base.database import Base
from app.html import Html, node, HtmlString
from base.reps import Rep, rep
from base.util import make_logger
log = make_logger(__name__)

def field_names(obj) -> List[str]:
    """Get the field names from a list of objects."""
    fs = []
    if type(obj) is Row: 
        for o in obj: fs += field_names(o)
    if is_dataclass(obj): fs = fields(obj)
    elif isinstance(obj, dict): fs = obj.keys()
    elif isinstance(obj, Base): fs = [c.key for c in obj.__class__.__table__.columns]
    elif hasattr(obj, "__dict__"): fs = obj.__dict__.keys()
    else: fs = ["Value"]
    return fs

def reptable(rep: Rep, data: List[Any], status: Callable[[Any],str] = None) -> Html:
    fs = rep.fields.keys()
    log(f"Rep Table: {rep} {fs}")
    #log(f"Data[0]: {data[0]}")
    def trnode(d): 
        if status is None or status(d) is None: return node("tr")
        else: return node("tr", status=status(d))
    header = node("thead")(node("tr")(*[node("th")(f) for f in fs]))
    rows = node("tbody")(*[trnode(d)(*[node("td")(rep.htf(f, d)) for f in fs]) for d in data])
    return node("table", id="example", **{'class': 'stripe'})(header, rows) # style="width:100%", 
    
    
def table(data: List[Any]) -> Html:
    """Create an HTML table from a list of objects."""
    if not isinstance(data, List):
        if not isinstance(data, dict):
            log(f"Data is not a list or dict: {data}")
            dr  = rep(type(data))
            data = {f: dr.htf(f, data) for f in dr.fields.keys()}
            #data = {k: v for k, v in data.__dict__.items() if not k.startswith("_")}
        data = [dict(zip(["Field", "Value"], row)) for row in data.items()]
    if len(data)==0: return node("div")("No data")
    fs = field_names(data[0])
    #log(f"Data: {data} {fs}")
    def field(d, f):
        if type(d) is Row: 
            for di in d: 
                o = field(di, f)
                if o is not None: return o
            return None
        if isinstance(d, dict): return d.get(f)
        if hasattr(d, f): return getattr(d, f)
        return None
    header = node("thead")(node("tr")(*[node("th")(f) for f in fs]))
    rows = node("tbody")(*[node("tr")(*[node("td")(str(field(d, f))) for f in fs]) for d in data])
    return node("table", id="example", **{'class': 'stripe'})(header, rows) # style="width:100%", 

# editable: https://stackoverflow.com/questions/73082579/editable-table-cells-html

