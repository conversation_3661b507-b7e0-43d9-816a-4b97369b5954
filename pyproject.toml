[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
dynamic = ["dependencies"]
name = "mellitos"
version = "1.1"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
  { name="<PERSON>", email="<EMAIL>" },
  { name="Shrey<PERSON> Rai", email="<EMAIL>" },
]
description = "The MelliCell Operating System"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["base", "images", "app"]

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[project.scripts]
differentiation = "images.diff_run:main"
