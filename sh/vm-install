#
#  Enable scrolling in screen
#
cat <<+++ > ~/.screenrc
defscrollback 5000
termcapinfo xterm* ti@:te@
+++
#
#  Set timezone
#
sudo timedatectl set-timezone America/New_York
#
# This is run on a Debian VM to installl Docker.
#
sudo apt-get update
sudo apt-get install -y ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/debian/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc
#
echo Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/debian \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

echo Add the user to the docker group
sudo usermod -a -G docker andreas
#newgrp docker
#
echo Install the Google Cloud SQL Proxy
#
curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.14.0/cloud-sql-proxy.linux.amd64
chmod +x cloud-sql-proxy
#
echo Install Postgres
#
sudo apt install -y postgresql-client
#
echo "Install the Java JDK (needed for bioformats)"
#
sudo apt install -y default-jdk
#
echo Install python stuff
#
sudo apt install -y python3.11-venv
#
echo "Install javabridge (needed for bioformats)"
#
#  All this is needed for javabridge, which runs only on Python 3.10
sudo apt install -y gcc build-essential libssl-dev zlib1g-dev libbz2-dev libreadline-dev \
    libsqlite3-dev curl git libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev \
    libffi-dev liblzma-dev
curl https://pyenv.run | bash
echo 'export PATH="$HOME/.pyenv/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(pyenv init -)"' >> ~/.bashrc
echo 'eval "$(pyenv virtualenv-init -)"' >> ~/.bashrc
export PATH="$HOME/.pyenv/bin:$PATH"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"

pyenv install 3.10
pyenv shell 3.10
python -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install python-javabridge
pip install -r requirements.txt
pip install -e .

source .env

echo Add the user to the docker group
sudo usermod -a -G docker $USER
newgrp docker

echo All done!
