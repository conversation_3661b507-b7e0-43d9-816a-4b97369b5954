#
# update the local db from the Cloud Sql development database
#   assumes the local database server is up and running at port 5432
#
# credentials from secrets.json
SECRETS_FILE=".secrets/secrets.json"
if [ ! -f ${SECRETS_FILE} ]; then
  echo "No secrets.json file found. Please create the file .secrets/secrets.json with database credentials."
  exit 1
fi

export SRC_PASS=$(cat ${SECRETS_FILE} | jq -r '.database.development.password')
export DST_PASS=$(cat ${SECRETS_FILE} | jq -r '.database.local.password')

#
TMP=cache.sql
if [ ! -f ${TMP} ]; then
  echo '\n*' Pipe data from dev to cache file
  echo '\n*' Start the cloud sql proxy on port 5435
  ./cloud-sql-proxy development-311316:us-east1:mellitos-dev --port 5435 &
  proxyPID=$!
  sleep 3
  PGPASSWORD=${SRC_PASS} pg_dump --port=5435 --host=0.0.0.0 --dbname=app --username=app > ${TMP}
  # kill the proxy
  kill $proxyPID
fi
#
echo '\n*' Initialize Local Database
PGPASSWORD=${DST_PASS} psql --port=5432 --host=0.0.0.0 --dbname template1 --username=app << +++
DROP DATABASE IF EXISTS app;
CREATE DATABASE app;
+++
#
LOG=reset.log
echo '\n*' Loading data from temp file to local database "(log in ${LOG})"
echo '     (there will be a bunch of "role ... not exist" errors that apparently do not matter)'
( cat ${TMP} | \
    PGPASSWORD=${DST_PASS} psql --port=5432 --host=0.0.0.0 --dbname=app --username=app ) 2>&1 > ${LOG}
# echo '\n*' Pipe data from dev to local "(log in ${LOG})"
# echo '     (there will be a bunch of "role ... not exist" errors that apparently do not matter)'
# ( PGPASSWORD=${SRC_PASS} pg_dump --port=5435 --host=0.0.0.0 --dbname=app --username=app | \
#     PGPASSWORD=${DST_PASS} psql --port=5432 --host=0.0.0.0 --dbname=app --username=app ) 2>&1 > ${LOG}
#
#echo Synchronize buckets:
#gsutil rsync -r gs://mellitos-assay assay
#gsutil rsync -r gs://mellitos-assay gs://mellitos-assay-dev
echo Done.

