#Shell script for configuring Python environment before venv and packages

# Install pyenv if not already installed
# brew install pyenv

# Add to shell config
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(pyenv init -)"' >> ~/.zshrc

# Reload shell config
source ~/.zshrc

# Install and set Python 3.10
pyenv install 3.10
pyenv global 3.10

python --version