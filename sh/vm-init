host=$1
echo Host: $host
echo "\n*Apt"
gcloud compute ssh --command "sudo apt-get update && sudo apt-get install -y git" $host
echo "\n*Keys"
(cd; gcloud compute scp --recurse .ssh/id_ed25519* $host:.ssh)
gcloud compute ssh --command 'eval "$(ssh-agent -s)"' $host
echo "\n*Git"
gcloud compute ssh --command "curl --silent https://api.github.com/meta | jq --raw-output '\"github.com \"+.ssh_keys[]' >> ~/.ssh/known_hosts" $host
gcloud compute ssh --command "rm -rf mellitos && <NAME_EMAIL>:MelliCell/mellitos.git" $host
echo "\n*Env"
gcloud compute scp .env $host:mellitos/.env
gcloud compute scp --recurse .secrets .pass.* $host:mellitos/
gcloud compute ssh --command "cd mellitos && source sh/vm-install" $host
