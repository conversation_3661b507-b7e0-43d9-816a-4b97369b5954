# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Java not found. Installing OpenJDK..."
    brew install openjdk@17
else
    echo "Found Java:"
    java -version
fi

# Create symbolic link (might need sudo)
if [ ! -d "/Library/Java/JavaVirtualMachines/openjdk.jdk" ]; then
    echo "Creating symbolic link for Java..."
    sudo ln -sfn $(brew --prefix)/opt/openjdk/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk.jdk
fi

# Set up Java environment variables
echo "Setting up Java environment variables..."
export JAVA_HOME=$(/usr/libexec/java_home)
export PATH="$JAVA_HOME/bin:$PATH"

# Add to shell config if not already present
if ! grep -q "JAVA_HOME" ~/.zshrc; then
    echo 'export JAVA_HOME=$(/usr/libexec/java_home)' >> ~/.zshrc
    echo 'export PATH="$JAVA_HOME/bin:$PATH"' >> ~/.zshrc
fi

# Verify setup
echo "Java setup complete:"
echo "JAVA_HOME: $JAVA_HOME"
java -version

echo "Please run 'source ~/.zshrc' to apply changes to your current shell"