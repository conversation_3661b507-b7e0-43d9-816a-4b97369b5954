#
# Use this script to build and deply the app container. It ensures the appinfo is available.
#
python src/base/appinfo.py > src/base/_appinfo.py
cat src/base/_appinfo.py
docker build -f dockerfile.app -t us-east1-docker.pkg.dev/development-311316/dev-repo/mellitos-dev:latest --platform linux/amd64 .
docker push us-east1-docker.pkg.dev/development-311316/dev-repo/mellitos-dev:latest
rm src/base/_appinfo.py  # Remove the temporary appinfo file.

