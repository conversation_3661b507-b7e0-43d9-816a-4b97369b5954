# Current Imaging Pipelines: Tale of Two Systems

## Pipeline Comparison

```mermaid
graph TB
    subgraph "🟢 Cytosmart Pipeline: WORKING"
        A1[Cytosmart Omni<br/>Lab Imager] -->|Custom Software<br/>Auto-Export| A2[GCS Bucket<br/>cytosmart_brightfield_images]
        A2 -->|Cloud Function<br/>Trigger| A3[BF Analysis Pipeline<br/>Docker Container]
        A3 -->|Cellpose Segmentation<br/>Cell/Cluster Analysis| A4[BigQuery Tables<br/>+ mellitos DB]
        A5[❌ Separate Repository<br/>Outside MellitOS] -.-> A3
    end
    
    subgraph "🔴 Olympus Pipeline: INCOMPLETE"
        B1[Olympus IX83<br/>Lab Imager] -->|Windows Service<br/>OlympusUploadService| B2[GCS Bucket<br/>ix83-raw-files]
        B2 -->|Airflow DAGs<br/>Complex Orchestration| B3[3 Worker MIGs<br/>Convert → Segment → Measure]
        B3 -->|VSI→TIFF→Masks→CSV| B4[CSV Files<br/>+ External Tables]
        B5[❌ NO Brightfield Analysis<br/>Missing Cell/Cluster Data] -.-> B3
    end
    
    style A1 fill:#ccffcc
    style A2 fill:#ccffcc
    style A3 fill:#ccffcc
    style A4 fill:#ccffcc
    style A5 fill:#ffcccc
    style B1 fill:#ffffcc
    style B2 fill:#ffffcc
    style B3 fill:#ffffcc
    style B4 fill:#ffffcc
    style B5 fill:#ffcccc
```

## Key Differences

| **Aspect** | **Cytosmart** | **Olympus** |
|------------|---------------|-------------|
| **Brightfield Analysis** | ✅ Active cell/cluster counting | ❌ **Missing entirely** |
| **Architecture** | Simple: Imager → Function → Analysis | Complex: Imager → 3 MIGs + Airflow |
| **Code Location** | ❌ Separate repository | ❌ Multiple repositories |
| **Data Output** | BigQuery tables | CSV files |
| **Reliability** | ✅ Works consistently | ⚠️ Complex failure points |

## The Problem

### **Data Gap Crisis**
- When Cytosmart imager goes down → **No brightfield data available**
- Olympus images processed but **no biological insights extracted**
- QC analysis in MellitOS relies on Cytosmart data only

### **Maintenance Nightmare**
- **Cytosmart**: Code in separate repo, hard to update
- **Olympus**: Complex Airflow orchestration, multiple failure points
- **Both**: Different storage patterns, inconsistent approaches

---

*Result: We have two half-solutions instead of one complete system*
