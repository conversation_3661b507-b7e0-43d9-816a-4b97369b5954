FROM python:3.11

WORKDIR /code

RUN apt-get update
RUN apt install default-jdk -y

COPY requirements.txt /code/requirements.txt

RUN --mount=type=secret,id=credentials.json,target=/code/credentials.json
RUN pip install --upgrade pip
RUN pip install --upgrade -r /code/requirements.txt

# ENV PYTHONPATH=.

COPY ./src /code/src/
COPY ./pyproject.toml /code/
COPY ./static /code/static/

RUN pip install /code

CMD uvicorn app.main:app --host=0.0.0.0 --port=$PORT --workers=3
# ENTRYPOINT ["python3", "api/main.py"]
