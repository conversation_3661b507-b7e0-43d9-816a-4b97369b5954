services:
  app:
    build:
      context: .
      dockerfile: ./dockerfile.app
    command: bash -c 'while !</dev/tcp/db/5432; do sleep 1; done; uvicorn app.main:app --host 0.0.0.0 --port 5001 --workers=3'
    volumes:
      - ./api:/api/
      - ./.secrets:/run/secrets/.secrets
    ports:
      - 5001:5001
    environment:
      - PORT=5001
      - ENVIRONMENT=dev
      - DBHOST=db
      - DBPORT=5432
      - DBUSER=app
      - DBPASS=pass
      - DBNAME=app
      - DRIVERNAME=postgresql+pg8000
      - SECRETS=/run/secrets
      - GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/.secrets/development.json
      - TZ=America/New_York
    depends_on:
      db:
        condition: service_healthy
    networks:
      - main
    secrets:
      - .secrets
  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - 5432:5432
    environment:
      - POSTGRES_USER=app
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=app
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app -d app"]
      interval: 5s
      timeout: 10s
      retries: 5
    networks:
      - main
networks:
  main:
    name: main
secrets:
  .secrets:
    file: .secrets
volumes:
  postgres_data:

