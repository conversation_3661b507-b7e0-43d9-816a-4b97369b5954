"""revised pipeline request orm models

Revision ID: 832118c19a17
Revises: 35e0cb28553c
Create Date: 2024-07-16 16:38:12.688381

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '832118c19a17'
down_revision: Union[str, None] = '35e0cb28553c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pipeline_goal',
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('name')
    )
    op.create_table('pipeline_request',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_pipeline', sa.Integer(), nullable=False),
    sa.Column('check_url', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('result_set',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_set', sa.Integer(), nullable=False),
    sa.Column('pipeline_request', sa.Integer(), nullable=False),
    sa.Column('image_pipeline', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], ),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], ),
    sa.ForeignKeyConstraint(['pipeline_request'], ['pipeline_request.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('image_mask',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_id', sa.Integer(), nullable=False),
    sa.Column('result_set', sa.Integer(), nullable=False),
    sa.Column('image_scale_factor', sa.Float(), nullable=False),
    sa.Column('mask_uri', sa.String(), nullable=False),
    sa.Column('csv_uri', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['image_id'], ['image.id'], ),
    sa.ForeignKeyConstraint(['result_set'], ['result_set.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_constraint('image_pipeline_goal_fkey', 'image_pipeline', type_='foreignkey')
    op.drop_constraint('segmentation_mask_mask_set_fkey', 'segmentation_mask', type_='foreignkey')
    op.drop_table('segmentation_goal')
    op.drop_table('mask_set')
    op.drop_table('segmentation_mask')
    op.create_foreign_key(None, 'image_pipeline', 'pipeline_goal', ['goal'], ['name'])
    op.drop_column('image_pipeline', 'scale_factor')
    op.add_column('pipeline_version', sa.Column('template_path', sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('pipeline_version', 'template_path')
    op.add_column('image_pipeline', sa.Column('scale_factor', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'image_pipeline', type_='foreignkey')
    op.create_foreign_key('image_pipeline_goal_fkey', 'image_pipeline', 'segmentation_goal', ['goal'], ['name'])
    op.create_table('segmentation_mask',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('image', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('object_uri', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('size_x', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('size_y', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('mask_set', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['image'], ['image.id'], name='segmentation_mask_image_fkey'),
    sa.ForeignKeyConstraint(['mask_set'], ['mask_set.id'], name='segmentation_mask_mask_set_fkey'),
    sa.PrimaryKeyConstraint('id', name='segmentation_mask_pkey'),
    sa.UniqueConstraint('object_uri', name='segmentation_mask_object_uri_key')
    )
    op.create_table('mask_set',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('image_set', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('image_pipeline', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], name='mask_set_image_pipeline_fkey'),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], name='mask_set_image_set_fkey'),
    sa.PrimaryKeyConstraint('id', name='mask_set_pkey')
    )
    op.create_table('segmentation_goal',
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('name', name='segmentation_goal_pkey')
    )
    op.drop_table('image_mask')
    op.drop_table('result_set')
    op.drop_table('pipeline_request')
    op.drop_table('pipeline_goal')
    # ### end Alembic commands ###
