"""Add day column

Revision ID: f37385415359
Revises: f7e92df941b0
Create Date: 2024-11-03 09:56:49.026549

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f37385415359'
down_revision: Union[str, None] = 'f7e92df941b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('image_set', sa.Column('day', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('image_set', 'day')
    # ### end Alembic commands ###
