"""Assay entity

Revision ID: 69e725b1bf05
Revises: bb16268111c5
Create Date: 2024-05-08 18:44:10.571209

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '69e725b1bf05'
down_revision: Union[str, None] = 'bb16268111c5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('assay',
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=False),
        sa.Column('created', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('name')
    )
    op.add_column('assay_in', sa.Column('assay', sa.String(), nullable=False))
    op.create_foreign_key(None, 'assay_in', 'assay', ['assay'], ['name'])


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'assay_in', type_='foreignkey')
    op.drop_column('assay_in', 'assay')
    op.drop_table('assay')
    # ### end Alembic commands ###
