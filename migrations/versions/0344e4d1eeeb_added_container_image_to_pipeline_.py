"""added container image to pipeline versions

Revision ID: 0344e4d1eeeb
Revises: 335cbb77375d
Create Date: 2024-07-30 14:26:40.591065

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0344e4d1eeeb'
down_revision: Union[str, None] = '335cbb77375d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('pipeline_version', sa.Column('container_image', sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('pipeline_version', 'container_image')
    # ### end Alembic commands ###
