"""Add created column to Donor

Revision ID: 0d30dae79d95
Revises: 68046b0de89b
Create Date: 2024-11-09 11:37:59.970725

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import base

# revision identifiers, used by Alembic.
revision: str = '0d30dae79d95'
down_revision: Union[str, None] = '68046b0de89b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('donor', sa.Column('created', base.database.UtcDateTime(), nullable=True))


def downgrade() -> None:
    op.drop_column('donor', 'created')
