"""added version numbe to image

Revision ID: c75c552256d3
Revises: 6f49a63f167c
Create Date: 2024-05-17 10:02:35.829029

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c75c552256d3'
down_revision: Union[str, None] = '6f49a63f167c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('image', sa.Column('version', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('image', 'version')
    # ### end Alembic commands ###
