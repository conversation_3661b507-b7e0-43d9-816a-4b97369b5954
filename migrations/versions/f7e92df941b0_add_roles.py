"""Add roles

Revision ID: f7e92df941b0
Revises: 69f9a4ffde7a
Create Date: 2024-10-26 21:32:38.545144

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f7e92df941b0'
down_revision: Union[str, None] = '69f9a4ffde7a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('role', sa.String(), nullable=True))
    op.execute("UPDATE public.user SET role = 'member'")
    op.execute("UPDATE public.user SET role = 'administrator' WHERE admin = TRUE")
    op.alter_column('user', 'role', nullable=False)
    op.drop_constraint('user_email_key', 'user', type_='unique')
    op.create_foreign_key(None, 'user', 'user_role', ['role'], ['name'])
    op.drop_column('user', 'admin')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('admin', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.execute("UPDATE public.user SET admin = FALSE")
    op.execute("UPDATE public.user SET admin = TRUE WHERE role = 'administrator'")
    op.alter_column('user', 'admin', nullable=False)
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.create_unique_constraint('user_email_key', 'user', ['email'])
    op.drop_column('user', 'role')
    # ### end Alembic commands ###
