"""Bot worker table

Revision ID: 9fad89fcc1b6
Revises: 335cbb77375d
Create Date: 2024-07-26 18:03:57.802176

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9fad89fcc1b6'
down_revision: Union[str, None] = '335cbb77375d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('worker',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('started', sa.DateTime(), nullable=False),
        sa.Column('last', sa.DateTime(), nullable=False),
        sa.Column('finished', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('worker')
    # ### end Alembic commands ###
