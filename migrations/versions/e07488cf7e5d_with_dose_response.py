"""with dose response

Revision ID: e07488cf7e5d
Revises: 604d9c4a1e8c
Create Date: 2024-03-15 19:07:37.793696

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e07488cf7e5d'
down_revision: Union[str, None] = '604d9c4a1e8c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dose', sa.Column('id', sa.Integer(), nullable=False))
    op.add_column('dose', sa.Column('well_pos_id', sa.Integer(), nullable=False))
    op.add_column('dose', sa.Column('map_group_id', sa.Integer(), nullable=False))
    op.add_column('dose', sa.Column('concentration__value', sa.Float(), nullable=False))
    op.add_column('dose', sa.Column('concentration__unit', sa.String(), nullable=False))
    op.drop_constraint('dose_well_id_fkey', 'dose', type_='foreignkey')
    op.drop_constraint('dose_experiment_id_fkey', 'dose', type_='foreignkey')
    op.drop_constraint('dose_reagent_id_fkey', 'dose', type_='foreignkey')
    op.create_foreign_key(None, 'dose', 'concentration_unit', ['concentration__unit'], ['name'])
    op.create_foreign_key(None, 'dose', 'well_pos', ['well_pos_id'], ['id'])
    op.create_foreign_key(None, 'dose', 'map_group', ['map_group_id'], ['id'])
    op.drop_column('dose', 'reagent_id')
    op.drop_column('dose', 'experiment_id')
    op.drop_column('dose', 'dose_id')
    op.drop_column('dose', 'concentration')
    op.drop_column('dose', 'added')
    op.drop_column('dose', 'group_type')
    op.drop_column('dose', 'concentration_unit')
    op.drop_column('dose', 'well_id')
    op.create_unique_constraint(None, 'medium', ['name'])
    op.add_column('plate_format', sa.Column('row_count', sa.Integer(), nullable=True))
    op.add_column('plate_format', sa.Column('col_count', sa.Integer(), nullable=True))
    op.create_unique_constraint(None, 'vendor', ['name'])
    op.drop_table('reagent')
    op.drop_table('experiment')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'vendor', type_='unique')
    op.drop_column('plate_format', 'col_count')
    op.drop_column('plate_format', 'row_count')
    op.drop_constraint(None, 'medium', type_='unique')
    op.add_column('dose', sa.Column('well_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('concentration_unit', postgresql.ENUM('uM', 'nM', name='concentrationunit'), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('group_type', postgresql.ENUM('positive', 'negative', 'test', name='group'), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('added', postgresql.TIMESTAMP(), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('concentration', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('dose_id', sa.INTEGER(), autoincrement=True, nullable=False))
    op.add_column('dose', sa.Column('experiment_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('dose', sa.Column('reagent_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'dose', type_='foreignkey')
    op.drop_constraint(None, 'dose', type_='foreignkey')
    op.drop_constraint(None, 'dose', type_='foreignkey')
    op.create_foreign_key('dose_reagent_id_fkey', 'dose', 'reagent', ['reagent_id'], ['reagent_id'])
    op.create_foreign_key('dose_experiment_id_fkey', 'dose', 'experiment', ['experiment_id'], ['experiment_id'])
    op.create_foreign_key('dose_well_id_fkey', 'dose', 'well', ['well_id'], ['well_id'])
    op.drop_column('dose', 'concentration__unit')
    op.drop_column('dose', 'concentration__value')
    op.drop_column('dose', 'map_group_id')
    op.drop_column('dose', 'well_pos_id')
    op.drop_column('dose', 'id')
    op.create_table('experiment',
    sa.Column('experiment_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('experiment_type', postgresql.ENUM('dose_response', 'screening', name='experimenttype'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('experiment_id', name='experiment_pkey')
    )
    op.create_table('reagent',
    sa.Column('reagent_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('mcl_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('reagent_type', postgresql.ENUM('small_molecule', 'sirna', name='reagenttype'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('reagent_id', name='reagent_pkey'),
    sa.UniqueConstraint('mcl_id', name='reagent_mcl_id_key'),
    sa.UniqueConstraint('name', name='reagent_name_key')
    )
    # ### end Alembic commands ###
