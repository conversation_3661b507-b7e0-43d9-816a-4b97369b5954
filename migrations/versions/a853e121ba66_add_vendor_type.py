"""add vendor type

Revision ID: a853e121ba66
Revises: c75c552256d3
Create Date: 2024-06-25 18:05:15.515706

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a853e121ba66'
down_revision: Union[str, None] = 'c75c552256d3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('vendor', sa.Column('type', sa.String(), nullable=True))


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('vendor', 'type')
    # ### end Alembic commands ###
