"""added channels for pipelines in orm

Revision ID: 2240bd062551
Revises: 832118c19a17
Create Date: 2024-07-17 09:56:11.830789

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2240bd062551'
down_revision: Union[str, None] = '832118c19a17'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pipeline_channel',
    sa.Column('image_pipeline', sa.Integer(), nullable=True),
    sa.Column('image_channel', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['image_channel'], ['channel.name'], ),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], )
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('pipeline_channel')
    # ### end Alembic commands ###
