"""gender type to gender table and some user and images model edits

Revision ID: dba0331fbcf1
Revises: 
Create Date: 2024-04-23 10:29:50.530477

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'dba0331fbcf1'
down_revision: Union[str, None] = '01f9c69c76e8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('channel',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('wavelength', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    schema='public'
    )
    
    
    op.alter_column('donor', 'gender',
               existing_type=postgresql.ENUM('male', 'female', name='gender'),
               type_=sa.String(),
               existing_nullable=False)
               
    op.execute("DROP TYPE gender;")
    
    op.create_table('gender',
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('name'),
    schema='public'
    )
    
    
    op.execute("INSERT INTO gender VALUES ('male'), ('female');")
    
    
    op.create_table('segmentation_goal',
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('name'),
    schema='public'
    )
    op.create_table('sirna',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('gene', sa.String(), nullable=False),
    sa.Column('barcode', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('barcode'),
    schema='public'
    )
    op.create_table('small_molecule',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('smiles', sa.String(), nullable=True),
    sa.Column('barcode', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('barcode'),
    sa.UniqueConstraint('smiles'),
    schema='public'
    )
    op.create_table('user_role',
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('admin', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('name'),
    schema='public'
    )
    op.create_table('segmentation_model',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('goal', sa.String(), nullable=False),
    sa.Column('scale_factor', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['goal'], ['public.segmentation_goal.name'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    schema='public'
    )
    op.create_table('supernatent_plate',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('barcode', sa.Integer(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=False),
    sa.Column('source_plate', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['source_plate'], ['public.plate.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('barcode'),
    sa.UniqueConstraint('name'),
    schema='public'
    )
    op.create_table('image_channel',
    sa.Column('image_id', sa.Integer(), nullable=True),
    sa.Column('channel_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['channel_id'], ['public.channel.id'], ),
    sa.ForeignKeyConstraint(['image_id'], ['public.image.id'], ),
    schema='public'
    )
    op.create_table('segmentation_mask',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image', sa.Integer(), nullable=False),
    sa.Column('object_uri', sa.String(), nullable=False),
    sa.Column('model', sa.Integer(), nullable=False),
    sa.Column('size_x', sa.Integer(), nullable=False),
    sa.Column('size_y', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image'], ['public.image.id'], ),
    sa.ForeignKeyConstraint(['model'], ['public.segmentation_model.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('object_uri'),
    schema='public'
    )
    op.drop_table('olympus_lipid_mask')
    op.drop_table('plate_growth_condition')
    op.drop_table('olympus_image')
    op.drop_table('growth_protocol')
    op.drop_table('well')
    op.drop_constraint('action_step_fkey', 'action', type_='foreignkey')
    op.drop_constraint('action_station_fkey', 'action', type_='foreignkey')
    op.drop_constraint('action_sop_fkey', 'action', type_='foreignkey')
    op.drop_constraint('action_operator_fkey', 'action', type_='foreignkey')
    op.drop_constraint('action_project_fkey', 'action', type_='foreignkey')
    op.create_foreign_key(None, 'action', 'sop', ['sop'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'action', 'station', ['station'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'action', 'project', ['project'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'action', 'user', ['operator'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'action', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    
    op.drop_constraint('donor_vendor_fkey', 'donor', type_='foreignkey')
    op.drop_constraint('donor_race_fkey', 'donor', type_='foreignkey')
    op.create_foreign_key(None, 'donor', 'race', ['race'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'donor', 'vendor', ['vendor'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'donor', 'gender', ['gender'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('dose_map_group_id_fkey', 'dose', type_='foreignkey')
    op.drop_constraint('dose_well_pos_id_fkey', 'dose', type_='foreignkey')
    op.drop_constraint('dose_concentration__unit_fkey', 'dose', type_='foreignkey')
    op.create_foreign_key(None, 'dose', 'concentration_unit', ['concentration__unit'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'dose', 'map_group', ['map_group_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'dose', 'well_pos', ['well_pos_id'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('feed_in_request_fkey', 'feed_in', type_='foreignkey')
    op.drop_constraint('feed_in_medium_fkey', 'feed_in', type_='foreignkey')
    op.drop_constraint('feed_in_plate_fkey', 'feed_in', type_='foreignkey')
    op.create_foreign_key(None, 'feed_in', 'plate', ['plate'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'feed_in', 'medium', ['medium'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'feed_in', 'request', ['request'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('image_well_pos_fkey', 'image', type_='foreignkey')
    op.drop_constraint('image_image_set_fkey', 'image', type_='foreignkey')
    op.create_foreign_key(None, 'image', 'well_pos', ['well_pos'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'image', 'image_set', ['image_set'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('image_in_plate_fkey', 'image_in', type_='foreignkey')
    op.drop_constraint('image_in_device_fkey', 'image_in', type_='foreignkey')
    op.drop_constraint('image_in_request_fkey', 'image_in', type_='foreignkey')
    op.create_foreign_key(None, 'image_in', 'request', ['request'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'image_in', 'plate', ['plate'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'image_in', 'device', ['device'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('image_set_device_fkey', 'image_set', type_='foreignkey')
    op.drop_constraint('image_set_plate_fkey', 'image_set', type_='foreignkey')
    op.create_foreign_key(None, 'image_set', 'device', ['device'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'image_set', 'plate', ['plate'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('map_group_plate_map_id_fkey', 'map_group', type_='foreignkey')
    op.drop_constraint('map_group_group_type_fkey', 'map_group', type_='foreignkey')
    op.create_foreign_key(None, 'map_group', 'group_type', ['group_type'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'map_group', 'plate_map', ['plate_map_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_unique_constraint(None, 'medium', ['name'], schema='public')
    op.drop_constraint('plate_format_fkey', 'plate', type_='foreignkey')
    op.drop_constraint('plate_type_fkey', 'plate', type_='foreignkey')
    op.drop_constraint('plate_vendor_fkey', 'plate', type_='foreignkey')
    op.create_foreign_key(None, 'plate', 'plate_format', ['format'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'plate', 'plate_type', ['type'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'plate', 'vendor', ['vendor'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('plate_map_format_name_fkey', 'plate_map', type_='foreignkey')
    op.create_foreign_key(None, 'plate_map', 'plate_format', ['format_name'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('plate_map_assignment_map_group_id_fkey', 'plate_map_assignment', type_='foreignkey')
    op.drop_constraint('plate_map_assignment_treatment_id_fkey', 'plate_map_assignment', type_='foreignkey')
    op.drop_constraint('plate_map_assignment_treatment_regiment_id_fkey', 'plate_map_assignment', type_='foreignkey')
    op.create_foreign_key(None, 'plate_map_assignment', 'treatment_regiment', ['treatment_regiment_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'plate_map_assignment', 'treatment', ['treatment_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'plate_map_assignment', 'map_group', ['map_group_id'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('request_action_fkey', 'request', type_='foreignkey')
    op.drop_constraint('request_project_fkey', 'request', type_='foreignkey')
    op.drop_constraint('request_step_fkey', 'request', type_='foreignkey')
    op.create_foreign_key(None, 'request', 'action', ['action'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'request', 'project', ['project'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'request', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('result_id_fkey', 'result', type_='foreignkey')
    op.drop_constraint('result_result_fkey', 'result', type_='foreignkey')
    op.create_foreign_key(None, 'result', 'action', ['id'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'result', 'result_reason', ['result'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('result_reason_step_fkey', 'result_reason', type_='foreignkey')
    op.create_foreign_key(None, 'result_reason', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('seed_in_request_fkey', 'seed_in', type_='foreignkey')
    op.drop_constraint('seed_in_plate_fkey', 'seed_in', type_='foreignkey')
    op.drop_constraint('seed_in_donor_fkey', 'seed_in', type_='foreignkey')
    op.create_foreign_key(None, 'seed_in', 'request', ['request'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'seed_in', 'donor', ['donor'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'seed_in', 'plate', ['plate'], ['id'], source_schema='public', referent_schema='public')
    op.drop_constraint('sop_step_fkey', 'sop', type_='foreignkey')
    op.create_foreign_key(None, 'sop', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('sop_param_step_fkey', 'sop_param', type_='foreignkey')
    op.create_foreign_key(None, 'sop_param', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('sop_param_value_sop_fkey', 'sop_param_value', type_='foreignkey')
    op.drop_constraint('sop_param_value_param_fkey', 'sop_param_value', type_='foreignkey')
    op.create_foreign_key(None, 'sop_param_value', 'sop', ['sop'], ['name'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'sop_param_value', 'sop_param', ['param'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('station_step_fkey', 'station', type_='foreignkey')
    op.create_foreign_key(None, 'station', 'step', ['step'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('treatment_type_fkey', 'treatment', type_='foreignkey')
    op.create_foreign_key(None, 'treatment', 'treatment_type', ['type'], ['name'], source_schema='public', referent_schema='public')
    op.drop_constraint('treatment_regiment_plate_map_id_fkey', 'treatment_regiment', type_='foreignkey')
    op.drop_constraint('treatment_regiment_plate_id_fkey', 'treatment_regiment', type_='foreignkey')
    op.create_foreign_key(None, 'treatment_regiment', 'plate', ['plate_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_foreign_key(None, 'treatment_regiment', 'plate_map', ['plate_map_id'], ['id'], source_schema='public', referent_schema='public')
    op.create_unique_constraint(None, 'vendor', ['name'], schema='public')
    op.drop_constraint('well_pos_plate_format_fkey', 'well_pos', type_='foreignkey')
    op.create_foreign_key(None, 'well_pos', 'plate_format', ['plate_format'], ['name'], source_schema='public', referent_schema='public')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'well_pos', schema='public', type_='foreignkey')
    op.create_foreign_key('well_pos_plate_format_fkey', 'well_pos', 'plate_format', ['plate_format'], ['name'])
    op.drop_constraint(None, 'vendor', schema='public', type_='unique')
    op.drop_constraint(None, 'treatment_regiment', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'treatment_regiment', schema='public', type_='foreignkey')
    op.create_foreign_key('treatment_regiment_plate_id_fkey', 'treatment_regiment', 'plate', ['plate_id'], ['id'])
    op.create_foreign_key('treatment_regiment_plate_map_id_fkey', 'treatment_regiment', 'plate_map', ['plate_map_id'], ['id'])
    op.drop_constraint(None, 'treatment', schema='public', type_='foreignkey')
    op.create_foreign_key('treatment_type_fkey', 'treatment', 'treatment_type', ['type'], ['name'])
    op.drop_constraint(None, 'station', schema='public', type_='foreignkey')
    op.create_foreign_key('station_step_fkey', 'station', 'step', ['step'], ['name'])
    op.drop_constraint(None, 'sop_param_value', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'sop_param_value', schema='public', type_='foreignkey')
    op.create_foreign_key('sop_param_value_param_fkey', 'sop_param_value', 'sop_param', ['param'], ['name'])
    op.create_foreign_key('sop_param_value_sop_fkey', 'sop_param_value', 'sop', ['sop'], ['name'])
    op.drop_constraint(None, 'sop_param', schema='public', type_='foreignkey')
    op.create_foreign_key('sop_param_step_fkey', 'sop_param', 'step', ['step'], ['name'])
    op.drop_constraint(None, 'sop', schema='public', type_='foreignkey')
    op.create_foreign_key('sop_step_fkey', 'sop', 'step', ['step'], ['name'])
    op.drop_constraint(None, 'seed_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'seed_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'seed_in', schema='public', type_='foreignkey')
    op.create_foreign_key('seed_in_donor_fkey', 'seed_in', 'donor', ['donor'], ['id'])
    op.create_foreign_key('seed_in_plate_fkey', 'seed_in', 'plate', ['plate'], ['id'])
    op.create_foreign_key('seed_in_request_fkey', 'seed_in', 'request', ['request'], ['id'])
    op.drop_constraint(None, 'result_reason', schema='public', type_='foreignkey')
    op.create_foreign_key('result_reason_step_fkey', 'result_reason', 'step', ['step'], ['name'])
    op.drop_constraint(None, 'result', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'result', schema='public', type_='foreignkey')
    op.create_foreign_key('result_result_fkey', 'result', 'result_reason', ['result'], ['name'])
    op.create_foreign_key('result_id_fkey', 'result', 'action', ['id'], ['id'])
    op.drop_constraint(None, 'request', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'request', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'request', schema='public', type_='foreignkey')
    op.create_foreign_key('request_step_fkey', 'request', 'step', ['step'], ['name'])
    op.create_foreign_key('request_project_fkey', 'request', 'project', ['project'], ['name'])
    op.create_foreign_key('request_action_fkey', 'request', 'action', ['action'], ['id'])
    op.drop_constraint(None, 'plate_map_assignment', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'plate_map_assignment', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'plate_map_assignment', schema='public', type_='foreignkey')
    op.create_foreign_key('plate_map_assignment_treatment_regiment_id_fkey', 'plate_map_assignment', 'treatment_regiment', ['treatment_regiment_id'], ['id'])
    op.create_foreign_key('plate_map_assignment_treatment_id_fkey', 'plate_map_assignment', 'treatment', ['treatment_id'], ['id'])
    op.create_foreign_key('plate_map_assignment_map_group_id_fkey', 'plate_map_assignment', 'map_group', ['map_group_id'], ['id'])
    op.drop_constraint(None, 'plate_map', schema='public', type_='foreignkey')
    op.create_foreign_key('plate_map_format_name_fkey', 'plate_map', 'plate_format', ['format_name'], ['name'])
    op.drop_constraint(None, 'plate', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'plate', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'plate', schema='public', type_='foreignkey')
    op.create_foreign_key('plate_vendor_fkey', 'plate', 'vendor', ['vendor'], ['name'])
    op.create_foreign_key('plate_type_fkey', 'plate', 'plate_type', ['type'], ['name'])
    op.create_foreign_key('plate_format_fkey', 'plate', 'plate_format', ['format'], ['name'])
    op.drop_constraint(None, 'medium', schema='public', type_='unique')
    op.drop_constraint(None, 'map_group', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'map_group', schema='public', type_='foreignkey')
    op.create_foreign_key('map_group_group_type_fkey', 'map_group', 'group_type', ['group_type'], ['name'])
    op.create_foreign_key('map_group_plate_map_id_fkey', 'map_group', 'plate_map', ['plate_map_id'], ['id'])
    op.drop_constraint(None, 'image_set', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'image_set', schema='public', type_='foreignkey')
    op.create_foreign_key('image_set_plate_fkey', 'image_set', 'plate', ['plate'], ['id'])
    op.create_foreign_key('image_set_device_fkey', 'image_set', 'device', ['device'], ['name'])
    op.drop_constraint(None, 'image_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'image_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'image_in', schema='public', type_='foreignkey')
    op.create_foreign_key('image_in_request_fkey', 'image_in', 'request', ['request'], ['id'])
    op.create_foreign_key('image_in_device_fkey', 'image_in', 'device', ['device'], ['name'])
    op.create_foreign_key('image_in_plate_fkey', 'image_in', 'plate', ['plate'], ['id'])
    op.drop_constraint(None, 'image', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'image', schema='public', type_='foreignkey')
    op.create_foreign_key('image_image_set_fkey', 'image', 'image_set', ['image_set'], ['id'])
    op.create_foreign_key('image_well_pos_fkey', 'image', 'well_pos', ['well_pos'], ['id'])
    op.drop_constraint(None, 'feed_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'feed_in', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'feed_in', schema='public', type_='foreignkey')
    op.create_foreign_key('feed_in_plate_fkey', 'feed_in', 'plate', ['plate'], ['id'])
    op.create_foreign_key('feed_in_medium_fkey', 'feed_in', 'medium', ['medium'], ['name'])
    op.create_foreign_key('feed_in_request_fkey', 'feed_in', 'request', ['request'], ['id'])
    op.drop_constraint(None, 'dose', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'dose', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'dose', schema='public', type_='foreignkey')
    op.create_foreign_key('dose_concentration__unit_fkey', 'dose', 'concentration_unit', ['concentration__unit'], ['name'])
    op.create_foreign_key('dose_well_pos_id_fkey', 'dose', 'well_pos', ['well_pos_id'], ['id'])
    op.create_foreign_key('dose_map_group_id_fkey', 'dose', 'map_group', ['map_group_id'], ['id'])
    op.drop_constraint(None, 'donor', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'donor', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'donor', schema='public', type_='foreignkey')
    op.create_foreign_key('donor_race_fkey', 'donor', 'race', ['race'], ['name'])
    op.create_foreign_key('donor_vendor_fkey', 'donor', 'vendor', ['vendor'], ['name'])
    op.alter_column('donor', 'gender',
               existing_type=sa.String(),
               type_=postgresql.ENUM('male', 'female', name='gender'),
               existing_nullable=False)
    op.drop_constraint(None, 'action', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'action', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'action', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'action', schema='public', type_='foreignkey')
    op.drop_constraint(None, 'action', schema='public', type_='foreignkey')
    op.create_foreign_key('action_project_fkey', 'action', 'project', ['project'], ['name'])
    op.create_foreign_key('action_operator_fkey', 'action', 'user', ['operator'], ['name'])
    op.create_foreign_key('action_sop_fkey', 'action', 'sop', ['sop'], ['name'])
    op.create_foreign_key('action_station_fkey', 'action', 'station', ['station'], ['name'])
    op.create_foreign_key('action_step_fkey', 'action', 'step', ['step'], ['name'])
    op.create_table('well',
    sa.Column('well_id', sa.INTEGER(), server_default=sa.text("nextval('well_well_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('well_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('plate_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['plate_id'], ['plate.id'], name='well_plate_id_fkey'),
    sa.PrimaryKeyConstraint('well_id', name='well_pkey'),
    sa.UniqueConstraint('plate_id', 'well_name', name='well_plate_id_well_name_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('growth_protocol',
    sa.Column('growth_protocol_id', sa.INTEGER(), server_default=sa.text("nextval('growth_protocol_growth_protocol_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('attributes', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('growth_protocol_id', name='growth_protocol_pkey'),
    sa.UniqueConstraint('name', name='growth_protocol_name_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('olympus_image',
    sa.Column('image_id', sa.INTEGER(), server_default=sa.text("nextval('olympus_image_image_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('object_uri', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('original_image', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('imager', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('plate_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('well_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('culture_day', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('channel_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('um_per_pixel', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('size_x', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('size_y', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['plate_id'], ['plate.id'], name='olympus_image_plate_id_fkey'),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], name='olympus_image_well_id_fkey'),
    sa.PrimaryKeyConstraint('image_id', name='olympus_image_pkey'),
    sa.UniqueConstraint('object_uri', name='olympus_image_object_uri_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('plate_growth_condition',
    sa.Column('well_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('donor_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('growth_protocol_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('seeded', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['donor_id'], ['donor.id'], name='plate_growth_condition_donor_id_fkey'),
    sa.ForeignKeyConstraint(['growth_protocol_id'], ['growth_protocol.growth_protocol_id'], name='plate_growth_condition_growth_protocol_id_fkey'),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], name='plate_growth_condition_well_id_fkey'),
    sa.PrimaryKeyConstraint('well_id', name='plate_growth_condition_pkey')
    )
    op.create_table('olympus_lipid_mask',
    sa.Column('mask_id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('olympus_image_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('object_uri', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('model_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('scale', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('size_x', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('size_y', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['olympus_image_id'], ['olympus_image.image_id'], name='olympus_lipid_mask_olympus_image_id_fkey'),
    sa.PrimaryKeyConstraint('mask_id', name='olympus_lipid_mask_pkey'),
    sa.UniqueConstraint('object_uri', name='olympus_lipid_mask_object_uri_key')
    )
    op.drop_table('segmentation_mask', schema='public')
    op.drop_table('image_channel', schema='public')
    op.drop_table('supernatent_plate', schema='public')
    op.drop_table('segmentation_model', schema='public')
    op.drop_table('user_role', schema='public')
    op.drop_table('small_molecule', schema='public')
    op.drop_table('sirna', schema='public')
    op.drop_table('segmentation_goal', schema='public')
    op.drop_table('gender', schema='public')
    op.drop_table('channel', schema='public')
    # ### end Alembic commands ###
