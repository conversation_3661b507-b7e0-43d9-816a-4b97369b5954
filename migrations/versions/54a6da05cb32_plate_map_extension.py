"""Plate map extension

Revision ID: 54a6da05cb32
Revises: 62610ee98991
Create Date: 2024-04-30 12:16:22.470616

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '54a6da05cb32'
down_revision: Union[str, None] = '62610ee98991'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('plate_map_type',
        sa.Column('name', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('name'),
        schema='public'
    )
    op.add_column('plate_map', sa.Column('name', sa.String(), nullable=True))
    op.add_column('plate_map', sa.Column('description', sa.String(), nullable=True))
    op.add_column('plate_map', sa.Column('type', sa.String(), nullable=True))
    op.add_column('plate_map', sa.Column('created', sa.DateTime(), nullable=True))
    op.create_unique_constraint(None, 'plate_map', ['name'], schema='public')
    op.create_foreign_key(None, 'plate_map', 'plate_map_type', ['type'], ['name'], source_schema='public', referent_schema='public')


def downgrade() -> None:
    op.drop_column('plate_map', 'created')
    op.drop_column('plate_map', 'type')
    op.drop_column('plate_map', 'description')
    op.drop_column('plate_map', 'name')
    op.drop_table('plate_map_type', schema='public')
    # ### end Alembic commands ###
