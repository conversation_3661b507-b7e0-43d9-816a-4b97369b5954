"""Apply some not null constraints

Revision ID: 2001e701f4aa
Revises: 0d30dae79d95
Create Date: 2024-11-12 20:04:09.509682

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2001e701f4aa'
down_revision: Union[str, None] = '0d30dae79d95'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('donor', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('image_set', 'day',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('image_set', 'lighting',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('plate', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('plate_map', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('vendor', 'type',
               existing_type=sa.VARCHAR(),
               nullable=False)


def downgrade() -> None:
    op.alter_column('vendor', 'type',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('plate_map', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('plate', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('image_set', 'lighting',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('image_set', 'day',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('donor', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
