"""Add image upload step

Revision ID: 50047f8e1ac0
Revises: b22cc4651e2f
Create Date: 2024-05-12 20:19:37.841476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50047f8e1ac0'
down_revision: Union[str, None] = 'b22cc4651e2f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('upload_in',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('image_req', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['image_req'], ['request.id'], ),
        sa.ForeignKeyConstraint(['request'], ['request.id'], ),
        sa.PrimaryKeyConstraint('request')
    )
    op.create_table('upload_out',
        sa.<PERSON>umn('request', sa.Integer(), nullable=False),
        sa.Column('image_set', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], ),
        sa.ForeignKeyConstraint(['request'], ['request.id'], ),
        sa.PrimaryKeyConstraint('request')
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('upload_out')
    op.drop_table('upload_in')
    # ### end Alembic commands ###
