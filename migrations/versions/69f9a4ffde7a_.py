"""empty message

Revision ID: 69f9a4ffde7a
Revises: a327ba58c873, d3115cb1b15b
Create Date: 2024-08-08 13:18:30.763901

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '69f9a4ffde7a'
down_revision: Union[str, None] = ('a327ba58c873', 'd3115cb1b15b')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
