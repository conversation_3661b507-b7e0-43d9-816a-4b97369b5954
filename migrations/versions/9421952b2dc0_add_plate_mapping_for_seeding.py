"""Add plate mapping for seeding

Revision ID: 9421952b2dc0
Revises: 2001e701f4aa
Create Date: 2024-12-13 17:33:42.317864

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9421952b2dc0'
down_revision: Union[str, None] = '2001e701f4aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('seed_donor',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('donor', sa.Integer(), nullable=False),
        sa.<PERSON>umn('group', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['donor'], ['donor.id'], ),
        sa.ForeignKeyConstraint(['group'], ['map_group.id'], ),
        sa.ForeignKeyConstraint(['request'], ['seed_in.request'], ),
        sa.PrimaryKeyConstraint('request', 'donor')
    )


def downgrade() -> None:
    op.drop_table('seed_donor')
