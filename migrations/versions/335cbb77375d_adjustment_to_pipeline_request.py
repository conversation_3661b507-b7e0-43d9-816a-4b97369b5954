"""adjustment to pipeline_request

Revision ID: 335cbb77375d
Revises: 84a950ead8a6
Create Date: 2024-07-24 14:06:21.006007

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '335cbb77375d'
down_revision: Union[str, None] = '84a950ead8a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('result_set', sa.Column('image_pipeline_version', sa.Integer(), nullable=False))
    op.drop_constraint('result_set_image_pipeline_fkey', 'result_set', type_='foreignkey')
    op.create_foreign_key(None, 'result_set', 'pipeline_version', ['image_pipeline_version'], ['id'])
    op.drop_column('result_set', 'image_pipeline')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('result_set', sa.Column('image_pipeline', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'result_set', type_='foreignkey')
    op.create_foreign_key('result_set_image_pipeline_fkey', 'result_set', 'image_pipeline', ['image_pipeline'], ['id'])
    op.drop_column('result_set', 'image_pipeline_version')
    # ### end Alembic commands ###
