"""added channel index to image_channel table

Revision ID: 6f49a63f167c
Revises: c3039cfb4060
Create Date: 2024-05-16 19:49:22.339901

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6f49a63f167c'
down_revision: Union[str, None] = 'c3039cfb4060'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('original_cellsens_image',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_id', sa.Integer(), nullable=False),
    sa.Column('original_file_uri', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['image_id'], ['image.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('original_file_uri')
    )
    op.add_column('image_channel', sa.Column('channel_index', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('image_channel', 'channel_index')
    op.drop_table('original_cellsens_image')
    # ### end Alembic commands ###
