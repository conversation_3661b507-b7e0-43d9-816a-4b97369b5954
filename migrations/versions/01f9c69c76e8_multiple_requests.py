"""Multiple requests

Revision ID: 01f9c69c76e8
Revises: e07488cf7e5d
Create Date: 2024-03-21 15:50:10.329930

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import orm
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.engine.reflection import Inspector

# revision identifiers, used by Alembic.
revision: str = '01f9c69c76e8'
down_revision: Union[str, None] = 'e07488cf7e5d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# https://stackoverflow.com/questions/24612395/how-do-i-execute-inserts-and-updates-in-an-alembic-upgrade-script
Base = declarative_base()

class Project(Base):
    __tablename__ = 'project'
    name = sa.Column(sa.String, primary_key=True)
    description = sa.Column('description', sa.String, nullable=False)

def upgrade() -> None:
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    inspector = Inspector.from_engine(bind)
    tables = inspector.get_table_names()
    
    # create the project table
    if "project" not in tables:
        Project.__table__.create(bind)
        session.add(Project(name="production", description="All requests that matter go here"))
        session.add(Project(name="backfill", description="Requests used in the backfill process"))
        session.commit()
    # This is from autogenerate and verified, but added the "server_default"
    op.drop_constraint('action_id_fkey', 'action', type_='foreignkey')
    op.add_column('request', sa.Column('project', sa.String(), nullable=False, server_default='production'))
    op.add_column('request', sa.Column('action', sa.Integer(), nullable=True))
    op.add_column('action', sa.Column('project', sa.String(), nullable=False, server_default='seed'))
    op.add_column('action', sa.Column('step', sa.String(), nullable=False, server_default='seed'))
    op.create_foreign_key(None, 'request', 'action', ['action'], ['id'])
    op.create_foreign_key(None, 'request', 'project', ['project'], ['name'])
    op.create_foreign_key(None, 'action', 'project', ['project'], ['name'])
    op.create_foreign_key(None, 'action', 'step', ['step'], ['name'])
    op.add_column('image_set', sa.Column('bt_key', sa.String(), nullable=False))
    

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'request', type_='foreignkey')
    op.drop_constraint(None, 'request', type_='foreignkey')
    op.drop_column('request', 'action')
    op.drop_column('request', 'project')
    op.create_foreign_key('action_id_fkey', 'action', 'request', ['id'], ['id'])
    # ### end Alembic commands ###
