"""assay workflow

Revision ID: 92bb38996d30
Revises: dba0331fbcf1
Create Date: 2024-04-23 14:14:13.616473

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '92bb38996d30'
down_revision: Union[str, None] = 'dba0331fbcf1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assay_in',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('plate', sa.Integer(), nullable=False),
        sa.Column('quadrant', sa.String(), nullable=False),
        sa.Column('controls', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['controls'], ['public.plate_map.id'], ),
        sa.ForeignKeyConstraint(['plate'], ['public.plate.id'], ),
        sa.ForeignKeyConstraint(['request'], ['public.request.id'], ),
        sa.PrimaryKeyConstraint('request'),
        schema='public'
    )
    op.create_table('dilute_in',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('plate', sa.Integer(), nullable=False),
        sa.Column('dilution', sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(['plate'], ['public.plate.id'], ),
        sa.ForeignKeyConstraint(['request'], ['public.request.id'], ),
        sa.PrimaryKeyConstraint('request'),
        schema='public'
    )
    op.create_table('extract_in',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('plate', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['plate'], ['public.plate.id'], ),
        sa.ForeignKeyConstraint(['request'], ['public.request.id'], ),
        sa.PrimaryKeyConstraint('request'),
        schema='public'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('extract_in', schema='public')
    op.drop_table('dilute_in', schema='public')
    op.drop_table('assay_in', schema='public')
    # ### end Alembic commands ###
