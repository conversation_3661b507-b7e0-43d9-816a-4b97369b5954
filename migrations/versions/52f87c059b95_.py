"""empty message

Revision ID: 52f87c059b95
Revises: c6fccac4e86d, 470a58df39dc
Create Date: 2024-08-05 11:58:10.788547

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '52f87c059b95'
down_revision: Union[str, None] = ('c6fccac4e86d', '470a58df39dc')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
