"""added device to image_pipeline

Revision ID: 84a950ead8a6
Revises: 9136aaf282b6
Create Date: 2024-07-18 12:14:02.552770

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '84a950ead8a6'
down_revision: Union[str, None] = '9136aaf282b6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('image_pipeline', sa.Column('device', sa.String(), nullable=True))
    op.execute("UPDATE image_pipeline SET device='olympus'")
    op.alter_column("image_pipeline", "device", nullable=False)
    op.create_foreign_key(None, 'image_pipeline', 'device', ['device'], ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'image_pipeline', type_='foreignkey')
    op.drop_column('image_pipeline', 'device')
    # ### end Alembic commands ###
