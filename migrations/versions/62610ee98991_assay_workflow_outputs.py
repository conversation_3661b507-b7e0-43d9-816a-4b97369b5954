"""assay workflow outputs

Revision ID: 62610ee98991
Revises: 92bb38996d30
Create Date: 2024-04-25 15:36:57.986486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '62610ee98991'
down_revision: Union[str, None] = '92bb38996d30'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    #
    op.create_table('dilution',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('factor', sa.Float(), nullable=False),
        sa.Column('source', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['public.plate.id'], ),
        sa.ForeignKeyConstraint(['source'], ['public.plate.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='public'
    )
    op.create_table('supernatant',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['public.plate.id'], ),
        sa.ForeignKeyConstraint(['source'], ['public.plate.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='public'
    )
    op.drop_table('supernatent_plate')
    op.add_column('plate', sa.Column('created', sa.DateTime(), nullable=True))
    #


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('plate', 'created')
    
    op.create_table('supernatent_plate',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column('barcode', sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column('created', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column('source_plate', sa.INTEGER(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(['source_plate'], ['plate.id'], name='supernatent_plate_source_plate_fkey'),
        sa.PrimaryKeyConstraint('id', name='supernatent_plate_pkey'),
        sa.UniqueConstraint('barcode', name='supernatent_plate_barcode_key'),
        sa.UniqueConstraint('name', name='supernatent_plate_name_key')
    )
    
    op.drop_table('supernatant', schema='public')
    op.drop_table('dilution', schema='public')
    #
