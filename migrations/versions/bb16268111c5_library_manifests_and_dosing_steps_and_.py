"""library manifests and dosing steps and action tags

Revision ID: bb16268111c5
Revises: ecd3b3f10f68
Create Date: 2024-05-08 17:29:48.468196

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bb16268111c5'
down_revision: Union[str, None] = 'ecd3b3f10f68'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('library_manifest',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('vendor', sa.String(), nullable=False),
    sa.Column('file_uri', sa.String(), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['vendor'], ['vendor.name'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('file_uri')
    )
    op.create_table('library_plate',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('library_manifest', sa.Integer(), nullable=False),
    sa.Column('manifest_plate_number', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['library_manifest'], ['library_manifest.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('library_manifest', 'manifest_plate_number')
    )
    op.create_table('action_tag',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('action', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('payload', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['action'], ['action.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('library_mapping',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('library_plate', sa.Integer(), nullable=False),
    sa.Column('treatment', sa.Integer(), nullable=False),
    sa.Column('well_position', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['library_plate'], ['library_plate.id'], ),
    sa.ForeignKeyConstraint(['treatment'], ['treatment.id'], ),
    sa.ForeignKeyConstraint(['well_position'], ['well_pos.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dose_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('plate', sa.Integer(), nullable=False),
    sa.Column('treatment_batch', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['plate'], ['plate.id'], ),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.ForeignKeyConstraint(['treatment_batch'], ['treatment_regiment.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    op.create_table('dose_response_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('plate', sa.Integer(), nullable=False),
    sa.Column('platemap', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['plate'], ['plate.id'], ),
    sa.ForeignKeyConstraint(['platemap'], ['plate_map.id'], ),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    op.create_table('screen_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('plate', sa.Integer(), nullable=False),
    sa.Column('quadrant', sa.String(), nullable=False),
    sa.Column('platemap', sa.Integer(), nullable=False),
    sa.Column('library_plate', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['library_plate'], ['library_plate.id'], ),
    sa.ForeignKeyConstraint(['plate'], ['plate.id'], ),
    sa.ForeignKeyConstraint(['platemap'], ['plate_map.id'], ),
    sa.ForeignKeyConstraint(['quadrant'], ['quadrant.name'], ),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    op.drop_table('sirna')
    op.drop_table('small_molecule')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('small_molecule',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('smiles', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('barcode', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='small_molecule_pkey'),
    sa.UniqueConstraint('barcode', name='small_molecule_barcode_key'),
    sa.UniqueConstraint('smiles', name='small_molecule_smiles_key')
    )
    op.create_table('sirna',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('gene', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('barcode', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='sirna_pkey'),
    sa.UniqueConstraint('barcode', name='sirna_barcode_key')
    )
    op.drop_table('screen_in')
    op.drop_table('dose_response_in')
    op.drop_table('dose_in')
    op.drop_table('library_mapping')
    op.drop_table('action_tag')
    op.drop_table('library_plate')
    op.drop_table('library_manifest')
    # ### end Alembic commands ###
