"""initial revision

Revision ID: 6ceeeb438064
Revises: 
Create Date: 2024-01-29 16:01:44.591659

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6ceeeb438064'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('donor',
    sa.Column('donor_id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=False),
    sa.Column('biopsy_site', sa.String(), nullable=False),
    sa.Column('documentation', sa.String(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=False),
    sa.Column('gender', sa.Enum('male', 'female', name='gender'), nullable=False),
    sa.Column('age', sa.Integer(), nullable=False),
    sa.Column('bmi', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('donor_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('experiment',
    sa.Column('experiment_id', sa.Integer(), nullable=False),
    sa.Column('experiment_type', sa.Enum('dose_response', 'screening', name='experimenttype'), nullable=False),
    sa.PrimaryKeyConstraint('experiment_id')
    )
    op.create_table('growth_protocol',
    sa.Column('growth_protocol_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('attributes', sa.JSON(), nullable=False),
    sa.PrimaryKeyConstraint('growth_protocol_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('reagent',
    sa.Column('reagent_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('mcl_id', sa.String(), nullable=True),
    sa.Column('reagent_type', sa.Enum('small_molecule', 'sirna', name='reagenttype'), nullable=False),
    sa.PrimaryKeyConstraint('reagent_id'),
    sa.UniqueConstraint('mcl_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('plate',
    sa.Column('plate_id', sa.Integer(), nullable=False),
    sa.Column('plate_name', sa.String(), nullable=False),
    sa.Column('number_wells', sa.Integer(), nullable=True),
    sa.Column('plate_type', sa.String(), nullable=False),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('experiment_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['experiment_id'], ['experiment.experiment_id'], ),
    sa.PrimaryKeyConstraint('plate_id'),
    sa.UniqueConstraint('plate_name')
    )
    op.create_table('well',
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('well_name', sa.String(), nullable=False),
    sa.Column('plate_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['plate_id'], ['plate.plate_id'], ),
    sa.PrimaryKeyConstraint('well_id'),
    sa.UniqueConstraint('plate_id', 'well_name')
    )
    op.create_table('dose',
    sa.Column('dose_id', sa.Integer(), nullable=False),
    sa.Column('experiment_id', sa.Integer(), nullable=False),
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('reagent_id', sa.Integer(), nullable=False),
    sa.Column('concentration', sa.Float(), nullable=False),
    sa.Column('concentration_unit', sa.Enum('uM', 'nM', name='concentrationunit'), nullable=False),
    sa.Column('group_type', sa.Enum('positive', 'negative', 'test', name='group'), nullable=False),
    sa.Column('added', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['experiment_id'], ['experiment.experiment_id'], ),
    sa.ForeignKeyConstraint(['reagent_id'], ['reagent.reagent_id'], ),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], ),
    sa.PrimaryKeyConstraint('dose_id')
    )
    op.create_table('olympus_image',
    sa.Column('image_id', sa.Integer(), nullable=False),
    sa.Column('object_uri', sa.String(), nullable=False),
    sa.Column('original_image', sa.String(), nullable=False),
    sa.Column('imager', sa.String(), nullable=False),
    sa.Column('plate_id', sa.Integer(), nullable=False),
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('culture_day', sa.Integer(), nullable=False),
    sa.Column('channel_name', sa.String(), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=False),
    sa.Column('um_per_pixel', sa.Float(), nullable=False),
    sa.Column('size_x', sa.Integer(), nullable=False),
    sa.Column('size_y', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['plate_id'], ['plate.plate_id'], ),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], ),
    sa.PrimaryKeyConstraint('image_id'),
    sa.UniqueConstraint('object_uri')
    )
    op.create_table('plate_growth_condition',
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('donor_id', sa.Integer(), nullable=False),
    sa.Column('growth_protocol_id', sa.Integer(), nullable=False),
    sa.Column('seeded', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['donor_id'], ['donor.donor_id'], ),
    sa.ForeignKeyConstraint(['growth_protocol_id'], ['growth_protocol.growth_protocol_id'], ),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], ),
    sa.PrimaryKeyConstraint('well_id')
    )
    op.create_table('olympus_lipid_mask',
    sa.Column('mask_id', sa.Integer(), nullable=False),
    sa.Column('olympus_image_id', sa.Integer(), nullable=False),
    sa.Column('object_uri', sa.String(), nullable=False),
    sa.Column('model_name', sa.String(), nullable=False),
    sa.Column('scale', sa.Float(), nullable=False),
    sa.Column('size_x', sa.Integer(), nullable=False),
    sa.Column('size_y', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['olympus_image_id'], ['olympus_image.image_id'], ),
    sa.PrimaryKeyConstraint('mask_id'),
    sa.UniqueConstraint('object_uri')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('olympus_lipid_mask')
    op.drop_table('plate_growth_condition')
    op.drop_table('olympus_image')
    op.drop_table('dose')
    op.drop_table('well')
    op.drop_table('plate')
    op.drop_table('reagent')
    op.drop_table('growth_protocol')
    op.drop_table('experiment')
    op.drop_table('donor')
    # ### end Alembic commands ###
