"""added boolean to mark a pipeline as active or inactive

Revision ID: a327ba58c873
Revises: 037f4f450f6b
Create Date: 2024-08-08 10:50:31.426761

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a327ba58c873'
down_revision: Union[str, None] = '037f4f450f6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('image_pipeline', sa.Column('active', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('image_pipeline', 'active')
    # ### end Alembic commands ###
