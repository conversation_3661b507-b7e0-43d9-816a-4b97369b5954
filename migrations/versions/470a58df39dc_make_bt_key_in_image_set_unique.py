"""make bt key in image_set unique

Revision ID: 470a58df39dc
Revises: 0344e4d1eeeb
Create Date: 2024-07-31 13:16:01.885597

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '470a58df39dc'
down_revision: Union[str, None] = '0344e4d1eeeb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'image_set', ['bt_key'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'image_set', type_='unique')
    # ### end Alembic commands ###
