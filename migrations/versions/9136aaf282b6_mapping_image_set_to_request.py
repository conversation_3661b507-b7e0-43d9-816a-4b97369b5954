"""mapping image_set to request

Revision ID: 9136aaf282b6
Revises: 2240bd062551
Create Date: 2024-07-17 16:55:31.832039

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9136aaf282b6'
down_revision: Union[str, None] = '2240bd062551'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pipeline_status',
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('name')
    )
    op.add_column('pipeline_request', sa.Column('pipeline_version', sa.Integer(), nullable=False))
    op.add_column('pipeline_request', sa.Column('image_set', sa.Integer(), nullable=False))
    op.add_column('pipeline_request', sa.Column('status', sa.String(), nullable=False))
    op.drop_constraint('pipeline_request_image_pipeline_fkey', 'pipeline_request', type_='foreignkey')
    op.create_foreign_key(None, 'pipeline_request', 'image_set', ['image_set'], ['id'])
    op.create_foreign_key(None, 'pipeline_request', 'pipeline_status', ['status'], ['name'])
    op.create_foreign_key(None, 'pipeline_request', 'pipeline_version', ['pipeline_version'], ['id'])
    op.drop_column('pipeline_request', 'check_url')
    op.drop_column('pipeline_request', 'image_pipeline')
    op.drop_constraint('result_set_image_set_fkey', 'result_set', type_='foreignkey')
    op.drop_column('result_set', 'image_set')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('result_set', sa.Column('image_set', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key('result_set_image_set_fkey', 'result_set', 'image_set', ['image_set'], ['id'])
    op.add_column('pipeline_request', sa.Column('image_pipeline', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('pipeline_request', sa.Column('check_url', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'pipeline_request', type_='foreignkey')
    op.drop_constraint(None, 'pipeline_request', type_='foreignkey')
    op.drop_constraint(None, 'pipeline_request', type_='foreignkey')
    op.create_foreign_key('pipeline_request_image_pipeline_fkey', 'pipeline_request', 'image_pipeline', ['image_pipeline'], ['id'])
    op.drop_column('pipeline_request', 'status')
    op.drop_column('pipeline_request', 'image_set')
    op.drop_column('pipeline_request', 'pipeline_version')
    op.drop_table('pipeline_status')
    # ### end Alembic commands ###
