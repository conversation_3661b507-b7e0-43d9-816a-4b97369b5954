"""Add image-plate relationship

Revision ID: 0736bc2a7667
Revises: f37385415359
Create Date: 2024-11-03 17:10:43.903565

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0736bc2a7667'
down_revision: Union[str, None] = 'f37385415359'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('image_set', 'plate', new_column_name='plate_id')
    op.drop_constraint('image_set_plate_fkey', 'image_set', type_='foreignkey')
    op.create_foreign_key(None, 'image_set', 'plate', ['plate_id'], ['id'])


def downgrade() -> None:
    op.alter_column('image_set', 'plate_id', new_column_name='plate')
    op.drop_constraint(None, 'image_set', type_='foreignkey')
    op.create_foreign_key('image_set_plate_fkey', 'image_set', 'plate', ['plate'], ['id'])
