"""added pipelinein and pipeline out in orm

Revision ID: 037f4f450f6b
Revises: 52f87c059b95
Create Date: 2024-08-05 12:01:49.378439

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '037f4f450f6b'
down_revision: Union[str, None] = '52f87c059b95'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pipeline_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('image_set', sa.Integer(), nullable=False),
    sa.Column('pipeline_version', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], ),
    sa.ForeignKeyConstraint(['pipeline_version'], ['pipeline_version.id'], ),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    op.create_table('pipeline_out',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('result_set', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.ForeignKeyConstraint(['result_set'], ['result_set.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    op.drop_table('segment_in')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('segment_in',
    sa.Column('request', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('image_set', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('pipeline_version', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], name='segment_in_image_set_fkey'),
    sa.ForeignKeyConstraint(['pipeline_version'], ['pipeline_version.id'], name='segment_in_pipeline_version_fkey'),
    sa.ForeignKeyConstraint(['request'], ['request.id'], name='segment_in_request_fkey'),
    sa.PrimaryKeyConstraint('request', name='segment_in_pkey')
    )
    op.drop_table('pipeline_out')
    op.drop_table('pipeline_in')
    # ### end Alembic commands ###
