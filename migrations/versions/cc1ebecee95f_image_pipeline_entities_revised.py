"""image pipeline entities revised

Revision ID: cc1ebecee95f
Revises: a853e121ba66
Create Date: 2024-07-15 11:09:43.673856

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cc1ebecee95f'
down_revision: Union[str, None] = 'a853e121ba66'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('image_pipeline',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('goal', sa.String(), nullable=False),
    sa.Column('scale_factor', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['goal'], ['segmentation_goal.name'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('pipeline_version',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_pipeline', sa.Integer(), nullable=False),
    sa.Column('version_number', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('mask_set',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('image_set', sa.Integer(), nullable=False),
    sa.Column('image_pipeline', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image_pipeline'], ['image_pipeline.id'], ),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_constraint('segmentation_mask_model_fkey', 'segmentation_mask', type_='foreignkey')
    op.drop_column('segmentation_mask', 'model')
    op.drop_table('segmentation_model')
    op.add_column('segmentation_mask', sa.Column('mask_set', sa.Integer(), nullable=False))
    
    op.create_foreign_key(None, 'segmentation_mask', 'mask_set', ['mask_set'], ['id'])
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('segmentation_mask', sa.Column('model', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'segmentation_mask', type_='foreignkey')
    op.create_foreign_key('segmentation_mask_model_fkey', 'segmentation_mask', 'segmentation_model', ['model'], ['id'])
    op.drop_column('segmentation_mask', 'mask_set')
    op.create_table('segmentation_model',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('goal', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('scale_factor', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['goal'], ['segmentation_goal.name'], name='segmentation_model_goal_fkey'),
    sa.PrimaryKeyConstraint('id', name='segmentation_model_pkey'),
    sa.UniqueConstraint('name', name='segmentation_model_name_key')
    )
    op.drop_table('mask_set')
    op.drop_table('pipeline_version')
    op.drop_table('image_pipeline')
    # ### end Alembic commands ###
