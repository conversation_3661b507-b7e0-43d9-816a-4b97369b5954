"""changes to imaging tables

Revision ID: c3039cfb4060
Revises: 50047f8e1ac0
Create Date: 2024-05-16 13:47:36.882335

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c3039cfb4060'
down_revision: Union[str, None] = '50047f8e1ac0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('image_channel_channel_id_fkey', 'image_channel', type_='foreignkey')
    op.drop_column('channel', 'wavelength')
    op.drop_column('channel', 'id')
    op.create_primary_key('pk_channel', 'channel', ['name'])
    op.add_column('image', sa.Column('size_x', sa.Integer(), nullable=False))
    op.add_column('image', sa.Column('size_y', sa.Integer(), nullable=False))
    op.add_column('image_channel', sa.Column('channel_name', sa.String(), nullable=True))
    op.create_foreign_key(None, 'image_channel', 'channel', ['channel_name'], ['name'])
    op.drop_column('image_channel', 'channel_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('image_channel', sa.Column('channel_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'image_channel', type_='foreignkey')
    op.create_foreign_key('image_channel_channel_id_fkey', 'image_channel', 'channel', ['channel_id'], ['id'])
    op.drop_column('image_channel', 'channel_name')
    op.drop_column('image', 'size_y')
    op.drop_column('image', 'size_x')
    op.add_column('channel', sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('channel_id_seq'::regclass)"), autoincrement=True, nullable=False))
    op.add_column('channel', sa.Column('wavelength', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
