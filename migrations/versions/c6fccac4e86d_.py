"""empty message

Revision ID: c6fccac4e86d
Revises: 0344e4d1eeeb, 9fad89fcc1b6
Create Date: 2024-07-30 18:46:06.564738

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c6fccac4e86d'
down_revision: Union[str, None] = ('0344e4d1eeeb', '9fad89fcc1b6')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
