"""Assay output link tables

Revision ID: b22cc4651e2f
Revises: 69e725b1bf05
Create Date: 2024-05-08 20:46:45.014575

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b22cc4651e2f'
down_revision: Union[str, None] = '69e725b1bf05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('dilute_out',
        sa.Column('request', sa.Integer(), nullable=False),
        sa.Column('plate', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['plate'], ['dilution.id'], ),
        sa.ForeignKeyConstraint(['request'], ['request.id'], ),
        sa.PrimaryKeyConstraint('request')
    )
    op.create_table('extract_out',
        sa.<PERSON>umn('request', sa.Integer(), nullable=False),
        sa.<PERSON>umn('plate', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['plate'], ['supernatant.id'], ),
        sa.ForeignKeyConstraint(['request'], ['request.id'], ),
        sa.PrimaryKeyConstraint('request')
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('extract_out')
    op.drop_table('dilute_out')
    # ### end Alembic commands ###
