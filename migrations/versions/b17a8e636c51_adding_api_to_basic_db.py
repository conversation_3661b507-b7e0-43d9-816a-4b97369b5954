"""adding api to basic DB

Revision ID: b17a8e636c51
Revises: 6ceeeb438064
Create Date: 2024-01-30 23:25:17.611578

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b17a8e636c51'
down_revision: Union[str, None] = '6ceeeb438064'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('donor',
    sa.Column('donor_id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=False),
    sa.Column('biopsy_site', sa.String(), nullable=False),
    sa.Column('documentation', sa.String(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=False),
    sa.Column('gender', sa.Enum('male', 'female', name='gender'), nullable=False),
    sa.Column('age', sa.Integer(), nullable=False),
    sa.Column('bmi', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('donor_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('experiment',
    sa.Column('experiment_id', sa.Integer(), nullable=False),
    sa.Column('experiment_type', sa.Enum('dose_response', 'screening', name='experimenttype'), nullable=False),
    sa.PrimaryKeyConstraint('experiment_id')
    )
    op.create_table('growth_protocol',
    sa.Column('growth_protocol_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('attributes', sa.JSON(), nullable=False),
    sa.PrimaryKeyConstraint('growth_protocol_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('reagent',
    sa.Column('reagent_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('mcl_id', sa.String(), nullable=True),
    sa.Column('reagent_type', sa.Enum('small_molecule', 'sirna', name='reagenttype'), nullable=False),
    sa.PrimaryKeyConstraint('reagent_id'),
    sa.UniqueConstraint('mcl_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('dose',
    sa.Column('dose_id', sa.Integer(), nullable=False),
    sa.Column('experiment_id', sa.Integer(), nullable=False),
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('reagent_id', sa.Integer(), nullable=False),
    sa.Column('concentration', sa.Float(), nullable=False),
    sa.Column('concentration_unit', sa.Enum('uM', 'nM', name='concentrationunit'), nullable=False),
    sa.Column('group_type', sa.Enum('positive', 'negative', 'test', name='group'), nullable=False),
    sa.Column('added', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['experiment_id'], ['experiment.experiment_id'], ),
    sa.ForeignKeyConstraint(['reagent_id'], ['reagent.reagent_id'], ),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], ),
    sa.PrimaryKeyConstraint('dose_id')
    )
    op.create_table('plate_growth_condition',
    sa.Column('well_id', sa.Integer(), nullable=False),
    sa.Column('donor_id', sa.Integer(), nullable=False),
    sa.Column('growth_protocol_id', sa.Integer(), nullable=False),
    sa.Column('seeded', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['donor_id'], ['donor.donor_id'], ),
    sa.ForeignKeyConstraint(['growth_protocol_id'], ['growth_protocol.growth_protocol_id'], ),
    sa.ForeignKeyConstraint(['well_id'], ['well.well_id'], ),
    sa.PrimaryKeyConstraint('well_id')
    )
    op.alter_column('olympus_image', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.add_column('plate', sa.Column('vendor', sa.String(), nullable=True))
    op.add_column('plate', sa.Column('experiment_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'plate', 'experiment', ['experiment_id'], ['experiment_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'plate', type_='foreignkey')
    op.drop_column('plate', 'experiment_id')
    op.drop_column('plate', 'vendor')
    op.alter_column('olympus_image', 'created',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.drop_table('plate_growth_condition')
    op.drop_table('dose')
    op.drop_table('reagent')
    op.drop_table('growth_protocol')
    op.drop_table('experiment')
    op.drop_table('donor')
    # ### end Alembic commands ###
