"""Plate quadrants

Revision ID: ecd3b3f10f68
Revises: 54a6da05cb32
Create Date: 2024-05-01 13:06:49.400123

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ecd3b3f10f68'
down_revision: Union[str, None] = '54a6da05cb32'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('quadrant',
        sa.Column('name', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('name')
    )
    op.add_column('assay_in', sa.Column('platemap', sa.Integer(), nullable=False))
    op.drop_constraint('assay_in_controls_fkey', 'assay_in', type_='foreignkey')
    op.create_foreign_key(None, 'assay_in', 'plate_map', ['platemap'], ['id'])
    op.create_foreign_key(None, 'assay_in', 'quadrant', ['quadrant'], ['name'])
    op.drop_column('assay_in', 'controls')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assay_in', sa.Column('controls', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'assay_in', type_='foreignkey')
    op.drop_constraint(None, 'assay_in', type_='foreignkey')
    op.create_foreign_key('assay_in_controls_fkey', 'assay_in', 'plate_map', ['controls'], ['id'])
    op.drop_column('assay_in', 'platemap')
    op.drop_table('quadrant')
    # ### end Alembic commands ###
