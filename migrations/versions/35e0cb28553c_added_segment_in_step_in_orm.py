"""added segment in step in orm

Revision ID: 35e0cb28553c
Revises: cc1ebecee95f
Create Date: 2024-07-15 12:42:44.686515

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '35e0cb28553c'
down_revision: Union[str, None] = 'cc1ebecee95f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('segment_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('image_set', sa.Integer(), nullable=False),
    sa.Column('pipeline_version', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['image_set'], ['image_set.id'], ),
    sa.ForeignKeyConstraint(['pipeline_version'], ['pipeline_version.id'], ),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('segment_in')
    # ### end Alembic commands ###
