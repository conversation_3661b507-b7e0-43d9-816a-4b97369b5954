"""empty message

Revision ID: d3115cb1b15b
Revises: 470a58df39dc, c6fccac4e86d
Create Date: 2024-08-02 12:28:12.171151

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd3115cb1b15b'
down_revision: Union[str, None] = ('470a58df39dc', 'c6fccac4e86d')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
