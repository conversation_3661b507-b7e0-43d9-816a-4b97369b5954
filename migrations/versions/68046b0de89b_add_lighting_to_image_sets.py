"""Add lighting to image sets

Revision ID: 68046b0de89b
Revises: 0736bc2a7667
Create Date: 2024-11-04 13:56:00.499027

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '68046b0de89b'
down_revision: Union[str, None] = '0736bc2a7667'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('lighting',
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('name')
    )
    op.add_column('image_set', sa.Column('lighting', sa.String(), nullable=True))
    op.create_foreign_key(None, 'image_set', 'lighting', ['lighting'], ['name'])


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'image_set', type_='foreignkey')
    op.drop_column('image_set', 'lighting')
    op.drop_table('lighting')
    # ### end Alembic commands ###
